#!/usr/bin/env python3
"""
Quick Test Script for D³AFD Multi-Algorithm Framework
Test different distillation algorithms with minimal setup
"""

import os
import sys
import argparse
import logging
import yaml
import torch
from pathlib import Path

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def setup_quick_test_config(algorithm):
    """Create a minimal configuration for quick testing"""
    config = {
        'experiment': {
            'name': f'd3afd_quick_test_{algorithm}',
            'device': 'cuda' if torch.cuda.is_available() else 'cpu',
            'seed': 42,
            'output_dir': f'outputs/quick_test_{algorithm}',
            'log_level': 'INFO'
        },
        'data': {
            'dataset_name': 'amazon_reviews',
            'data_dir': 'data/amazon_reviews',
            'domains': ['Electronics', 'Books'],  # Reduced for quick test
            'max_length': 256,  # Reduced for speed
            'train_split': 0.8,
            'val_split': 0.1,
            'test_split': 0.1,
            'num_workers': 2
        },
        'model': {
            'model_name': 'distilbert-base-uncased',
            'num_labels': 3,
            'hidden_size': 768,
            'dropout': 0.1,
            'freeze_backbone': False
        },
        'training': {
            'distillation_algorithm': algorithm,
            'num_rounds': 3,  # Reduced for quick test
            'local_epochs': 2,  # Reduced for quick test
            'distillation_epochs': 2,  # Reduced for quick test
            'batch_size': 8,  # Reduced for quick test
            'distillation_batch_size': 16,
            'learning_rate': 2e-5,
            'distillation_lr': 1e-4,
            'weight_decay': 0.01,
            'optimizer_type': 'adamw',
            'pseudo_samples_per_domain': 100,  # Reduced for quick test
            'mixed_domain_samples': 50,  # Added for quick test
            'cache_refresh_interval': 2,
            'contrastive_weight': 0.1,
            'domain_weight': 0.05
        },
        'federated': {
            'num_clients': 2,  # Reduced for quick test
            'clients_per_round': 2,
            'client_selection': 'all',
            'data_distribution': 'domain_specific',
            'alpha': 0.5
        },
        'evaluation': {
            'metrics': ['accuracy', 'f1_macro'],
            'eval_frequency': 1,  # Evaluate every round for quick test
            'save_best_model': True,
            'early_stopping_patience': 3
        },
        'memory': {
            'enable_memory_management': True,
            'max_memory_usage': 0.7,
            'cleanup_frequency': 1,
            'force_cleanup_epochs': True
        },
        'logging': {
            'log_frequency': 5,
            'save_logs': True,
            'tensorboard': False,  # Disabled for quick test
            'wandb': {'enabled': False}
        }
    }
    
    # Algorithm-specific configurations
    if algorithm == 'dkdm':
        config['dkdm'] = {
            'num_timesteps': 100,  # Reduced for quick test
            'batch_ratio': 0.2,  # Reduced for quick test
            'cache_dir': 'cache/dkdm_quick',
            'distill_weight': 1.0
        }
    elif algorithm == 'diffdfkd':
        config['diffdfkd'] = {
            't5_model': 't5-small',  # Smaller model for quick test
            'guided_scale': 7.0,
            'inference_steps': 20,  # Reduced for quick test
            'oh_weight': 1.0,
            'bn_weight': 0.1,
            'adv_weight': 1.0
        }
        config['text_generation'] = {
            'max_length': 100,  # Reduced for quick test
            'temperature': 0.8,
            'do_sample': True,
            'num_return_sequences': 1
        }
    elif algorithm == 'diffkd':
        config['diffkd'] = {
            'inference_steps': 3,  # Reduced for quick test
            'num_train_timesteps': 100,  # Reduced for quick test
            'use_ae': True,
            'ae_channels': 256,  # Reduced for quick test
            'tau': 1.0
        }
    
    return config

def run_quick_test(algorithm):
    """Run a quick test with the specified algorithm"""
    print(f"\n{'='*60}")
    print(f"Quick Test: {algorithm.upper()} Algorithm")
    print(f"{'='*60}")
    
    # Create config
    config_dict = setup_quick_test_config(algorithm)
    
    # Save temporary config file
    temp_config_path = f"temp_config_{algorithm}.yaml"
    with open(temp_config_path, 'w') as f:
        yaml.dump(config_dict, f, default_flow_style=False)
    
    try:
        # Import and run
        from train_d3afd_multi_algorithm import run_experiment
        from src.utils.config import Config
        from src.utils.logger import setup_logger
        
        # Setup
        config = Config(config_dict)
        logger = setup_logger(config.experiment.log_level)
        
        # Run experiment
        print(f"Starting quick test with {algorithm}...")
        final_metrics = run_experiment(config, algorithm)
        
        # Print results
        print(f"\n{algorithm.upper()} Quick Test Results:")
        print("-" * 40)
        for metric, value in final_metrics.items():
            if isinstance(value, (int, float)):
                print(f"{metric:15}: {value:.4f}")
            else:
                print(f"{metric:15}: {value}")
        
        return final_metrics
        
    except Exception as e:
        print(f"Error in {algorithm} quick test: {e}")
        return None
    
    finally:
        # Clean up temporary config
        if os.path.exists(temp_config_path):
            os.remove(temp_config_path)

def main():
    parser = argparse.ArgumentParser(description="Quick Test D³AFD Algorithms")
    parser.add_argument("--algorithm", type=str, 
                       choices=["dkd", "dkdm", "diffdfkd", "diffkd"],
                       help="Algorithm to test")
    parser.add_argument("--all", action="store_true",
                       help="Test all algorithms")
    parser.add_argument("--device", type=str, default="auto",
                       choices=["auto", "cuda", "cpu"],
                       help="Device to use")
    
    args = parser.parse_args()
    
    # Determine device
    if args.device == "auto":
        device = "cuda" if torch.cuda.is_available() else "cpu"
    else:
        device = args.device
    
    print(f"Using device: {device}")
    
    if args.all:
        # Test all algorithms
        algorithms = ["dkd", "dkdm", "diffdfkd", "diffkd"]
        results = {}
        
        for algorithm in algorithms:
            result = run_quick_test(algorithm)
            results[algorithm] = result
        
        # Summary
        print(f"\n{'='*60}")
        print("QUICK TEST SUMMARY")
        print(f"{'='*60}")
        
        for algorithm, metrics in results.items():
            if metrics:
                accuracy = metrics.get('accuracy', 'N/A')
                f1_macro = metrics.get('f1_macro', 'N/A')
                print(f"{algorithm.upper():10} - Accuracy: {accuracy:.4f}, F1-Macro: {f1_macro:.4f}")
            else:
                print(f"{algorithm.upper():10} - FAILED")
    
    elif args.algorithm:
        # Test single algorithm
        result = run_quick_test(args.algorithm)
        if result:
            print(f"\n{args.algorithm.upper()} quick test completed successfully!")
        else:
            print(f"\n{args.algorithm.upper()} quick test failed!")
    
    else:
        print("Please specify --algorithm or use --all to test all algorithms")
        print("Example: python quick_test_algorithms.py --algorithm dkdm")
        print("Example: python quick_test_algorithms.py --all")

if __name__ == "__main__":
    main()
