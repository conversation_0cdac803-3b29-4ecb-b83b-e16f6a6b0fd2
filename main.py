"""
Main script to run D³AFD experiments on Amazon Review dataset
"""
import os
import sys
import logging
import argparse
import torch
import random
import numpy as np
from datetime import datetime

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.config import get_default_config, get_memory_optimized_config, get_cpu_config
from src.federated.d3afd_framework import D3AFDFramework
from src.evaluation.baselines import FedAvgBaseline, FedProxBaseline, FedDFBaseline
from src.evaluation.metrics import ExperimentEvaluator

def setup_logging(log_dir: str):
    """Setup logging configuration"""
    os.makedirs(log_dir, exist_ok=True)
    
    # Create log filename with timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(log_dir, f"d3afd_experiment_{timestamp}.log")
    
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    logger = logging.getLogger(__name__)
    logger.info(f"Logging initialized. Log file: {log_file}")
    return logger

def set_random_seeds(seed: int):
    """Set random seeds for reproducibility"""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)
    
    # For deterministic behavior (may impact performance)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False

def run_d3afd_experiment(config, logger):
    """Run D³AFD experiment"""
    logger.info("Starting D³AFD experiment...")
    
    # Initialize framework
    framework = D3AFDFramework(config)
    
    # Run complete training
    results = framework.run_complete_training(force_reload_data=False)
    
    logger.info("D³AFD experiment completed")
    return results

def run_baseline_experiments(config, logger, clients_data):
    """Run baseline method experiments"""
    baseline_results = {}
    
    # Initialize data processor to create clients for baselines
    from src.data.amazon_dataset import AmazonDataProcessor
    from src.federated.client import FederatedClient
    
    data_processor = AmazonDataProcessor(config)
    
    # Create clients for baselines
    clients = {}
    for client_id in range(config.data.num_clients):
        if client_id in clients_data:
            train_loader = data_processor.create_dataloaders(clients_data, client_id, 'train')
            val_loader = data_processor.create_dataloaders(clients_data, client_id, 'val')
            test_loader = data_processor.create_dataloaders(clients_data, client_id, 'test')
            
            client = FederatedClient(
                client_id=client_id,
                config=config,
                train_dataloader=train_loader,
                val_dataloader=val_loader,
                test_dataloader=test_loader
            )
            client.initialize_models()
            clients[client_id] = client
    
    # Run FedAvg
    logger.info("Running FedAvg baseline...")
    fedavg = FedAvgBaseline(config)
    fedavg_results = fedavg.train(clients, num_rounds=config.training.federated_rounds)
    baseline_results['FedAvg'] = fedavg_results
    logger.info("FedAvg baseline completed")

    # Run FedProx
    logger.info("Running FedProx baseline...")
    fedprox = FedProxBaseline(config, mu=0.01)
    fedprox_results = fedprox.train(clients, num_rounds=config.training.federated_rounds)
    baseline_results['FedProx'] = fedprox_results
    logger.info("FedProx baseline completed")

    # Run FedDF
    logger.info("Running FedDF baseline...")
    feddf = FedDFBaseline(config)
    feddf_results = feddf.train(clients, num_rounds=config.training.federated_rounds)
    baseline_results['FedDF'] = feddf_results
    logger.info("FedDF baseline completed")
    
    return baseline_results

def run_comprehensive_evaluation(d3afd_results, baseline_results, config, logger):
    """Run comprehensive evaluation and comparison"""
    logger.info("Starting comprehensive evaluation...")
    
    # Initialize evaluator
    evaluator = ExperimentEvaluator(config)
    
    # Add D³AFD results
    evaluator.add_method_results('D³AFD', d3afd_results)
    
    # Add baseline results
    for method_name, results in baseline_results.items():
        evaluator.add_method_results(method_name, results)
    
    # Generate comparison report
    report = evaluator.generate_comparison_report()
    logger.info("Evaluation Report:")
    logger.info("\n" + report)
    
    # Save report to file
    report_path = os.path.join(config.experiment.output_dir, "evaluation_report.txt")
    with open(report_path, 'w') as f:
        f.write(report)
    logger.info(f"Evaluation report saved to {report_path}")
    
    # Generate plots
    plots_path = os.path.join(config.experiment.output_dir, "training_curves.png")
    evaluator.plot_training_curves(save_path=plots_path)
    
    # Save detailed results
    detailed_results_path = os.path.join(config.experiment.output_dir, "detailed_comparison.json")
    evaluator.save_detailed_results(detailed_results_path)
    
    logger.info("Comprehensive evaluation completed")
    return evaluator

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Run D³AFD experiments on Amazon Review dataset')
    parser.add_argument('--config', type=str, help='Path to config file (optional)')
    parser.add_argument('--output_dir', type=str, default='outputs', help='Output directory')
    parser.add_argument('--log_dir', type=str, default='logs', help='Log directory')
    parser.add_argument('--seed', type=int, default=42, help='Random seed')
    parser.add_argument('--device', type=str, default='cuda', help='Device to use (cuda/cpu)')
    parser.add_argument('--num_clients', type=int, default=10, help='Number of clients')
    parser.add_argument('--federated_rounds', type=int, default=20, help='Number of federated learning rounds')
    parser.add_argument('--distillation_rounds', type=int, default=10, help='Number of distillation rounds per federated round')
    parser.add_argument('--skip_baselines', action='store_true', help='Skip baseline experiments')
    parser.add_argument('--force_reload_data', action='store_true', help='Force reload dataset')
    parser.add_argument('--memory_optimized', action='store_true', help='Use memory-optimized configuration')
    parser.add_argument('--cpu_only', action='store_true', help='Use CPU-only configuration')
    
    args = parser.parse_args()
    
    # Setup logging
    logger = setup_logging(args.log_dir)
    
    # Set random seeds
    set_random_seeds(args.seed)
    
    # Load configuration
    if args.cpu_only:
        config = get_cpu_config()
        logger.info("Using CPU-only configuration")
    elif args.memory_optimized:
        config = get_memory_optimized_config()
        logger.info("Using memory-optimized configuration")
    elif args.config:
        # Load custom config (implementation needed)
        config = get_default_config()
        logger.info(f"Using custom config: {args.config}")
    else:
        config = get_default_config()
        logger.info("Using default configuration")
    
    # Override config with command line arguments
    config.experiment.output_dir = args.output_dir
    config.experiment.log_dir = args.log_dir
    config.experiment.seed = args.seed
    config.experiment.device = args.device
    config.data.num_clients = args.num_clients
    config.training.federated_rounds = args.federated_rounds
    config.training.distillation_rounds = args.distillation_rounds
    
    # Create output directories
    os.makedirs(config.experiment.output_dir, exist_ok=True)
    os.makedirs(config.experiment.data_dir, exist_ok=True)
    
    logger.info("Starting D³AFD experiments...")
    logger.info(f"Configuration: {config.to_dict()}")
    
    try:
        # Run D³AFD experiment
        d3afd_results = run_d3afd_experiment(config, logger)
        
        # Setup data for baselines (reuse D³AFD data setup)
        from src.data.amazon_dataset import AmazonDataProcessor
        data_processor = AmazonDataProcessor(config)
        
        # Load or create federated data
        federated_data_dir = os.path.join(config.experiment.data_dir, "federated_split")
        if os.path.exists(federated_data_dir):
            clients_data = data_processor.load_federated_data(federated_data_dir)
        else:
            df = data_processor.load_amazon_reviews()
            clients_data = data_processor.create_federated_split(df)
            data_processor.save_federated_data(clients_data, federated_data_dir)
        
        # Run baseline experiments
        if not args.skip_baselines:
            baseline_results = run_baseline_experiments(config, logger, clients_data)
        else:
            baseline_results = {}
            logger.info("Skipping baseline experiments")
        
        # Run comprehensive evaluation
        evaluator = run_comprehensive_evaluation(d3afd_results, baseline_results, config, logger)
        
        logger.info("All experiments completed successfully!")
        logger.info(f"Results saved to: {config.experiment.output_dir}")
        
    except Exception as e:
        logger.error(f"Experiment failed with error: {e}")
        raise e

if __name__ == "__main__":
    main()
