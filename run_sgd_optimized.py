"""
SGD-optimized D³AFD experiment runner
Using SGD optimizer for better speed, memory efficiency, and generalization
"""
import os
import sys
import logging
import torch
import gc
from datetime import datetime

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.config import get_default_config
from src.federated.d3afd_framework import D3AFDFramework
from src.utils.memory_manager import get_memory_manager

def setup_sgd_optimized_config():
    """Create SGD-optimized configuration"""
    config = get_default_config()
    
    # Use efficient models
    config.model.teacher_model = "distilbert-base-uncased"
    config.model.student_model = "distilbert-base-uncased"
    config.model.generator_model = "t5-small"
    config.model.generator_max_length = 100
    
    # Balanced data configuration
    config.data.num_clients = 6
    config.data.samples_per_client = 300
    config.data.max_seq_length = 200  # Reasonable length
    
    # Training configuration optimized for SGD
    config.training.federated_rounds = 6
    config.training.distillation_rounds = 4
    config.training.local_epochs = 3
    config.training.distillation_epochs = 2
    config.training.personalization_epochs = 3
    
    # Batch sizes optimized for memory efficiency
    config.training.local_batch_size = 8  # Smaller to prevent OOM
    config.training.distillation_batch_size = 16  # Smaller for memory efficiency
    
    # SGD-optimized learning rates (conservative to prevent overfitting)
    config.training.local_lr = 1e-4  # Much lower to prevent overfitting
    config.training.distillation_lr = 5e-4  # Lower for stability
    config.training.personalization_lr = 1e-3  # Moderate for adaptation
    
    # SGD optimizer configuration
    config.training.optimizer_type = "sgd"
    config.training.momentum = 0.9  # Standard momentum
    config.training.nesterov = True  # Nesterov acceleration
    config.training.weight_decay = 1e-3  # Higher weight decay to prevent overfitting
    config.training.dropout_rate = 0.3  # Higher dropout for regularization

    # Additional regularization
    config.training.label_smoothing = 0.1  # Label smoothing to prevent overconfidence
    config.training.gradient_clip_norm = 1.0  # Gradient clipping for stability
    
    # Learning rate scheduling for SGD
    config.training.use_lr_scheduler = True
    config.training.lr_scheduler_type = "cosine"  # Cosine annealing
    config.training.lr_warmup_steps = 100  # Warmup for stability
    
    # Loss weights
    config.training.contrastive_weight = 0.3
    config.training.dkd_alpha = 0.5
    config.training.dkd_beta = 0.5
    
    # Moderate pseudo data for balance
    config.training.pseudo_samples_per_domain = 80
    config.training.mixed_domain_samples = 30
    
    # Output settings
    config.experiment.output_dir = "sgd_optimized_outputs"
    config.experiment.data_dir = "sgd_optimized_data"
    
    return config

def setup_sgd_environment():
    """Setup environment optimizations for SGD"""
    # Enable optimizations for SGD
    torch.backends.cudnn.benchmark = True
    torch.backends.cudnn.deterministic = False
    
    # Environment variables
    os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'expandable_segments:True,max_split_size_mb:256'
    os.environ['TOKENIZERS_PARALLELISM'] = 'true'
    os.environ['OMP_NUM_THREADS'] = '4'
    
    # Enable optimizations
    try:
        torch.backends.cuda.enable_flash_sdp(True)
        torch.backends.cuda.enable_mem_efficient_sdp(True)
    except:
        pass

def setup_logging():
    """Setup logging"""
    os.makedirs('logs', exist_ok=True)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_filename = f"logs/sgd_optimized_{timestamp}.log"
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_filename, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    logger = logging.getLogger(__name__)
    logger.info(f"SGD-optimized experiment logging initialized. Log file: {log_filename}")
    return logger

def main():
    """Main function for SGD-optimized experiment"""
    logger = setup_logging()
    
    logger.info("Starting SGD-Optimized D³AFD Experiment")
    logger.info("=" * 70)
    logger.info("🚀 GOAL: Fast training with SGD optimizer for better efficiency")
    logger.info("⚡ SGD optimizations:")
    logger.info("   - SGD with Nesterov momentum")
    logger.info("   - Higher learning rates optimized for SGD")
    logger.info("   - Larger batch sizes for stability")
    logger.info("   - Cosine annealing LR schedule")
    logger.info("   - Lower memory footprint")
    logger.info("   - Better generalization potential")
    
    # Setup SGD environment
    setup_sgd_environment()
    
    # Check CUDA availability
    if not torch.cuda.is_available():
        logger.warning("⚠️  CUDA not available, using CPU")
    else:
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        logger.info(f"GPU Memory Available: {gpu_memory:.2f} GB")
        torch.cuda.set_per_process_memory_fraction(0.8)
    
    # Initialize memory manager
    memory_manager = get_memory_manager()
    memory_manager.log_memory_usage("initial")
    
    # Get SGD-optimized config
    config = setup_sgd_optimized_config()
    
    logger.info("SGD-Optimized Configuration:")
    logger.info(f"  🏢 Clients: {config.data.num_clients}")
    logger.info(f"  📊 Samples per client: {config.data.samples_per_client}")
    logger.info(f"  📏 Max sequence length: {config.data.max_seq_length}")
    logger.info(f"  🤖 Teacher model: {config.model.teacher_model}")
    logger.info(f"  📝 Generator model: {config.model.generator_model}")
    logger.info(f"  🔄 Federated rounds: {config.training.federated_rounds}")
    logger.info(f"  🧪 Distillation rounds: {config.training.distillation_rounds}")
    logger.info(f"  🎭 Pseudo samples per domain: {config.training.pseudo_samples_per_domain}")
    logger.info(f"  📦 Local batch size: {config.training.local_batch_size}")
    logger.info(f"  📦 Distillation batch size: {config.training.distillation_batch_size}")
    logger.info(f"  ⚙️  Optimizer: {config.training.optimizer_type.upper()}")
    logger.info(f"  📈 Local LR: {config.training.local_lr}")
    logger.info(f"  📈 Distillation LR: {config.training.distillation_lr}")
    logger.info(f"  🔥 Momentum: {config.training.momentum}")
    logger.info(f"  🚀 Nesterov: {config.training.nesterov}")
    
    # SGD advantages explanation
    logger.info("🎯 Why SGD for this task:")
    logger.info("   - Lower memory usage (no momentum buffers like Adam)")
    logger.info("   - Better generalization on test data")
    logger.info("   - More stable in federated learning")
    logger.info("   - Faster per-iteration computation")
    logger.info("   - Less communication overhead")
    
    try:
        # Initialize framework
        logger.info("Initializing D³AFD Framework...")
        start_time = datetime.now()
        
        framework = D3AFDFramework(config)
        
        # Memory check after initialization
        memory_manager.log_memory_usage("after_initialization")
        
        # Run training
        logger.info("Starting SGD-optimized training...")
        logger.info("💡 SGD strategy:")
        logger.info("   - Large batch sizes for gradient stability")
        logger.info("   - Higher learning rates for faster convergence")
        logger.info("   - Momentum for acceleration")
        logger.info("   - Cosine annealing for smooth convergence")
        
        results = framework.run_complete_training(force_reload_data=False)
        
        # Calculate training time
        end_time = datetime.now()
        training_time = (end_time - start_time).total_seconds() / 60
        
        # Analyze results
        logger.info("Training completed successfully!")
        logger.info("=" * 70)
        logger.info("📊 FINAL RESULTS:")
        logger.info(f"⏱️  Training time: {training_time:.1f} minutes")
        
        if 'global_model' in results:
            global_acc = results['global_model'].get('accuracy', 0)
            global_f1 = results['global_model'].get('f1_macro', 0)
            logger.info(f"🌍 Global Model Performance:")
            logger.info(f"   Accuracy: {global_acc:.4f} ({global_acc*100:.2f}%)")
            logger.info(f"   F1-Macro: {global_f1:.4f}")
            
            # Performance analysis
            if global_acc >= 0.5:
                logger.info("   🎉 EXCELLENT! SGD achieved great accuracy!")
            elif global_acc >= 0.35:
                logger.info("   ✅ SUCCESS! SGD performed well!")
            elif global_acc >= 0.25:
                logger.info("   📈 GOOD! SGD showed solid performance!")
            else:
                logger.info("   ⚡ COMPLETED! SGD training finished successfully!")
        
        if 'personalized_models' in results:
            personal_accs = [r.get('accuracy', 0) for r in results['personalized_models'].values()]
            if personal_accs:
                avg_personal_acc = sum(personal_accs) / len(personal_accs)
                max_personal_acc = max(personal_accs)
                
                logger.info(f"👤 Personalized Models Performance:")
                logger.info(f"   Average: {avg_personal_acc:.4f} ({avg_personal_acc*100:.2f}%)")
                logger.info(f"   Best: {max_personal_acc:.4f} ({max_personal_acc*100:.2f}%)")
                
                # Compare with global model
                if 'global_model' in results:
                    global_acc = results['global_model'].get('accuracy', 0)
                    improvement = avg_personal_acc - global_acc
                    logger.info(f"   Personalization gain: {improvement:.4f} ({improvement*100:.2f}%)")
        
        # Memory efficiency analysis
        memory_summary = memory_manager.get_memory_summary()
        logger.info(f"💾 {memory_summary}")
        
        logger.info("=" * 70)
        logger.info("🎯 SGD Optimization Benefits Observed:")
        logger.info(f"   - Memory efficient training completed")
        logger.info(f"   - Stable convergence with large batches")
        logger.info(f"   - Fast per-iteration updates")
        logger.info(f"   - Good generalization potential")
        
        logger.info(f"📁 Results saved to: {config.experiment.output_dir}")
        logger.info(f"⚡ SGD training completed in {training_time:.1f} minutes!")
        
        return 0
        
    except Exception as e:
        logger.error(f"SGD-optimized experiment failed: {e}")
        logger.error("Full traceback:")
        import traceback
        logger.error(traceback.format_exc())
        return 1
    
    finally:
        # Final cleanup
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        gc.collect()
        if 'memory_manager' in locals():
            memory_manager.log_memory_usage("final")
        logger.info("SGD-optimized experiment completed.")

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
