"""
Example configuration for D³AFD experiments
Shows how to customize training parameters including federated rounds
"""
from src.config import get_default_config

def get_quick_test_config():
    """Configuration for quick testing"""
    config = get_default_config()
    
    # Data settings
    config.data.num_clients = 5
    config.data.samples_per_client = 500
    
    # Training settings
    config.training.federated_rounds = 5        # Total federated learning rounds
    config.training.distillation_rounds = 3     # Distillation rounds per federated round
    config.training.local_epochs = 3
    config.training.distillation_epochs = 2
    config.training.pseudo_samples_per_domain = 100
    
    # Output settings
    config.experiment.output_dir = "quick_test_outputs"
    config.experiment.data_dir = "quick_test_data"
    
    return config

def get_full_experiment_config():
    """Configuration for full experiments"""
    config = get_default_config()
    
    # Data settings
    config.data.num_clients = 10
    config.data.samples_per_client = 1000
    
    # Training settings
    config.training.federated_rounds = 20       # Total federated learning rounds
    config.training.distillation_rounds = 10    # Distillation rounds per federated round
    config.training.local_epochs = 5
    config.training.distillation_epochs = 3
    config.training.pseudo_samples_per_domain = 500
    
    # DKD parameters
    config.training.alpha = 0.3  # Target class weight
    config.training.beta = 0.7   # Non-target class weight
    
    # Contrastive learning
    config.training.contrastive_weight = 0.1
    
    # Output settings
    config.experiment.output_dir = "full_experiment_outputs"
    config.experiment.data_dir = "full_experiment_data"
    
    return config

def get_large_scale_config():
    """Configuration for large-scale experiments"""
    config = get_default_config()
    
    # Data settings
    config.data.num_clients = 20
    config.data.samples_per_client = 2000
    
    # Training settings
    config.training.federated_rounds = 30       # Total federated learning rounds
    config.training.distillation_rounds = 15    # Distillation rounds per federated round
    config.training.local_epochs = 5
    config.training.distillation_epochs = 3
    config.training.pseudo_samples_per_domain = 1000
    
    # Learning rates
    config.training.local_lr = 1e-5
    config.training.distillation_lr = 5e-5
    
    # Output settings
    config.experiment.output_dir = "large_scale_outputs"
    config.experiment.data_dir = "large_scale_data"
    
    return config

def print_config_summary(config):
    """Print a summary of the configuration"""
    print("=" * 60)
    print("D³AFD CONFIGURATION SUMMARY")
    print("=" * 60)
    
    print(f"\nDATA CONFIGURATION:")
    print(f"  Number of clients: {config.data.num_clients}")
    print(f"  Samples per client: {config.data.samples_per_client}")
    print(f"  Domains: {len(config.data.domains)} ({', '.join(config.data.domains[:3])}...)")
    
    print(f"\nTRAINING CONFIGURATION:")
    print(f"  Federated rounds: {config.training.federated_rounds}")
    print(f"  Distillation rounds per federated round: {config.training.distillation_rounds}")
    print(f"  Total distillation rounds: {config.training.federated_rounds * config.training.distillation_rounds}")
    print(f"  Local epochs: {config.training.local_epochs}")
    print(f"  Distillation epochs: {config.training.distillation_epochs}")
    
    print(f"\nDKD PARAMETERS:")
    print(f"  Alpha (target class weight): {config.training.alpha}")
    print(f"  Beta (non-target class weight): {config.training.beta}")
    print(f"  Temperature: {config.training.temperature}")
    
    print(f"\nPSEUDO DATA GENERATION:")
    print(f"  Samples per domain: {config.training.pseudo_samples_per_domain}")
    print(f"  Mixed domain samples: {config.training.mixed_domain_samples}")
    
    print(f"\nOUTPUT CONFIGURATION:")
    print(f"  Output directory: {config.experiment.output_dir}")
    print(f"  Data directory: {config.experiment.data_dir}")
    print(f"  Device: {config.experiment.device}")
    
    print("=" * 60)

if __name__ == "__main__":
    print("D³AFD Configuration Examples\n")
    
    # Show different configurations
    configs = {
        "Quick Test": get_quick_test_config(),
        "Full Experiment": get_full_experiment_config(),
        "Large Scale": get_large_scale_config()
    }
    
    for name, config in configs.items():
        print(f"\n{name.upper()} CONFIGURATION:")
        print_config_summary(config)
        print("\n" + "-" * 60)
