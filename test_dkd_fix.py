"""
Test script to verify DKD loss fixes
"""
import torch
import sys
import os

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.models.dkd_distillation import <PERSON><PERSON><PERSON><PERSON><PERSON>

def test_dkd_with_various_shapes():
    """Test DKD loss with various tensor shapes"""
    print("🧪 Testing DKD Loss with various shapes...")
    
    dkd_loss = DKDLoss(alpha=0.7, beta=0.3, temperature=3.0)
    
    test_cases = [
        (4, 5),   # 4 samples, 5 classes
        (8, 5),   # 8 samples, 5 classes
        (16, 5),  # 16 samples, 5 classes (the problematic case)
        (32, 5),  # 32 samples, 5 classes
        (1, 5),   # 1 sample, 5 classes
        (2, 3),   # 2 samples, 3 classes
    ]
    
    for batch_size, num_classes in test_cases:
        print(f"\n📊 Testing batch_size={batch_size}, num_classes={num_classes}")
        
        # Create test data
        student_logits = torch.randn(batch_size, num_classes)
        teacher_logits = torch.randn(batch_size, num_classes)
        
        try:
            # Compute DKD loss
            losses = dkd_loss(student_logits, teacher_logits)
            
            print(f"   ✅ Success:")
            print(f"      TCKD Loss: {losses['tckd_loss'].item():.6f}")
            print(f"      NCKD Loss: {losses['nckd_loss'].item():.6f}")
            print(f"      Total DKD Loss: {losses['dkd_loss'].item():.6f}")
            
            # Check for NaN
            if torch.isnan(losses['dkd_loss']):
                print(f"   ⚠️  NaN detected in total loss")
            
        except Exception as e:
            print(f"   ❌ Failed: {e}")
            return False
    
    return True

def test_edge_cases():
    """Test edge cases that might cause issues"""
    print("\n🔬 Testing edge cases...")
    
    dkd_loss = DKDLoss(alpha=0.7, beta=0.3, temperature=3.0)
    
    # Test case 1: All zeros
    print("\n📊 Test case 1: All zeros")
    student_logits = torch.zeros(16, 5)
    teacher_logits = torch.zeros(16, 5)
    
    try:
        losses = dkd_loss(student_logits, teacher_logits)
        print(f"   ✅ All zeros: DKD Loss = {losses['dkd_loss'].item():.6f}")
    except Exception as e:
        print(f"   ❌ All zeros failed: {e}")
        return False
    
    # Test case 2: Extreme values
    print("\n📊 Test case 2: Extreme values")
    student_logits = torch.tensor([[100.0, -100.0, 0.0, 0.0, 0.0]] * 16)
    teacher_logits = torch.tensor([[-100.0, 100.0, 0.0, 0.0, 0.0]] * 16)
    
    try:
        losses = dkd_loss(student_logits, teacher_logits)
        print(f"   ✅ Extreme values: DKD Loss = {losses['dkd_loss'].item():.6f}")
    except Exception as e:
        print(f"   ❌ Extreme values failed: {e}")
        return False
    
    # Test case 3: Shape mismatch
    print("\n📊 Test case 3: Shape mismatch")
    student_logits = torch.randn(16, 5)
    teacher_logits = torch.randn(16, 4)  # Different number of classes
    
    try:
        losses = dkd_loss(student_logits, teacher_logits)
        print(f"   ✅ Shape mismatch handled: DKD Loss = {losses['dkd_loss'].item():.6f}")
    except Exception as e:
        print(f"   ❌ Shape mismatch failed: {e}")
        return False
    
    return True

def test_gradient_flow():
    """Test gradient flow"""
    print("\n🔄 Testing gradient flow...")
    
    # Create a simple model
    model = torch.nn.Linear(10, 5)
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
    dkd_loss = DKDLoss()
    
    # Test data
    x = torch.randn(16, 10)
    student_logits = model(x)
    teacher_logits = torch.randn(16, 5)
    
    try:
        # Forward pass
        losses = dkd_loss(student_logits, teacher_logits)
        
        # Backward pass
        optimizer.zero_grad()
        losses['dkd_loss'].backward()
        
        # Check gradients
        has_grad = False
        for param in model.parameters():
            if param.grad is not None and not torch.isnan(param.grad).any():
                has_grad = True
                break
        
        if has_grad:
            print("   ✅ Gradient flow is working")
            return True
        else:
            print("   ❌ No valid gradients found")
            return False
            
    except Exception as e:
        print(f"   ❌ Gradient flow failed: {e}")
        return False

def main():
    """Main test function"""
    print("=" * 60)
    print("🔧 Testing DKD Loss Fixes")
    print("=" * 60)
    
    # Run tests
    shape_test = test_dkd_with_various_shapes()
    edge_test = test_edge_cases()
    grad_test = test_gradient_flow()
    
    print("\n" + "=" * 60)
    print("📊 Test Results:")
    print(f"   Shape Tests: {'✅ PASS' if shape_test else '❌ FAIL'}")
    print(f"   Edge Cases: {'✅ PASS' if edge_test else '❌ FAIL'}")
    print(f"   Gradient Flow: {'✅ PASS' if grad_test else '❌ FAIL'}")
    
    all_passed = shape_test and edge_test and grad_test
    
    if all_passed:
        print("\n🎉 All tests passed! DKD fixes are working correctly.")
        print("You can now restart the experiment.")
        return 0
    else:
        print("\n⚠️  Some tests failed. Please check the fixes.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
