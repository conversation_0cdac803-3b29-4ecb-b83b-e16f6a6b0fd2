"""
Configuration utilities for D³AFD framework
"""

import os
import yaml
from typing import Dict, Any, Optional
from pathlib import Path


class Config:
    """
    Configuration class that provides dot notation access to nested dictionaries
    """
    
    def __init__(self, config_dict: Dict[str, Any]):
        """
        Initialize config from dictionary
        
        Args:
            config_dict: Configuration dictionary
        """
        self._config = config_dict
        self._setup_attributes(config_dict)
    
    def _setup_attributes(self, config_dict: Dict[str, Any], prefix: str = ""):
        """
        Recursively setup attributes for dot notation access
        
        Args:
            config_dict: Configuration dictionary
            prefix: Prefix for nested attributes
        """
        for key, value in config_dict.items():
            if isinstance(value, dict):
                # Create nested Config object only for dictionaries
                nested_config = Config(value)
                setattr(self, key, nested_config)
            elif isinstance(value, list):
                # Keep lists as-is, don't convert to Config objects
                setattr(self, key, value)
            else:
                setattr(self, key, value)
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        Get configuration value with dot notation support
        
        Args:
            key: Configuration key (supports dot notation like 'training.batch_size')
            default: Default value if key not found
            
        Returns:
            Configuration value or default
        """
        try:
            keys = key.split('.')
            value = self._config
            
            for k in keys:
                if isinstance(value, dict) and k in value:
                    value = value[k]
                else:
                    return default
            
            return value
        except:
            return default
    
    def set(self, key: str, value: Any) -> None:
        """
        Set configuration value with dot notation support
        
        Args:
            key: Configuration key (supports dot notation)
            value: Value to set
        """
        keys = key.split('.')
        config = self._config
        
        # Navigate to the parent dictionary
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        # Set the value
        config[keys[-1]] = value
        
        # Update attributes
        self._setup_attributes(self._config)
    
    def update(self, other_config: Dict[str, Any]) -> None:
        """
        Update configuration with another dictionary
        
        Args:
            other_config: Dictionary to merge
        """
        self._config = self._deep_merge(self._config, other_config)
        self._setup_attributes(self._config)
    
    def _deep_merge(self, dict1: Dict, dict2: Dict) -> Dict:
        """
        Deep merge two dictionaries
        
        Args:
            dict1: First dictionary
            dict2: Second dictionary
            
        Returns:
            Merged dictionary
        """
        result = dict1.copy()
        
        for key, value in dict2.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._deep_merge(result[key], value)
            else:
                result[key] = value
        
        return result
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert config back to dictionary
        
        Returns:
            Configuration dictionary
        """
        return self._config.copy()
    
    def save(self, path: str) -> None:
        """
        Save configuration to YAML file
        
        Args:
            path: Path to save configuration
        """
        with open(path, 'w') as f:
            yaml.dump(self._config, f, default_flow_style=False, indent=2)
    
    @classmethod
    def from_file(cls, path: str) -> 'Config':
        """
        Load configuration from YAML file
        
        Args:
            path: Path to configuration file
            
        Returns:
            Config object
        """
        with open(path, 'r') as f:
            config_dict = yaml.safe_load(f)
        
        return cls(config_dict)
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'Config':
        """
        Create config from dictionary
        
        Args:
            config_dict: Configuration dictionary
            
        Returns:
            Config object
        """
        return cls(config_dict)
    
    def __getitem__(self, key: str) -> Any:
        """Support dictionary-style access"""
        return self.get(key)
    
    def __setitem__(self, key: str, value: Any) -> None:
        """Support dictionary-style assignment"""
        self.set(key, value)
    
    def __contains__(self, key: str) -> bool:
        """Support 'in' operator"""
        return self.get(key) is not None
    
    def __repr__(self) -> str:
        """String representation"""
        return f"Config({self._config})"


def load_config(config_path: str, overrides: Optional[Dict[str, Any]] = None) -> Config:
    """
    Load configuration from file with optional overrides
    
    Args:
        config_path: Path to configuration file
        overrides: Optional dictionary of overrides
        
    Returns:
        Config object
    """
    # Check if file exists
    if not os.path.exists(config_path):
        raise FileNotFoundError(f"Configuration file not found: {config_path}")
    
    # Load base configuration
    config = Config.from_file(config_path)
    
    # Apply overrides if provided
    if overrides:
        config.update(overrides)
    
    return config


def create_default_config() -> Config:
    """
    Create a default configuration for D³AFD
    
    Returns:
        Default Config object
    """
    default_config = {
        'experiment': {
            'name': 'd3afd_experiment',
            'device': 'cuda',
            'seed': 42,
            'output_dir': 'outputs',
            'log_level': 'INFO'
        },
        'data': {
            'dataset_name': 'amazon_reviews',
            'data_dir': 'data',
            'domains': ['Electronics', 'Books'],
            'max_length': 512,
            'train_split': 0.8,
            'val_split': 0.1,
            'test_split': 0.1,
            'num_workers': 4
        },
        'model': {
            'model_name': 'distilbert-base-uncased',
            'num_labels': 3,
            'hidden_size': 768,
            'dropout': 0.1,
            'freeze_backbone': False
        },
        'training': {
            'distillation_algorithm': 'dkd',
            'num_rounds': 10,
            'local_epochs': 3,
            'distillation_epochs': 5,
            'batch_size': 16,
            'distillation_batch_size': 32,
            'learning_rate': 2e-5,
            'distillation_lr': 1e-4,
            'weight_decay': 0.01,
            'optimizer_type': 'adamw',
            'pseudo_samples_per_domain': 1000,
            'mixed_domain_samples': 500,
            'cache_refresh_interval': 3,
            'contrastive_weight': 0.1,
            'domain_weight': 0.05
        },
        'federated': {
            'num_clients': 4,
            'clients_per_round': 4,
            'client_selection': 'all',
            'data_distribution': 'domain_specific',
            'alpha': 0.5
        },
        'evaluation': {
            'metrics': ['accuracy', 'f1_macro', 'f1_weighted'],
            'eval_frequency': 2,
            'save_best_model': True,
            'early_stopping_patience': 5
        },
        'memory': {
            'enable_memory_management': True,
            'max_memory_usage': 0.8,
            'cleanup_frequency': 1,
            'force_cleanup_epochs': True
        },
        'logging': {
            'log_frequency': 10,
            'save_logs': True,
            'tensorboard': False,
            'wandb': {'enabled': False}
        }
    }
    
    return Config(default_config)
