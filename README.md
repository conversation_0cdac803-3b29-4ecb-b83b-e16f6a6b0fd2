# D³AFD: Diffusion-Driven Domain-Adaptive Data-Free Federated Distillation

基于Amazon Review数据集的D³AFD算法实现，用于多域非IID联邦学习场景下的知识蒸馏。

## 项目概述

D³AFD（Diffusion-Driven Domain-Adaptive Data-Free Federated Distillation）是一种创新的联邦学习算法，专门设计用于处理多域异构数据分布的场景。本项目在Amazon Review数据集上实现了完整的D³AFD框架，包括：

- **文本生成器**：基于T5模型的条件文本生成，替代扩散模型在文本领域的应用
- **域判别器**：评估样本与特定域的相关性，为蒸馏过程提供权重
- **分离式知识蒸馏（DKD）**：将目标类和非目标类蒸馏分离，提高知识传递效果
- **跨域对比学习**：对齐不同域间的特征表示
- **个性化微调**：轻量级的客户端个性化适配

## 核心特性

### 1. 数据无依赖蒸馏
- 使用T5生成器创建伪数据，无需共享真实数据
- 支持多域条件生成和混合域样本生成
- 保护客户端数据隐私

### 2. 域自适应加权
- 每个客户端训练域判别器评估样本相关性
- 动态调整不同域模型在蒸馏中的贡献权重
- 减少无关域信息的干扰

### 3. 分离式知识蒸馏
- 将蒸馏损失分解为目标类（TCKD）和非目标类（NCKD）
- 通过权重调节平衡主要类别和次要类别的学习
- 适应类别不均衡的多域场景

### 4. 跨域对比学习
- 对同类不同域样本特征进行拉近
- 对异类样本特征进行推远
- 学习域不变的判别表示

### 5. 轻量化个性化
- 共享全局骨干网络，仅微调本地输出头
- 极低的通信和计算开销
- 兼顾全局一致性和本地适应性

## 项目结构

```
amazon-re-fd/
├── src/
│   ├── config.py                 # 配置管理
│   ├── data/
│   │   ├── amazon_dataset.py     # Amazon Review数据处理
│   │   └── __init__.py
│   ├── models/
│   │   ├── base_models.py        # 基础模型定义
│   │   ├── text_generator.py     # T5文本生成器
│   │   ├── domain_discriminator.py # 域判别器
│   │   ├── dkd_distillation.py   # DKD蒸馏模块
│   │   ├── contrastive_learning.py # 对比学习模块
│   │   └── __init__.py
│   ├── federated/
│   │   ├── client.py             # 联邦客户端
│   │   ├── server.py             # 联邦服务器
│   │   ├── d3afd_framework.py    # D³AFD主框架
│   │   └── __init__.py
│   ├── evaluation/
│   │   ├── baselines.py          # 基线方法实现
│   │   ├── metrics.py            # 评估指标
│   │   └── __init__.py
│   └── __init__.py
├── docs/
│   └── D³AFD算法研究方案.md      # 算法详细方案
├── main.py                       # 主实验脚本
├── demo.py                       # 演示脚本
├── requirements.txt              # 依赖包
├── 文本数据集实验方案.md          # 文本实验方案
└── README.md                     # 项目说明
```

## 安装和使用

### 环境要求

- Python 3.8+
- PyTorch 1.12+
- Transformers 4.20+
- CUDA（推荐，用于GPU加速）

### 安装依赖

```bash
pip install -r requirements.txt
```

### 快速开始

#### 1. 运行演示

```bash
python demo.py
```

演示脚本将展示：
- Amazon Review数据集处理
- T5文本生成器功能
- 域判别器训练
- 简化的联邦训练过程

#### 2. 运行完整实验

```bash
# 使用默认配置（需要>16GB GPU内存）
python main.py

# 内存优化配置（适用于8-16GB GPU）
python main.py --memory_optimized

# CPU配置（无GPU要求，但速度较慢）
python main.py --cpu_only

# 自定义参数
python main.py --num_clients 10 --federated_rounds 20 --distillation_rounds 15 --output_dir results
```

#### 3. 内存不足解决方案

如果遇到CUDA内存不足错误，请参考：

```bash
# 方案1：使用内存优化脚本
python run_memory_optimized.py

# 方案2：使用CPU演示
python run_cpu_demo.py
```

详细的内存优化指南请参考：[MEMORY_OPTIMIZATION.md](MEMORY_OPTIMIZATION.md)

#### 4. 主要参数

- `--num_clients`: 客户端数量（默认：10）
- `--federated_rounds`: 联邦学习轮数（默认：20）
- `--distillation_rounds`: 每个联邦轮次内的蒸馏轮数（默认：10）
- `--output_dir`: 输出目录（默认：outputs）
- `--device`: 设备类型（默认：cuda）
- `--memory_optimized`: 使用内存优化配置
- `--cpu_only`: 使用CPU配置
- `--skip_baselines`: 跳过基线方法对比
- `--force_reload_data`: 强制重新加载数据

## 实验设计

### 数据集设置

- **数据源**：Amazon Review多语言数据集
- **域划分**：按产品类别划分（Books, Electronics, Home & Kitchen等）
- **任务**：5分类情感分析（1-5星评级）
- **非IID设置**：每个客户端分配1-2个域的数据

### 基线方法对比

实现了以下基线方法用于对比：

1. **FedAvg**：经典联邦平均算法
2. **FedProx**：带近端项的联邦学习
3. **FedDF**：联邦蒸馏方法

### 评估指标

- **全局性能**：准确率、F1分数（宏平均、加权平均）
- **个性化性能**：各客户端本地测试准确率
- **域间一致性**：不同域间性能方差
- **通信效率**：收敛轮数、每轮通信量
- **训练稳定性**：训练过程中的性能波动

## 核心算法流程

### D³AFD四阶段设计

根据原始算法设计，D³AFD的整体流程包含四大阶段，每个联邦轮次都完整执行这四个阶段：

**每个联邦轮次的四阶段流程：**

```
联邦轮次 1:
  ├── 阶段1: 本地模型训练 (初次训练)
  ├── 阶段2: 伪数据生成 (T5生成多域样本)
  ├── 阶段3: 加权DKD蒸馏 (多次蒸馏迭代)
  └── 阶段4: 个性化微调 (轻量级适配)

联邦轮次 2:
  ├── 阶段1: 本地模型训练 (重训练/更新)
  ├── 阶段2: 伪数据生成 (新的伪样本)
  ├── 阶段3: 加权DKD蒸馏 (继续蒸馏)
  └── 阶段4: 个性化微调 (更新个性化)
...
```

### 详细阶段说明

#### 阶段1：本地模型训练
- **初次训练**：各客户端在私有数据上训练本地教师模型
- **域判别器训练**：训练域判别器识别本域样本
- **重训练**：后续轮次中可选的模型更新

#### 阶段2：伪数据生成
- 服务器使用T5生成器创建域特定伪样本
- 生成混合域样本覆盖域间过渡分布
- 为每个域和混合域生成足够的训练样本

#### 阶段3：加权DKD蒸馏
- 收集各客户端教师模型的预测
- 使用域判别器计算样本-域相关性权重
- 通过加权融合创建集成教师
- 使用DKD损失和对比损失训练全局学生模型
- 在每个联邦轮次内进行多次蒸馏迭代

#### 阶段4：个性化微调
- 分发当前全局模型骨干到各客户端
- 各客户端创建/更新个性化输出头
- 在本地数据上快速微调个性化组件
- 保持全局一致性的同时适应本地分布

## 实验结果

运行完整实验后，将在输出目录生成：

- `experiment_results.json`：详细实验结果
- `evaluation_report.txt`：对比评估报告
- `training_curves.png`：训练曲线图
- `detailed_comparison.json`：详细对比数据

## 技术创新点

1. **首次将T5模型用于联邦蒸馏的伪数据生成**，适应文本领域特点
2. **域自适应加权机制**，动态调整多教师模型贡献
3. **分离式知识蒸馏**，提高类别不均衡场景下的性能
4. **跨域对比学习**，增强特征的域不变性
5. **轻量化个性化方案**，适合资源受限设备

## 贡献和引用

本项目实现了D³AFD算法在文本领域的完整应用，为多域联邦学习提供了创新的技术方案。

## 许可证

本项目采用MIT许可证。详见LICENSE文件。

## 联系方式

如有问题或建议，请通过GitHub Issues联系。
