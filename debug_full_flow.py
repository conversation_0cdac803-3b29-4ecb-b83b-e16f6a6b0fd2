#!/usr/bin/env python3
"""
Debug script to trace the full execution flow
"""

import sys
import os

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def debug_full_flow():
    """Debug the full execution flow step by step"""
    print("=" * 60)
    print("DEBUG: Full Execution Flow")
    print("=" * 60)
    
    try:
        # Step 1: Force reload modules
        import importlib
        
        modules_to_reload = [
            'src.federated.client',
            'src.data.amazon_dataset',
            'src.utils.config',
            'train_d3afd_multi_algorithm'
        ]
        
        for module_name in modules_to_reload:
            if module_name in sys.modules:
                importlib.reload(sys.modules[module_name])
        
        print("✅ Step 1: Modules reloaded")
        
        # Step 2: Create config exactly like quick_test_algorithms.py
        from quick_test_algorithms import setup_quick_test_config
        from src.utils.config import Config
        from src.utils.logger import setup_logger
        
        config_dict = setup_quick_test_config('dkdm')
        config = Config(config_dict)
        logger = setup_logger(config.experiment.log_level)
        
        print("✅ Step 2: Config created")
        print(f"    Domains: {config.data.domains}")

        # Step 2.5: Test dataset BEFORE importing train_d3afd_multi_algorithm
        print("\n" + "-" * 40)
        print("Testing dataset BEFORE importing train module:")
        print("-" * 40)

        from src.data.amazon_dataset import AmazonReviewDataset
        dataset_before = AmazonReviewDataset(config)
        print(f"Dataset before import: {len(dataset_before)} samples")
        print(f"Domains before import: {list(set(dataset_before.domains))}")

        # Step 3: Import run_experiment
        print("\nImporting train_d3afd_multi_algorithm...")
        from train_d3afd_multi_algorithm import run_experiment

        print("✅ Step 3: run_experiment imported")

        # Step 3.5: Test dataset AFTER importing train_d3afd_multi_algorithm
        print("\n" + "-" * 40)
        print("Testing dataset AFTER importing train module:")
        print("-" * 40)

        dataset_after = AmazonReviewDataset(config)
        print(f"Dataset after import: {len(dataset_after)} samples")
        print(f"Domains after import: {list(set(dataset_after.domains))}")
        
        # Step 4: Manually execute the first part of run_experiment
        print("\n" + "-" * 40)
        print("Executing run_experiment steps manually:")
        print("-" * 40)
        
        algorithm = 'dkdm'
        print(f"Algorithm: {algorithm}")
        
        # Update config with selected algorithm
        config.training.distillation_algorithm = algorithm
        print(f"Config updated with algorithm: {config.training.distillation_algorithm}")
        
        # Load dataset (this is where the problem might be)
        print("About to load dataset...")
        from src.data.amazon_dataset import AmazonReviewDataset
        dataset = AmazonReviewDataset(config)
        print(f"Dataset loaded: {len(dataset)} samples")
        print(f"Dataset domains: {list(set(dataset.domains))}")
        
        # Check domain data
        for domain in config.data.domains:
            domain_data = dataset.get_domain_data(domain)
            print(f"  Domain '{domain}': {len(domain_data)} samples")
        
        # Setup clients
        print("\nSetting up clients...")
        from train_d3afd_multi_algorithm import setup_clients
        clients = setup_clients(config, dataset)
        
        print(f"Clients created: {len(clients)}")
        for client_id, client in clients.items():
            has_data = client.train_dataloader is not None
            print(f"  {client_id}: {'✅' if has_data else '❌'} {'Has data' if has_data else 'No data'}")
        
        print("\n" + "=" * 60)
        print("DEBUG COMPLETED")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        import traceback
        print(f"❌ Error in debug: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = debug_full_flow()
    if success:
        print("\n🎉 Debug completed successfully!")
    else:
        print("\n💥 Debug failed.")
