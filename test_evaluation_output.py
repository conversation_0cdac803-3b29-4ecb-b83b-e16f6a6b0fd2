"""
Test script to verify evaluation output formatting
"""
import logging

def test_evaluation_logging():
    """Test the evaluation logging format"""
    print("🧪 Testing evaluation logging format...")
    
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    logger = logging.getLogger(__name__)
    
    # Simulate evaluation results
    mock_results = [
        {
            'round': 'Federated_Round_1',
            'accuracy': 0.2567,
            'f1_macro': 0.2134,
            'loss': 1.4523
        },
        {
            'round': 'Federated_Round_2', 
            'accuracy': 0.3421,
            'f1_macro': 0.3089,
            'loss': 1.2876
        },
        {
            'round': 'Federated_Round_3',
            'accuracy': 0.4156,
            'f1_macro': 0.3892,
            'loss': 1.1234
        },
        {
            'round': 'Federated_Round_4',
            'accuracy': 0.4789,
            'f1_macro': 0.4456,
            'loss': 0.9876
        },
        {
            'round': 'Federated_Round_5',
            'accuracy': 0.5234,
            'f1_macro': 0.4923,
            'loss': 0.8765
        }
    ]
    
    print("\n📊 Simulated Federated Learning Progress:")
    print("=" * 70)
    
    for i, results in enumerate(mock_results, 1):
        round_identifier = results['round']
        all_test_samples = 1000  # Mock test sample count
        
        logger.info(f"🎯 {round_identifier} Global Model Evaluation:")
        logger.info(f"   📊 Accuracy: {results['accuracy']:.4f} ({results['accuracy']*100:.2f}%)")
        logger.info(f"   📈 F1-Macro: {results['f1_macro']:.4f}")
        logger.info(f"   📉 Loss: {results['loss']:.4f}")
        logger.info(f"   📋 Test Samples: {all_test_samples}")
        
        # Show improvement
        if i > 1:
            prev_acc = mock_results[i-2]['accuracy']
            improvement = results['accuracy'] - prev_acc
            if improvement > 0:
                logger.info(f"   📈 Improvement: +{improvement:.4f} ({improvement*100:.2f}%)")
            else:
                logger.info(f"   📉 Change: {improvement:.4f} ({improvement*100:.2f}%)")
        
        logger.info(f"✅ Federated Round {i}/{len(mock_results)} completed")
        logger.info("=" * 60)
        
        print()  # Add spacing between rounds
    
    logger.info("🎉 All federated learning rounds completed!")
    
    # Summary
    print("\n📈 Training Progress Summary:")
    print(f"   Initial Accuracy: {mock_results[0]['accuracy']:.4f} ({mock_results[0]['accuracy']*100:.2f}%)")
    print(f"   Final Accuracy:   {mock_results[-1]['accuracy']:.4f} ({mock_results[-1]['accuracy']*100:.2f}%)")
    print(f"   Total Improvement: +{mock_results[-1]['accuracy'] - mock_results[0]['accuracy']:.4f} ({(mock_results[-1]['accuracy'] - mock_results[0]['accuracy'])*100:.2f}%)")
    
    return True

def test_accuracy_tracking():
    """Test accuracy tracking across rounds"""
    print("\n🔍 Testing accuracy tracking...")
    
    accuracies = [0.25, 0.34, 0.42, 0.48, 0.52, 0.56, 0.59, 0.61]
    
    print("Round | Accuracy | Change")
    print("------|----------|--------")
    
    for i, acc in enumerate(accuracies, 1):
        if i == 1:
            change_str = "  -    "
        else:
            change = acc - accuracies[i-2]
            if change > 0:
                change_str = f"+{change:.3f}"
            else:
                change_str = f"{change:.3f}"
        
        print(f"  {i:2d}  |  {acc:.3f}   | {change_str}")
    
    # Check for convergence
    recent_changes = [accuracies[i] - accuracies[i-1] for i in range(-3, 0)]
    avg_recent_change = sum(recent_changes) / len(recent_changes)
    
    print(f"\nRecent average change: {avg_recent_change:.4f}")
    if avg_recent_change < 0.01:
        print("🔄 Model appears to be converging (small improvements)")
    else:
        print("📈 Model is still improving significantly")
    
    return True

def main():
    """Main test function"""
    print("=" * 70)
    print("🔧 Testing Evaluation Output Format")
    print("=" * 70)
    
    # Run tests
    logging_test = test_evaluation_logging()
    tracking_test = test_accuracy_tracking()
    
    print("\n" + "=" * 70)
    print("📊 Test Results:")
    print(f"   Evaluation Logging: {'✅ PASS' if logging_test else '❌ FAIL'}")
    print(f"   Accuracy Tracking: {'✅ PASS' if tracking_test else '❌ FAIL'}")
    
    all_passed = logging_test and tracking_test
    
    if all_passed:
        print("\n🎉 All tests passed! Evaluation output format is ready.")
        print("You should now see detailed accuracy information for each federated round.")
        return 0
    else:
        print("\n⚠️  Some tests failed.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    exit(exit_code)
