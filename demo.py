"""
Demo script for D³AFD on Amazon Review dataset
Simplified version for quick testing and demonstration
"""
import os
import sys
import logging
import torch

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.config import get_default_config
from src.data.amazon_dataset import AmazonDataProcessor
from src.models.text_generator import T5TextGenerator
from src.models.domain_discriminator import DomainDiscriminatorTrainer
from src.federated.d3afd_framework import D3AFDFramework

def setup_demo_logging():
    """Setup simple logging for demo"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def demo_data_processing():
    """Demonstrate data processing capabilities"""
    logger = logging.getLogger(__name__)
    logger.info("=== Demo: Data Processing ===")
    
    # Get config
    config = get_default_config()
    config.data.num_clients = 5  # Smaller for demo
    config.experiment.data_dir = "demo_data"
    
    # Initialize data processor
    data_processor = AmazonDataProcessor(config)
    
    # Load and process data
    logger.info("Loading Amazon Reviews dataset...")
    df = data_processor.load_amazon_reviews()
    logger.info(f"Loaded {len(df)} samples")
    
    # Create federated split
    logger.info("Creating federated data split...")
    clients_data = data_processor.create_federated_split(df)
    
    # Show statistics
    stats = data_processor.get_domain_statistics(clients_data)
    logger.info("Federated data statistics:")
    logger.info(f"  Total clients: {stats['total_clients']}")
    logger.info(f"  Domain distribution: {dict(stats['domain_distribution'])}")
    
    for client_id, domain_counts in stats['client_domain_counts'].items():
        logger.info(f"  Client {client_id}: {domain_counts}")
    
    return clients_data

def demo_text_generation():
    """Demonstrate text generation capabilities"""
    logger = logging.getLogger(__name__)
    logger.info("=== Demo: Text Generation ===")
    
    # Get config
    config = get_default_config()
    
    # Initialize text generator
    logger.info("Initializing T5 text generator...")
    text_generator = T5TextGenerator(config)
    
    # Generate samples for different domains
    domains_to_test = ["Books", "Electronics", "Home_and_Kitchen"]
    
    for domain in domains_to_test:
        logger.info(f"\nGenerating samples for domain: {domain}")
        
        # Generate a few samples
        samples = text_generator.generate_pseudo_samples(
            domain=domain, 
            num_samples=3
        )
        
        for i, sample in enumerate(samples):
            logger.info(f"  Sample {i+1} (Rating {sample['label']+1}/5): {sample['text'][:100]}...")
    
    # Generate mixed domain samples
    logger.info("\nGenerating mixed domain samples...")
    mixed_samples = text_generator.generate_mixed_domain_samples(
        domains=domains_to_test,
        num_samples=2
    )
    
    for i, sample in enumerate(mixed_samples):
        logger.info(f"  Mixed sample {i+1}: {sample['text'][:100]}...")

def demo_domain_discrimination():
    """Demonstrate domain discrimination capabilities"""
    logger = logging.getLogger(__name__)
    logger.info("=== Demo: Domain Discrimination ===")
    
    # Get config
    config = get_default_config()
    
    # Initialize domain discriminator trainer
    discriminator_trainer = DomainDiscriminatorTrainer(config)
    
    # Create sample data for training
    sample_texts = {
        "Books": [
            "This novel was absolutely captivating with great character development.",
            "The plot was engaging and I couldn't put the book down.",
            "Amazing storytelling and beautiful prose throughout."
        ],
        "Electronics": [
            "The battery life on this device is excellent.",
            "Great picture quality and fast processing speed.",
            "The camera features are impressive and easy to use."
        ]
    }
    
    # Train discriminator for Books domain
    domain = "Books"
    positive_samples = sample_texts[domain]
    negative_samples = sample_texts["Electronics"]
    
    logger.info(f"Training domain discriminator for: {domain}")
    stats = discriminator_trainer.train_discriminator(
        domain=domain,
        positive_samples=positive_samples,
        negative_samples=negative_samples,
        num_epochs=2  # Quick training for demo
    )
    
    logger.info(f"Discriminator training completed. Accuracy: {stats['accuracy']:.4f}")
    
    # Test domain weights
    test_texts = [
        "This book has an amazing storyline and characters.",  # Should be high for Books
        "The smartphone has excellent battery performance."     # Should be low for Books
    ]
    
    weights = discriminator_trainer.get_domain_weights(test_texts)
    
    logger.info("Domain relevance weights:")
    for i, text in enumerate(test_texts):
        weight = weights[domain][i].item()
        logger.info(f"  Text: '{text[:50]}...' -> Weight: {weight:.4f}")

def demo_mini_federated_training():
    """Demonstrate a mini federated training process"""
    logger = logging.getLogger(__name__)
    logger.info("=== Demo: Mini Federated Training ===")
    
    # Get simplified config
    config = get_default_config()
    config.data.num_clients = 3
    config.training.local_epochs = 2
    config.training.federated_rounds = 2  # Very few for demo
    config.training.distillation_rounds = 3
    config.training.distillation_epochs = 1
    config.training.pseudo_samples_per_domain = 50
    config.experiment.output_dir = "demo_outputs"
    config.experiment.data_dir = "demo_data"

    logger.info("Starting mini D³AFD training...")
    logger.info("Following D³AFD four-stage design:")
    logger.info("  Stage 1: Local Training → Stage 2: Pseudo Data → Stage 3: DKD Distillation → Stage 4: Personalization")
    logger.info(f"Clients: {config.data.num_clients}")
    logger.info(f"Federated rounds: {config.training.federated_rounds}")
    logger.info(f"Distillation iterations per round: {config.training.distillation_rounds}")
    
    try:
        # Initialize framework
        framework = D3AFDFramework(config)
        
        # Run training (this will take some time)
        results = framework.run_complete_training(force_reload_data=False)
        
        logger.info("Mini training completed!")
        logger.info("Results summary:")
        
        if 'global_model' in results:
            global_acc = results['global_model'].get('accuracy', 0)
            logger.info(f"  Global model accuracy: {global_acc:.4f}")
        
        if 'personalized_models' in results:
            personal_accs = [r.get('accuracy', 0) for r in results['personalized_models'].values()]
            avg_personal_acc = sum(personal_accs) / len(personal_accs) if personal_accs else 0
            logger.info(f"  Average personalized accuracy: {avg_personal_acc:.4f}")
        
    except Exception as e:
        logger.error(f"Mini training failed: {e}")
        logger.info("This is normal for a quick demo - full training requires more resources")

def main():
    """Main demo function"""
    logger = setup_demo_logging()
    
    logger.info("Starting D³AFD Demo on Amazon Review Dataset")
    logger.info("=" * 60)
    
    try:
        # Demo 1: Data processing
        clients_data = demo_data_processing()
        
        # Demo 2: Text generation
        demo_text_generation()
        
        # Demo 3: Domain discrimination
        demo_domain_discrimination()
        
        # Demo 4: Mini federated training (optional - can be slow)
        user_input = input("\nRun mini federated training demo? (y/n): ")
        if user_input.lower() == 'y':
            demo_mini_federated_training()
        else:
            logger.info("Skipping mini federated training demo")
        
        logger.info("\n" + "=" * 60)
        logger.info("Demo completed successfully!")
        logger.info("To run full experiments, use: python main.py")
        
    except Exception as e:
        logger.error(f"Demo failed with error: {e}")
        logger.info("This might be due to missing dependencies or insufficient resources")
        logger.info("Please check the requirements and try again")

if __name__ == "__main__":
    main()
