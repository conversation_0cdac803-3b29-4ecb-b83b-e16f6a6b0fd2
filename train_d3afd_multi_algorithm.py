#!/usr/bin/env python3
"""
D³AFD Multi-Algorithm Training Script
Supports DKDM, DiffDFKD, and DiffKD distillation algorithms for Amazon Review sentiment analysis
"""

import os
import sys
import argparse
import logging
import yaml
import torch
import random
import numpy as np
from pathlib import Path
from datetime import datetime

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.federated.server import FederatedServer, DistillationAlgorithm
from src.federated.client import FederatedClient
from src.data.amazon_dataset import AmazonReviewDataset
from src.utils.config import Config
from src.utils.logger import setup_logger, get_logger
from src.utils.metrics import MetricsTracker

# Initialize a default logger that can be used throughout the module
logger = get_logger("d3afd_train")

def set_seed(seed):
    """Set random seed for reproducibility"""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    if torch.cuda.is_available():
        torch.backends.cudnn.deterministic = True
        torch.backends.cudnn.benchmark = False

def load_config(config_path):
    """Load configuration from YAML file"""
    with open(config_path, 'r') as f:
        config_dict = yaml.safe_load(f)
    return Config(config_dict)

def create_output_directory(config):
    """Create output directory with timestamp"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    algorithm = config.training.distillation_algorithm
    output_dir = Path(config.experiment.output_dir) / f"{algorithm}_{timestamp}"
    output_dir.mkdir(parents=True, exist_ok=True)
    return output_dir

def setup_clients(config, dataset):
    """Setup federated clients with domain-specific data"""
    clients = {}
    domains = config.data.domains
    
    logger.info(f"Setting up {len(domains)} clients for domains: {domains}")
    
    for i, domain in enumerate(domains):
        client_id = f"client_{i}_{domain}"
        
        # Create domain-specific dataset
        domain_data = dataset.get_domain_data(domain)
        
        # Create client
        client = FederatedClient(
            client_id=client_id,
            domain=domain,
            config=config,
            local_data=domain_data
        )
        
        clients[client_id] = client
        logger.info(f"Created client {client_id} with {len(domain_data)} samples")
    
    return clients

def run_experiment(config, algorithm):
    """Run D³AFD experiment with specified algorithm"""
    logger.info(f"Starting D³AFD experiment with {algorithm} algorithm")
    
    # Update config with selected algorithm
    config.training.distillation_algorithm = algorithm
    
    # Create output directory
    output_dir = create_output_directory(config)
    logger.info(f"Output directory: {output_dir}")
    
    # Load dataset
    logger.info("Loading Amazon Reviews dataset...")
    dataset = AmazonReviewDataset(config)
    
    # Setup clients
    clients = setup_clients(config, dataset)
    
    # Initialize server
    logger.info(f"Initializing federated server with {algorithm} algorithm...")
    server = FederatedServer(config)
    
    # Initialize metrics tracker
    metrics_tracker = MetricsTracker(config, output_dir)
    
    # Training loop
    logger.info("Starting federated training...")
    for round_num in range(config.training.num_rounds):
        logger.info(f"\n{'='*50}")
        logger.info(f"Round {round_num + 1}/{config.training.num_rounds}")
        logger.info(f"Algorithm: {algorithm}")
        logger.info(f"{'='*50}")
        
        try:
            # Local training phase
            logger.info("Phase 1: Local training on clients...")
            for client_id, client in clients.items():
                logger.info(f"Training client {client_id}...")
                client.local_train(server.global_model)
            
            # Distillation phase
            logger.info("Phase 2: Global distillation...")
            distillation_stats = server.distillation_round(clients, round_num)
            
            # Log metrics
            metrics_tracker.log_round_metrics(round_num, distillation_stats)
            
            # Evaluation phase
            if (round_num + 1) % config.evaluation.eval_frequency == 0:
                logger.info("Phase 3: Evaluation...")
                eval_metrics = server.evaluate_global_model(dataset.get_test_data())
                metrics_tracker.log_eval_metrics(round_num, eval_metrics)
                
                logger.info(f"Evaluation Results - Round {round_num + 1}:")
                for metric, value in eval_metrics.items():
                    logger.info(f"  {metric}: {value:.4f}")
            
            # Save checkpoint
            if (round_num + 1) % 5 == 0:
                checkpoint_path = output_dir / f"checkpoint_round_{round_num + 1}.pt"
                server.save_checkpoint(checkpoint_path)
                logger.info(f"Checkpoint saved: {checkpoint_path}")
        
        except Exception as e:
            logger.error(f"Error in round {round_num + 1}: {e}")
            continue
    
    # Final evaluation
    logger.info("\nFinal evaluation...")
    final_metrics = server.evaluate_global_model(dataset.get_test_data())
    metrics_tracker.log_final_metrics(final_metrics)
    
    logger.info("Final Results:")
    for metric, value in final_metrics.items():
        logger.info(f"  {metric}: {value:.4f}")
    
    # Save final model
    final_model_path = output_dir / "final_model.pt"
    server.save_model(final_model_path)
    logger.info(f"Final model saved: {final_model_path}")
    
    # Save metrics
    metrics_tracker.save_metrics()
    
    return final_metrics

def main():
    parser = argparse.ArgumentParser(description="D³AFD Multi-Algorithm Training")
    parser.add_argument("--config", type=str, default="configs/d3afd_multi_algorithm.yaml",
                       help="Path to configuration file")
    parser.add_argument("--algorithm", type=str, choices=["dkd", "dkdm", "diffdfkd", "diffkd"],
                       help="Distillation algorithm to use (overrides config)")
    parser.add_argument("--run_all", action="store_true",
                       help="Run experiments with all algorithms")
    parser.add_argument("--seed", type=int, default=42,
                       help="Random seed")
    parser.add_argument("--device", type=str, default="cuda",
                       help="Device to use (cuda/cpu)")
    
    args = parser.parse_args()
    
    # Load configuration
    config = load_config(args.config)
    
    # Override config with command line arguments
    if args.algorithm:
        config.training.distillation_algorithm = args.algorithm
    if args.device:
        config.experiment.device = args.device
    
    # Set seed
    set_seed(args.seed)
    
    # Setup logging - update the global logger with proper configuration
    global logger
    logger = setup_logger(config.experiment.log_level, logger_name="d3afd_train")
    
    # Check device availability
    if config.experiment.device == "cuda" and not torch.cuda.is_available():
        logger.warning("CUDA not available, switching to CPU")
        config.experiment.device = "cpu"
    
    logger.info(f"Using device: {config.experiment.device}")
    logger.info(f"Configuration loaded from: {args.config}")
    
    # Run experiments
    if args.run_all:
        algorithms = ["dkd", "dkdm", "diffdfkd", "diffkd"]
        results = {}
        
        for algorithm in algorithms:
            logger.info(f"\n{'#'*60}")
            logger.info(f"Running experiment with {algorithm.upper()} algorithm")
            logger.info(f"{'#'*60}")
            
            try:
                final_metrics = run_experiment(config, algorithm)
                results[algorithm] = final_metrics
                logger.info(f"{algorithm.upper()} experiment completed successfully")
            except Exception as e:
                logger.error(f"Error in {algorithm} experiment: {e}")
                results[algorithm] = None
        
        # Summary of all experiments
        logger.info(f"\n{'='*60}")
        logger.info("EXPERIMENT SUMMARY")
        logger.info(f"{'='*60}")
        
        for algorithm, metrics in results.items():
            if metrics:
                accuracy = metrics.get('accuracy', 'N/A')
                f1_macro = metrics.get('f1_macro', 'N/A')
                logger.info(f"{algorithm.upper():10} - Accuracy: {accuracy:.4f}, F1-Macro: {f1_macro:.4f}")
            else:
                logger.info(f"{algorithm.upper():10} - FAILED")
    
    else:
        # Run single experiment
        algorithm = config.training.distillation_algorithm
        final_metrics = run_experiment(config, algorithm)
        logger.info(f"Experiment with {algorithm.upper()} completed successfully")

if __name__ == "__main__":
    main()
