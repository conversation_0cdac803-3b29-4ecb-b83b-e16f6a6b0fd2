"""
Cross-Domain Contrastive Learning for D³AFD
Aligns features across domains for same class samples
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Tuple
import logging

logger = logging.getLogger(__name__)

class SupervisedContrastiveLoss(nn.Module):
    """Supervised contrastive loss for cross-domain alignment"""
    
    def __init__(self, temperature: float = 0.07, base_temperature: float = 0.07):
        """
        Args:
            temperature: Temperature parameter for contrastive loss
            base_temperature: Base temperature for normalization
        """
        super().__init__()
        self.temperature = temperature
        self.base_temperature = base_temperature
    
    def forward(self, features: torch.Tensor, labels: torch.Tensor,
                domains: torch.Tensor = None) -> torch.Tensor:
        """
        Compute supervised contrastive loss
        Args:
            features: Feature representations [batch_size, feature_dim]
            labels: Class labels [batch_size]
            domains: Domain labels [batch_size] (optional)
        Returns:
            Contrastive loss
        """
        device = features.device
        batch_size = features.shape[0]

        # Check and fix batch size mismatches
        if labels.shape[0] != batch_size:
            logger.warning(f'Batch size mismatch in contrastive loss: features {batch_size} vs labels {labels.shape[0]}')
            # Truncate to minimum size
            min_size = min(batch_size, labels.shape[0])
            features = features[:min_size]
            labels = labels[:min_size]
            if domains is not None:
                domains = domains[:min_size]
            batch_size = min_size

        # Handle empty batch
        if batch_size == 0:
            return torch.tensor(0.0, device=device, requires_grad=True)
        
        # Normalize features
        features = F.normalize(features, dim=1)
        
        # Compute similarity matrix
        anchor_dot_contrast = torch.div(
            torch.matmul(features, features.T),
            self.temperature
        )
        
        # For numerical stability
        logits_max, _ = torch.max(anchor_dot_contrast, dim=1, keepdim=True)
        logits = anchor_dot_contrast - logits_max.detach()
        
        # Create mask for positive pairs (same class)
        labels = labels.contiguous().view(-1, 1)
        mask = torch.eq(labels, labels.T).float().to(device)
        
        # If domains are provided, encourage cross-domain positive pairs
        if domains is not None:
            domains = domains.contiguous().view(-1, 1)
            domain_mask = torch.ne(domains, domains.T).float().to(device)
            # Boost cross-domain same-class pairs
            mask = mask * (1 + 0.5 * domain_mask)
        
        # Mask out self-contrast cases
        logits_mask = torch.scatter(
            torch.ones_like(mask),
            1,
            torch.arange(batch_size).view(-1, 1).to(device),
            0
        )
        mask = mask * logits_mask
        
        # Compute log_prob with numerical stability
        exp_logits = torch.exp(logits) * logits_mask
        exp_sum = exp_logits.sum(1, keepdim=True)

        # Avoid log(0) by adding small epsilon
        eps = 1e-8
        exp_sum = torch.clamp(exp_sum, min=eps)
        log_prob = logits - torch.log(exp_sum)

        # Compute mean of log-likelihood over positive pairs
        mask_sum = mask.sum(1)

        # Check for samples with no positive pairs (mask_sum == 0)
        valid_samples = mask_sum > 0

        if valid_samples.sum() == 0:
            # No valid positive pairs at all, return zero loss
            return torch.tensor(0.0, device=features.device, requires_grad=True)

        # Only compute loss for samples with positive pairs
        valid_mask = mask[valid_samples]
        valid_log_prob = log_prob[valid_samples]
        valid_mask_sum = mask_sum[valid_samples]

        # Compute mean log probability for valid samples
        mean_log_prob_pos = (valid_mask * valid_log_prob).sum(1) / valid_mask_sum

        # Loss
        loss = - (self.temperature / self.base_temperature) * mean_log_prob_pos
        loss = loss.mean()

        # Final NaN check
        if torch.isnan(loss) or torch.isinf(loss):
            return torch.tensor(0.0, device=features.device, requires_grad=True)

        return loss

class CrossDomainContrastiveTrainer:
    """Trainer for cross-domain contrastive learning"""
    
    def __init__(self, config):
        self.config = config
        self.device = torch.device(config.experiment.device)
        
        # Initialize contrastive loss
        self.contrastive_loss = SupervisedContrastiveLoss(
            temperature=config.training.contrastive_temperature
        )
        
        # Domain name to ID mapping
        self.domain_to_id = {domain: i for i, domain in enumerate(config.data.domains)}
    
    def compute_contrastive_loss(self, student_model: nn.Module,
                                input_batch: Dict[str, torch.Tensor],
                                pseudo_labels: torch.Tensor,
                                domains: List[str]) -> torch.Tensor:
        """
        Compute contrastive loss for a batch
        Args:
            student_model: Student model to extract features from
            input_batch: Input batch data
            pseudo_labels: Pseudo labels from ensemble teacher
            domains: Domain names for each sample
        Returns:
            Contrastive loss
        """
        # Extract features from student model
        features = self._extract_features(student_model, input_batch)

        # Check batch size consistency
        batch_size = features.shape[0]
        if pseudo_labels.shape[0] != batch_size:
            logger.warning(f"Batch size mismatch: features {batch_size} vs labels {pseudo_labels.shape[0]}")
            # Truncate to minimum size
            min_size = min(batch_size, pseudo_labels.shape[0])
            features = features[:min_size]
            pseudo_labels = pseudo_labels[:min_size]
            domains = domains[:min_size]
            batch_size = min_size

        if len(domains) != batch_size:
            logger.warning(f"Domain count mismatch: features {batch_size} vs domains {len(domains)}")
            domains = domains[:batch_size]

        # Convert domains to tensor
        try:
            domain_ids = torch.tensor([self.domain_to_id[d] for d in domains]).to(self.device)
        except KeyError as e:
            logger.warning(f"Unknown domain in batch: {e}, using default domain")
            # Use default domain (0) for unknown domains
            domain_ids = torch.tensor([self.domain_to_id.get(d, 0) for d in domains]).to(self.device)

        # Compute contrastive loss
        contrastive_loss = self.contrastive_loss(features, pseudo_labels, domain_ids)

        return contrastive_loss
    
    def _extract_features(self, model: nn.Module, input_batch: Dict[str, torch.Tensor]) -> torch.Tensor:
        """Extract feature representations from model - 内存优化版本"""
        # 分批处理以节省内存
        batch_size = input_batch['input_ids'].size(0)
        mini_batch_size = min(2, batch_size)  # 极小批次

        features_list = []

        for i in range(0, batch_size, mini_batch_size):
            end_idx = min(i + mini_batch_size, batch_size)

            mini_batch = {
                'input_ids': input_batch['input_ids'][i:end_idx],
                'attention_mask': input_batch['attention_mask'][i:end_idx]
            }

            try:
                # Get model outputs
                outputs = model(
                    input_ids=mini_batch['input_ids'],
                    attention_mask=mini_batch['attention_mask'],
                    output_hidden_states=True
                )

                # Use the last hidden state of [CLS] token as features
                # or pooled output if available
                if hasattr(outputs, 'pooler_output') and outputs.pooler_output is not None:
                    mini_features = outputs.pooler_output
                else:
                    # Use [CLS] token (first token) from last hidden state
                    mini_features = outputs.hidden_states[-1][:, 0, :]

                # 立即移到CPU节省GPU内存
                features_list.append(mini_features.cpu())

                # 清理中间变量
                del outputs, mini_features
                torch.cuda.empty_cache()

            except torch.cuda.OutOfMemoryError:
                logger.warning(f"对比学习特征提取OOM，跳过批次 {i}-{end_idx}")
                torch.cuda.empty_cache()
                # 返回零特征作为fallback
                fallback_features = torch.zeros(end_idx - i, 768)
                features_list.append(fallback_features)
                continue

        if not features_list:
            # 返回零特征作为fallback
            return torch.zeros(batch_size, 768).to(input_batch['input_ids'].device)

        # 在CPU上合并，然后移回GPU
        features = torch.cat(features_list, dim=0).to(input_batch['input_ids'].device)
        return features
    
    def create_contrastive_batches(self, pseudo_samples: List[Dict], batch_size: int = 32):
        """
        Create batches optimized for contrastive learning
        Ensures each batch has samples from multiple domains and classes
        """
        from collections import defaultdict
        import random
        
        # Group samples by class and domain
        class_domain_samples = defaultdict(lambda: defaultdict(list))
        
        for sample in pseudo_samples:
            label = sample['label']
            domain = sample['domain']
            class_domain_samples[label][domain].append(sample)
        
        # Create balanced batches
        batches = []
        remaining_samples = pseudo_samples.copy()
        
        while len(remaining_samples) >= batch_size:
            batch = []
            
            # Try to include samples from different domains and classes
            classes = list(class_domain_samples.keys())
            random.shuffle(classes)
            
            for class_label in classes:
                if len(batch) >= batch_size:
                    break
                
                domains = list(class_domain_samples[class_label].keys())
                random.shuffle(domains)
                
                for domain in domains:
                    if len(batch) >= batch_size:
                        break
                    
                    available_samples = class_domain_samples[class_label][domain]
                    if available_samples:
                        # Add one sample from this class-domain combination
                        sample = available_samples.pop(0)
                        batch.append(sample)
                        remaining_samples.remove(sample)
            
            # Fill remaining slots randomly
            while len(batch) < batch_size and remaining_samples:
                sample = remaining_samples.pop(0)
                batch.append(sample)
            
            if len(batch) == batch_size:
                batches.append(batch)
        
        return batches
    
    def compute_feature_alignment_metrics(self, student_model: nn.Module,
                                        test_samples: List[Dict]) -> Dict[str, float]:
        """
        Compute metrics to evaluate cross-domain feature alignment
        """
        from sklearn.metrics import silhouette_score
        import numpy as np
        
        # Extract features for all test samples
        all_features = []
        all_labels = []
        all_domains = []
        
        student_model.eval()
        with torch.no_grad():
            for sample in test_samples:
                # Tokenize sample
                encoding = student_model.tokenizer(
                    sample['text'],
                    truncation=True,
                    padding='max_length',
                    max_length=self.config.data.max_seq_length,
                    return_tensors='pt'
                ).to(self.device)
                
                # Extract features
                features = self._extract_features(student_model, encoding)
                
                all_features.append(features.cpu().numpy())
                all_labels.append(sample['label'])
                all_domains.append(self.domain_to_id[sample['domain']])
        
        all_features = np.vstack(all_features)
        all_labels = np.array(all_labels)
        all_domains = np.array(all_domains)
        
        # Compute alignment metrics
        metrics = {}
        
        # Class-based silhouette score (higher is better)
        try:
            class_silhouette = silhouette_score(all_features, all_labels)
            metrics['class_silhouette'] = class_silhouette
        except:
            metrics['class_silhouette'] = 0.0
        
        # Domain-based silhouette score (lower is better for domain invariance)
        try:
            domain_silhouette = silhouette_score(all_features, all_domains)
            metrics['domain_silhouette'] = domain_silhouette
        except:
            metrics['domain_silhouette'] = 0.0
        
        # Cross-domain class consistency
        cross_domain_consistency = self._compute_cross_domain_consistency(
            all_features, all_labels, all_domains
        )
        metrics['cross_domain_consistency'] = cross_domain_consistency
        
        return metrics
    
    def _compute_cross_domain_consistency(self, features: np.ndarray, 
                                        labels: np.ndarray, domains: np.ndarray) -> float:
        """
        Compute how consistent features are across domains for the same class
        """
        from scipy.spatial.distance import cosine
        import numpy as np
        
        consistencies = []
        
        for class_label in np.unique(labels):
            class_mask = labels == class_label
            class_features = features[class_mask]
            class_domains = domains[class_mask]
            
            if len(np.unique(class_domains)) < 2:
                continue  # Need at least 2 domains for this class
            
            # Compute pairwise cosine similarities within this class
            similarities = []
            for i in range(len(class_features)):
                for j in range(i + 1, len(class_features)):
                    if class_domains[i] != class_domains[j]:  # Cross-domain pairs
                        sim = 1 - cosine(class_features[i], class_features[j])
                        similarities.append(sim)
            
            if similarities:
                consistencies.append(np.mean(similarities))
        
        return np.mean(consistencies) if consistencies else 0.0
