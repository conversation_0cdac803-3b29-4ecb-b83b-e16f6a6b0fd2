"""
D³AFD experiment runner with memory optimization fixes
Automatically applies memory optimization techniques
"""
import os
import sys
import subprocess
import logging

def setup_memory_environment():
    """Setup environment variables for memory optimization"""
    # Set CUDA memory allocation configuration
    os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'expandable_segments:True,max_split_size_mb:128'
    
    # Set other memory-related environment variables
    os.environ['CUDA_LAUNCH_BLOCKING'] = '1'  # For debugging
    os.environ['PYTHONHASHSEED'] = '0'  # For reproducibility
    
    print("✅ Memory optimization environment variables set:")
    print(f"   PYTORCH_CUDA_ALLOC_CONF = {os.environ.get('PYTORCH_CUDA_ALLOC_CONF')}")
    print(f"   CUDA_LAUNCH_BLOCKING = {os.environ.get('CUDA_LAUNCH_BLOCKING')}")

def check_gpu_memory():
    """Check available GPU memory"""
    try:
        import torch
        if torch.cuda.is_available():
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
            print(f"🔍 GPU Memory Available: {gpu_memory:.2f} GB")
            
            if gpu_memory < 8:
                print("⚠️  WARNING: Less than 8GB GPU memory detected!")
                print("   Recommend using ultra-low memory configuration")
                return "ultra_low"
            elif gpu_memory < 16:
                print("⚠️  WARNING: Less than 16GB GPU memory detected!")
                print("   Recommend using memory-optimized configuration")
                return "memory_optimized"
            else:
                print("✅ Sufficient GPU memory for default configuration")
                return "default"
        else:
            print("❌ CUDA not available, will use CPU")
            return "cpu"
    except ImportError:
        print("❌ PyTorch not available")
        return "cpu"

def run_experiment(config_type="auto"):
    """Run the experiment with appropriate configuration"""
    
    if config_type == "auto":
        config_type = check_gpu_memory()
    
    print(f"\n🚀 Starting D³AFD experiment with {config_type} configuration...")
    
    if config_type == "ultra_low":
        print("   Using ultra-low memory configuration (2 clients, batch size 2)")
        cmd = [sys.executable, "run_ultra_low_memory.py"]
    elif config_type == "memory_optimized":
        print("   Using memory-optimized configuration (3 clients, batch size 4)")
        cmd = [sys.executable, "run_memory_optimized.py"]
    elif config_type == "cpu":
        print("   Using CPU-only configuration")
        cmd = [sys.executable, "run_cpu_demo.py"]
    else:
        print("   Using default configuration")
        cmd = [sys.executable, "main.py"]
    
    try:
        # Run the experiment
        result = subprocess.run(cmd, check=True, capture_output=False)
        print("✅ Experiment completed successfully!")
        return result.returncode
    except subprocess.CalledProcessError as e:
        print(f"❌ Experiment failed with return code: {e.returncode}")
        return e.returncode
    except FileNotFoundError:
        print(f"❌ Script not found: {' '.join(cmd)}")
        return 1

def main():
    """Main function"""
    print("=" * 60)
    print("🔧 D³AFD Memory-Optimized Experiment Runner")
    print("=" * 60)
    
    # Parse command line arguments
    import argparse
    parser = argparse.ArgumentParser(description="Run D³AFD experiment with memory optimization")
    parser.add_argument('--config', choices=['auto', 'ultra_low', 'memory_optimized', 'cpu', 'default'], 
                       default='auto', help='Configuration type to use')
    parser.add_argument('--check-only', action='store_true', help='Only check GPU memory, do not run experiment')
    
    args = parser.parse_args()
    
    # Setup memory environment
    setup_memory_environment()
    
    if args.check_only:
        config_type = check_gpu_memory()
        print(f"\n💡 Recommended configuration: {config_type}")
        return 0
    
    # Run experiment
    return run_experiment(args.config)

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
