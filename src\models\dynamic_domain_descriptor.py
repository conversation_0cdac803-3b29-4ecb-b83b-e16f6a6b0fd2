"""
动态域描述符生成器
基于本地教师模型的知识状态生成域特征描述符
实现真正的动态联邦学习
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Tuple, Optional
import logging
from sklearn.cluster import KMeans
from sklearn.decomposition import PCA

logger = logging.getLogger(__name__)

class DynamicDomainDescriptor:
    """基于教师模型动态生成域描述符"""
    
    def __init__(self, config, teacher_model: nn.Module):
        self.config = config
        self.teacher_model = teacher_model
        self.device = next(teacher_model.parameters()).device
        
        # 域描述符维度 - 减少以节省内存
        self.descriptor_dim = 64
        
        # 特征提取器 (使用教师模型的中间层)
        self.feature_extractor = self._build_feature_extractor()
        
        # 域聚类器
        self.domain_clusterer = None
        self.domain_centers = {}
        
        logger.info("动态域描述符生成器初始化完成")
    
    def _build_feature_extractor(self) -> nn.Module:
        """构建特征提取器"""
        # 使用教师模型的编码器部分
        if hasattr(self.teacher_model, 'bert'):
            # BERT-based模型
            encoder = self.teacher_model.bert
        elif hasattr(self.teacher_model, 'encoder'):
            # 自定义编码器
            encoder = self.teacher_model.encoder
        else:
            # 回退到整个模型
            encoder = self.teacher_model
        
        # 添加投影层将特征映射到描述符维度
        hidden_size = encoder.config.hidden_size if hasattr(encoder, 'config') else 768
        
        projector = nn.Sequential(
            nn.Linear(hidden_size, 256),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(256, self.descriptor_dim),
            nn.Tanh()  # 归一化到[-1, 1]
        ).to(self.device)
        
        return nn.Sequential(encoder, projector)
    
    def extract_domain_features(self, dataloader, max_samples: int = 200) -> torch.Tensor:
        """从数据中提取域特征 - 优化内存使用"""
        logger.info(f"提取域特征，最大样本数: {max_samples}")

        self.teacher_model.eval()
        features = []
        sample_count = 0
        batch_size = 8  # 减小批次大小

        with torch.no_grad():
            for batch in dataloader:
                if sample_count >= max_samples:
                    break

                # 分批处理以节省内存
                input_ids = batch['input_ids']
                attention_mask = batch['attention_mask']

                # 进一步分割批次
                for i in range(0, input_ids.size(0), batch_size):
                    if sample_count >= max_samples:
                        break

                    end_idx = min(i + batch_size, input_ids.size(0))
                    mini_input_ids = input_ids[i:end_idx].to(self.device)
                    mini_attention_mask = attention_mask[i:end_idx].to(self.device)

                    try:
                        # 获取编码器输出
                        if hasattr(self.teacher_model, 'bert'):
                            encoder_outputs = self.teacher_model.bert(
                                input_ids=mini_input_ids,
                                attention_mask=mini_attention_mask
                            )
                            # 使用[CLS]标记的表示
                            batch_features = encoder_outputs.last_hidden_state[:, 0, :]
                        else:
                            # 使用整个模型的隐藏状态
                            outputs = self.teacher_model(
                                input_ids=mini_input_ids,
                                attention_mask=mini_attention_mask,
                                output_hidden_states=True
                            )
                            batch_features = outputs.hidden_states[-1][:, 0, :]

                        # 投影到描述符空间
                        projected_features = self.feature_extractor[1](batch_features)

                        # 立即移到CPU以节省GPU内存
                        features.append(projected_features.cpu())

                        sample_count += mini_input_ids.size(0)

                        # 清理GPU内存
                        del mini_input_ids, mini_attention_mask, batch_features, projected_features
                        if hasattr(self, 'encoder_outputs'):
                            del encoder_outputs
                        torch.cuda.empty_cache()

                    except torch.cuda.OutOfMemoryError:
                        logger.warning(f"GPU内存不足，跳过批次 {i}")
                        torch.cuda.empty_cache()
                        continue

        if not features:
            logger.warning("未提取到任何特征，返回随机特征")
            return torch.randn(1, self.descriptor_dim).to(self.device)

        # 在CPU上合并特征，然后移到GPU
        features = torch.cat(features, dim=0).to(self.device)
        logger.info(f"提取了 {features.size(0)} 个样本的特征")

        return features
    
    def generate_domain_descriptor(self, client_id: int, dataloader, 
                                 round_num: int) -> Dict[str, torch.Tensor]:
        """为客户端生成动态域描述符"""
        logger.info(f"为客户端 {client_id} 生成第 {round_num} 轮域描述符")
        
        # 提取当前教师模型的域特征
        domain_features = self.extract_domain_features(dataloader)
        
        # 计算域中心
        domain_center = torch.mean(domain_features, dim=0)
        
        # 计算域方差 (表示域的多样性)
        domain_variance = torch.var(domain_features, dim=0)
        
        # 计算域边界 (表示域的范围)
        domain_min = torch.min(domain_features, dim=0)[0]
        domain_max = torch.max(domain_features, dim=0)[0]
        domain_range = domain_max - domain_min
        
        # 计算域密度 (基于特征聚集程度)
        distances = torch.cdist(domain_features, domain_center.unsqueeze(0))
        domain_density = 1.0 / (torch.mean(distances) + 1e-8)
        
        # 生成多层次域描述符
        descriptor = {
            'center': domain_center,  # 域中心 [descriptor_dim]
            'variance': domain_variance,  # 域方差 [descriptor_dim]
            'range': domain_range,  # 域范围 [descriptor_dim]
            'density': domain_density,  # 域密度 [1]
            'round': round_num,  # 轮次信息
            'client_id': client_id,  # 客户端ID
            'sample_count': domain_features.size(0)  # 样本数量
        }
        
        # 存储历史描述符用于演化分析
        self.domain_centers[f"client_{client_id}_round_{round_num}"] = domain_center
        
        logger.info(f"客户端 {client_id} 域描述符生成完成:")
        logger.info(f"  域中心范数: {torch.norm(domain_center):.4f}")
        logger.info(f"  域方差均值: {torch.mean(domain_variance):.4f}")
        logger.info(f"  域密度: {domain_density:.4f}")
        
        return descriptor
    
    def compute_domain_similarity(self, desc1: Dict, desc2: Dict) -> float:
        """计算两个域描述符之间的相似度"""
        # 中心距离相似度
        center_sim = F.cosine_similarity(
            desc1['center'].unsqueeze(0), 
            desc2['center'].unsqueeze(0)
        ).item()
        
        # 方差相似度
        var_sim = F.cosine_similarity(
            desc1['variance'].unsqueeze(0), 
            desc2['variance'].unsqueeze(0)
        ).item()
        
        # 范围相似度
        range_sim = F.cosine_similarity(
            desc1['range'].unsqueeze(0), 
            desc2['range'].unsqueeze(0)
        ).item()
        
        # 密度相似度
        density_sim = 1.0 - abs(desc1['density'] - desc2['density']) / (
            desc1['density'] + desc2['density'] + 1e-8
        )
        
        # 加权平均
        total_sim = (center_sim * 0.4 + var_sim * 0.3 + 
                    range_sim * 0.2 + density_sim * 0.1)
        
        return total_sim
    
    def analyze_domain_evolution(self, client_id: int, current_round: int) -> Dict:
        """分析域描述符的演化趋势"""
        evolution_analysis = {
            'stability': 0.0,
            'learning_progress': 0.0,
            'convergence_trend': 'unknown'
        }
        
        if current_round < 2:
            return evolution_analysis
        
        # 获取历史描述符
        current_key = f"client_{client_id}_round_{current_round}"
        prev_key = f"client_{client_id}_round_{current_round-1}"
        
        if current_key not in self.domain_centers or prev_key not in self.domain_centers:
            return evolution_analysis
        
        current_center = self.domain_centers[current_key]
        prev_center = self.domain_centers[prev_key]
        
        # 计算稳定性 (中心变化程度)
        center_change = torch.norm(current_center - prev_center).item()
        evolution_analysis['stability'] = max(0, 1.0 - center_change)
        
        # 计算学习进展 (特征空间的移动方向性)
        if current_round >= 3:
            prev_prev_key = f"client_{client_id}_round_{current_round-2}"
            if prev_prev_key in self.domain_centers:
                prev_prev_center = self.domain_centers[prev_prev_key]
                
                # 计算移动方向的一致性
                move1 = prev_center - prev_prev_center
                move2 = current_center - prev_center
                
                direction_consistency = F.cosine_similarity(
                    move1.unsqueeze(0), move2.unsqueeze(0)
                ).item()
                
                evolution_analysis['learning_progress'] = max(0, direction_consistency)
                
                # 判断收敛趋势
                if direction_consistency > 0.8 and center_change < 0.1:
                    evolution_analysis['convergence_trend'] = 'converging'
                elif direction_consistency < -0.5:
                    evolution_analysis['convergence_trend'] = 'oscillating'
                else:
                    evolution_analysis['convergence_trend'] = 'learning'
        
        logger.info(f"客户端 {client_id} 域演化分析:")
        logger.info(f"  稳定性: {evolution_analysis['stability']:.4f}")
        logger.info(f"  学习进展: {evolution_analysis['learning_progress']:.4f}")
        logger.info(f"  收敛趋势: {evolution_analysis['convergence_trend']}")
        
        return evolution_analysis
    
    def generate_adaptive_prompts(self, descriptor: Dict, domain_name: str) -> List[str]:
        """基于域描述符生成自适应提示"""
        # 分析域特征
        center_norm = torch.norm(descriptor['center']).item()
        variance_mean = torch.mean(descriptor['variance']).item()
        density = descriptor['density'].item()
        
        # 基于域特征调整提示策略
        if density > 1.0:  # 高密度域 - 特征集中
            style_modifier = "focused and specific"
        elif variance_mean > 0.5:  # 高方差域 - 特征多样
            style_modifier = "diverse and varied"
        else:  # 平衡域
            style_modifier = "balanced and typical"
        
        if center_norm > 1.0:  # 强特征域
            intensity_modifier = "strong and distinctive"
        else:  # 温和特征域
            intensity_modifier = "subtle and nuanced"
        
        # 生成自适应提示
        adaptive_prompts = [
            f"Generate a {style_modifier} {domain_name.lower()} review with {intensity_modifier} characteristics:",
            f"Create a {intensity_modifier} {domain_name.lower()} review that is {style_modifier}:",
            f"Write a {domain_name.lower()} review with {style_modifier} features and {intensity_modifier} tone:"
        ]
        
        return adaptive_prompts
    
    def get_descriptor_summary(self, descriptor: Dict) -> str:
        """获取域描述符的可读摘要"""
        center_norm = torch.norm(descriptor['center']).item()
        variance_mean = torch.mean(descriptor['variance']).item()
        density = descriptor['density'].item()
        
        summary = f"Domain Descriptor Summary:\n"
        summary += f"  Center Strength: {center_norm:.4f}\n"
        summary += f"  Feature Diversity: {variance_mean:.4f}\n"
        summary += f"  Domain Density: {density:.4f}\n"
        summary += f"  Sample Count: {descriptor['sample_count']}\n"
        summary += f"  Round: {descriptor['round']}\n"
        
        return summary
