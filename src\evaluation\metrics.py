"""
Evaluation metrics and comparison utilities
"""
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Any, Optional
import pandas as pd
from sklearn.metrics import classification_report, confusion_matrix
import logging

logger = logging.getLogger(__name__)

class ExperimentEvaluator:
    """Comprehensive evaluation of D³AFD and baseline methods"""
    
    def __init__(self, config):
        self.config = config
        self.results = {}
        
    def add_method_results(self, method_name: str, results: Dict[str, Any]):
        """Add results for a specific method"""
        self.results[method_name] = results
        logger.info(f"Added results for method: {method_name}")
    
    def compare_global_performance(self) -> Dict[str, Any]:
        """Compare global model performance across methods"""
        comparison = {
            'final_accuracy': {},
            'final_f1_macro': {},
            'final_f1_weighted': {},
            'convergence_rounds': {},
            'training_stability': {}
        }
        
        for method_name, results in self.results.items():
            if 'global_model' in results:
                global_results = results['global_model']
                comparison['final_accuracy'][method_name] = global_results.get('accuracy', 0)
                comparison['final_f1_macro'][method_name] = global_results.get('f1_macro', 0)
                comparison['final_f1_weighted'][method_name] = global_results.get('f1_weighted', 0)
            
            # Analyze convergence from training history
            if 'training_history' in results:
                history = results['training_history']
                if history:
                    # Find convergence point (when accuracy stops improving significantly)
                    accuracies = [round_data.get('accuracy', 0) for round_data in history]
                    convergence_round = self._find_convergence_point(accuracies)
                    comparison['convergence_rounds'][method_name] = convergence_round
                    
                    # Calculate training stability (variance in later rounds)
                    if len(accuracies) > 5:
                        stability = np.std(accuracies[-5:])  # Std of last 5 rounds
                        comparison['training_stability'][method_name] = stability
        
        return comparison
    
    def compare_personalization_performance(self) -> Dict[str, Any]:
        """Compare personalized model performance"""
        comparison = {
            'avg_personalized_accuracy': {},
            'personalization_improvement': {},
            'domain_consistency': {}
        }
        
        for method_name, results in self.results.items():
            if 'personalized_models' in results:
                personalized_results = results['personalized_models']
                
                # Calculate average personalized accuracy
                accuracies = [client_results.get('accuracy', 0) 
                            for client_results in personalized_results.values()]
                avg_accuracy = np.mean(accuracies) if accuracies else 0
                comparison['avg_personalized_accuracy'][method_name] = avg_accuracy
                
                # Calculate improvement over global model
                if 'global_model' in results:
                    global_accuracy = results['global_model'].get('accuracy', 0)
                    improvement = avg_accuracy - global_accuracy
                    comparison['personalization_improvement'][method_name] = improvement
                
                # Calculate domain consistency (lower variance is better)
                consistency = np.std(accuracies) if len(accuracies) > 1 else 0
                comparison['domain_consistency'][method_name] = consistency
        
        return comparison
    
    def analyze_domain_performance(self) -> Dict[str, Any]:
        """Analyze performance across different domains"""
        domain_analysis = {}
        
        # This would require domain-specific evaluation data
        # For now, we'll provide a framework
        
        for method_name, results in self.results.items():
            if 'domain_analysis' in results:
                domain_analysis[method_name] = results['domain_analysis']
        
        return domain_analysis
    
    def calculate_communication_efficiency(self) -> Dict[str, Any]:
        """Calculate communication efficiency metrics"""
        efficiency = {
            'total_communication_rounds': {},
            'convergence_efficiency': {},
            'parameter_efficiency': {}
        }
        
        for method_name, results in self.results.items():
            # Total rounds to convergence
            if 'training_history' in results:
                history = results['training_history']
                total_rounds = len(history)
                efficiency['total_communication_rounds'][method_name] = total_rounds
                
                # Convergence efficiency (accuracy per round)
                if history:
                    final_accuracy = history[-1].get('accuracy', 0)
                    convergence_eff = final_accuracy / total_rounds if total_rounds > 0 else 0
                    efficiency['convergence_efficiency'][method_name] = convergence_eff
        
        return efficiency
    
    def _find_convergence_point(self, accuracies: List[float], 
                               threshold: float = 0.001) -> int:
        """Find the round where model converged"""
        if len(accuracies) < 3:
            return len(accuracies)
        
        # Look for point where improvement becomes minimal
        for i in range(2, len(accuracies)):
            recent_improvement = accuracies[i] - accuracies[i-2]
            if recent_improvement < threshold:
                return i
        
        return len(accuracies)
    
    def generate_comparison_report(self) -> str:
        """Generate comprehensive comparison report"""
        report = []
        report.append("=" * 60)
        report.append("D³AFD EXPERIMENTAL EVALUATION REPORT")
        report.append("=" * 60)
        
        # Global performance comparison
        global_comp = self.compare_global_performance()
        report.append("\n1. GLOBAL MODEL PERFORMANCE")
        report.append("-" * 30)
        
        for metric, values in global_comp.items():
            if values:
                report.append(f"\n{metric.upper()}:")
                for method, value in values.items():
                    report.append(f"  {method}: {value:.4f}")
        
        # Personalization comparison
        personal_comp = self.compare_personalization_performance()
        report.append("\n\n2. PERSONALIZATION PERFORMANCE")
        report.append("-" * 35)
        
        for metric, values in personal_comp.items():
            if values:
                report.append(f"\n{metric.upper()}:")
                for method, value in values.items():
                    report.append(f"  {method}: {value:.4f}")
        
        # Communication efficiency
        comm_eff = self.calculate_communication_efficiency()
        report.append("\n\n3. COMMUNICATION EFFICIENCY")
        report.append("-" * 30)
        
        for metric, values in comm_eff.items():
            if values:
                report.append(f"\n{metric.upper()}:")
                for method, value in values.items():
                    report.append(f"  {method}: {value:.4f}")
        
        # Summary and conclusions
        report.append("\n\n4. SUMMARY")
        report.append("-" * 15)
        
        # Find best performing method
        if global_comp['final_accuracy']:
            best_method = max(global_comp['final_accuracy'], 
                            key=global_comp['final_accuracy'].get)
            best_accuracy = global_comp['final_accuracy'][best_method]
            report.append(f"\nBest Global Accuracy: {best_method} ({best_accuracy:.4f})")
        
        if personal_comp['avg_personalized_accuracy']:
            best_personal = max(personal_comp['avg_personalized_accuracy'],
                              key=personal_comp['avg_personalized_accuracy'].get)
            best_personal_acc = personal_comp['avg_personalized_accuracy'][best_personal]
            report.append(f"Best Personalized Accuracy: {best_personal} ({best_personal_acc:.4f})")
        
        return "\n".join(report)
    
    def plot_training_curves(self, save_path: Optional[str] = None):
        """Plot training curves for all methods"""
        plt.figure(figsize=(15, 10))
        
        # Plot 1: Global accuracy over rounds
        plt.subplot(2, 2, 1)
        for method_name, results in self.results.items():
            if 'training_history' in results:
                history = results['training_history']
                rounds = [r.get('round', i+1) for i, r in enumerate(history)]
                accuracies = [r.get('accuracy', 0) for r in history]
                plt.plot(rounds, accuracies, marker='o', label=method_name)
        
        plt.xlabel('Communication Round')
        plt.ylabel('Global Accuracy')
        plt.title('Global Model Accuracy Over Time')
        plt.legend()
        plt.grid(True)
        
        # Plot 2: Loss curves
        plt.subplot(2, 2, 2)
        for method_name, results in self.results.items():
            if 'training_history' in results:
                history = results['training_history']
                rounds = [r.get('round', i+1) for i, r in enumerate(history)]
                losses = [r.get('loss', 0) for r in history]
                plt.plot(rounds, losses, marker='s', label=method_name)
        
        plt.xlabel('Communication Round')
        plt.ylabel('Loss')
        plt.title('Training Loss Over Time')
        plt.legend()
        plt.grid(True)
        
        # Plot 3: F1 scores comparison
        plt.subplot(2, 2, 3)
        methods = list(self.results.keys())
        f1_macros = []
        f1_weighteds = []
        
        for method in methods:
            if 'global_model' in self.results[method]:
                f1_macros.append(self.results[method]['global_model'].get('f1_macro', 0))
                f1_weighteds.append(self.results[method]['global_model'].get('f1_weighted', 0))
            else:
                f1_macros.append(0)
                f1_weighteds.append(0)
        
        x = np.arange(len(methods))
        width = 0.35
        
        plt.bar(x - width/2, f1_macros, width, label='F1-Macro')
        plt.bar(x + width/2, f1_weighteds, width, label='F1-Weighted')
        
        plt.xlabel('Methods')
        plt.ylabel('F1 Score')
        plt.title('F1 Score Comparison')
        plt.xticks(x, methods, rotation=45)
        plt.legend()
        
        # Plot 4: Personalization improvement
        plt.subplot(2, 2, 4)
        personal_comp = self.compare_personalization_performance()
        
        if personal_comp['personalization_improvement']:
            methods = list(personal_comp['personalization_improvement'].keys())
            improvements = list(personal_comp['personalization_improvement'].values())
            
            colors = ['green' if imp > 0 else 'red' for imp in improvements]
            plt.bar(methods, improvements, color=colors)
            plt.xlabel('Methods')
            plt.ylabel('Accuracy Improvement')
            plt.title('Personalization Improvement Over Global Model')
            plt.xticks(rotation=45)
            plt.axhline(y=0, color='black', linestyle='-', alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Training curves saved to {save_path}")
        
        plt.show()
    
    def save_detailed_results(self, save_path: str):
        """Save detailed results to file"""
        import json
        
        # Convert results to JSON-serializable format
        serializable_results = {}
        for method, results in self.results.items():
            serializable_results[method] = self._make_serializable(results)
        
        with open(save_path, 'w') as f:
            json.dump(serializable_results, f, indent=2, default=str)
        
        logger.info(f"Detailed results saved to {save_path}")
    
    def _make_serializable(self, obj):
        """Convert object to JSON-serializable format"""
        if isinstance(obj, dict):
            return {k: self._make_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._make_serializable(item) for item in obj]
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, (np.int64, np.int32)):
            return int(obj)
        elif isinstance(obj, (np.float64, np.float32)):
            return float(obj)
        else:
            return obj
