"""
Baseline methods for comparison with D³AFD
"""
import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from typing import Dict, List, Optional, Any
import copy
import logging
import numpy as np

from ..models.base_models import TeacherModel, StudentModel
from ..federated.client import FederatedClient

logger = logging.getLogger(__name__)

class FedAvgBaseline:
    """FedAvg baseline implementation"""
    
    def __init__(self, config):
        self.config = config
        self.device = torch.device(config.experiment.device)
        self.global_model = TeacherModel(config).to(self.device)
        self.training_history = []
    
    def train(self, clients: Dict[int, FederatedClient], num_rounds: int = 10) -> Dict[str, Any]:
        """Train using FedAvg algorithm"""
        logger.info("Starting FedAvg training...")
        
        for round_num in range(1, num_rounds + 1):
            logger.info(f"FedAvg Round {round_num}")
            
            # Client updates
            client_weights = []
            client_sizes = []
            
            for client_id, client in clients.items():
                # Download global model
                client.teacher_model.load_state_dict(self.global_model.state_dict())
                
                # Local training
                client.train_local_teacher(num_epochs=self.config.training.local_epochs)
                
                # Upload weights
                client_weights.append(copy.deepcopy(client.teacher_model.state_dict()))
                
                # Get client data size for weighted averaging
                client_size = len(client.train_dataloader.dataset)
                client_sizes.append(client_size)
            
            # Server aggregation (weighted average)
            self._aggregate_weights(client_weights, client_sizes)
            
            # Evaluate global model
            if round_num % 2 == 0:
                results = self._evaluate_global_model(clients)
                results['round'] = round_num
                self.training_history.append(results)
                
                logger.info(f"FedAvg Round {round_num}: Accuracy {results['accuracy']:.4f}")
        
        return {'training_history': self.training_history}
    
    def _aggregate_weights(self, client_weights: List[Dict], client_sizes: List[int]):
        """Aggregate client weights using weighted averaging"""
        total_size = sum(client_sizes)
        
        # Initialize aggregated weights
        aggregated_weights = {}
        
        # Get parameter names from first client
        param_names = client_weights[0].keys()
        
        for param_name in param_names:
            # Weighted average of parameters
            weighted_sum = torch.zeros_like(client_weights[0][param_name])
            
            for client_weight, client_size in zip(client_weights, client_sizes):
                weight = client_size / total_size
                weighted_sum += weight * client_weight[param_name]
            
            aggregated_weights[param_name] = weighted_sum
        
        # Update global model
        self.global_model.load_state_dict(aggregated_weights)
    
    def _evaluate_global_model(self, clients: Dict[int, FederatedClient]) -> Dict[str, float]:
        """Evaluate global model on combined test data"""
        self.global_model.eval()
        
        total_loss = 0
        total_correct = 0
        total_samples = 0
        all_predictions = []
        all_labels = []
        
        criterion = nn.CrossEntropyLoss()
        
        with torch.no_grad():
            for client in clients.values():
                for batch in client.test_dataloader:
                    input_ids = batch['input_ids'].to(self.device)
                    attention_mask = batch['attention_mask'].to(self.device)
                    labels = batch['labels'].to(self.device)
                    
                    outputs = self.global_model(
                        input_ids=input_ids,
                        attention_mask=attention_mask
                    )
                    
                    loss = criterion(outputs.logits, labels)
                    total_loss += loss.item()
                    
                    predictions = torch.argmax(outputs.logits, dim=1)
                    total_correct += (predictions == labels).sum().item()
                    total_samples += labels.size(0)
                    
                    all_predictions.extend(predictions.cpu().numpy())
                    all_labels.extend(labels.cpu().numpy())
        
        accuracy = total_correct / total_samples
        avg_loss = total_loss / total_samples
        
        # Calculate F1 scores
        from sklearn.metrics import f1_score
        f1_macro = f1_score(all_labels, all_predictions, average='macro')
        f1_weighted = f1_score(all_labels, all_predictions, average='weighted')
        
        return {
            'loss': avg_loss,
            'accuracy': accuracy,
            'f1_macro': f1_macro,
            'f1_weighted': f1_weighted
        }

class FedProxBaseline:
    """FedProx baseline implementation"""
    
    def __init__(self, config, mu: float = 0.01):
        self.config = config
        self.device = torch.device(config.experiment.device)
        self.global_model = TeacherModel(config).to(self.device)
        self.mu = mu  # Proximal term coefficient
        self.training_history = []
    
    def train(self, clients: Dict[int, FederatedClient], num_rounds: int = 10) -> Dict[str, Any]:
        """Train using FedProx algorithm"""
        logger.info("Starting FedProx training...")
        
        for round_num in range(1, num_rounds + 1):
            logger.info(f"FedProx Round {round_num}")
            
            # Client updates with proximal term
            client_weights = []
            client_sizes = []
            
            for client_id, client in clients.items():
                # Download global model
                global_weights = copy.deepcopy(self.global_model.state_dict())
                client.teacher_model.load_state_dict(global_weights)
                
                # Local training with proximal term
                self._train_client_with_proximal_term(client, global_weights)
                
                # Upload weights
                client_weights.append(copy.deepcopy(client.teacher_model.state_dict()))
                
                # Get client data size
                client_size = len(client.train_dataloader.dataset)
                client_sizes.append(client_size)
            
            # Server aggregation
            self._aggregate_weights(client_weights, client_sizes)
            
            # Evaluate
            if round_num % 2 == 0:
                results = self._evaluate_global_model(clients)
                results['round'] = round_num
                self.training_history.append(results)
                
                logger.info(f"FedProx Round {round_num}: Accuracy {results['accuracy']:.4f}")
        
        return {'training_history': self.training_history}
    
    def _train_client_with_proximal_term(self, client: FederatedClient, global_weights: Dict):
        """Train client with proximal term"""
        client.teacher_model.train()
        optimizer = torch.optim.AdamW(
            client.teacher_model.parameters(),
            lr=self.config.training.local_lr
        )
        criterion = nn.CrossEntropyLoss()
        
        for epoch in range(self.config.training.local_epochs):
            for batch in client.train_dataloader:
                input_ids = batch['input_ids'].to(self.device)
                attention_mask = batch['attention_mask'].to(self.device)
                labels = batch['labels'].to(self.device)
                
                # Forward pass
                outputs = client.teacher_model(
                    input_ids=input_ids,
                    attention_mask=attention_mask
                )
                
                # Standard loss
                loss = criterion(outputs.logits, labels)
                
                # Proximal term
                proximal_term = 0
                for name, param in client.teacher_model.named_parameters():
                    if name in global_weights:
                        proximal_term += torch.norm(param - global_weights[name]) ** 2
                
                total_loss = loss + (self.mu / 2) * proximal_term
                
                # Backward pass
                optimizer.zero_grad()
                total_loss.backward()
                optimizer.step()
    
    def _aggregate_weights(self, client_weights: List[Dict], client_sizes: List[int]):
        """Same as FedAvg aggregation"""
        total_size = sum(client_sizes)
        aggregated_weights = {}
        param_names = client_weights[0].keys()
        
        for param_name in param_names:
            weighted_sum = torch.zeros_like(client_weights[0][param_name])
            for client_weight, client_size in zip(client_weights, client_sizes):
                weight = client_size / total_size
                weighted_sum += weight * client_weight[param_name]
            aggregated_weights[param_name] = weighted_sum
        
        self.global_model.load_state_dict(aggregated_weights)
    
    def _evaluate_global_model(self, clients: Dict[int, FederatedClient]) -> Dict[str, float]:
        """Same as FedAvg evaluation"""
        # Implementation identical to FedAvg
        return FedAvgBaseline._evaluate_global_model(self, clients)

class FedDFBaseline:
    """FedDF (Federated Distillation) baseline implementation"""
    
    def __init__(self, config):
        self.config = config
        self.device = torch.device(config.experiment.device)
        self.global_model = StudentModel(config).to(self.device)
        self.training_history = []
    
    def train(self, clients: Dict[int, FederatedClient], num_rounds: int = 10) -> Dict[str, Any]:
        """Train using FedDF algorithm"""
        logger.info("Starting FedDF training...")
        
        # First, train local models
        for client in clients.values():
            client.train_local_teacher()
        
        # Generate synthetic data (simplified - using random data)
        synthetic_data = self._generate_synthetic_data()
        
        for round_num in range(1, num_rounds + 1):
            logger.info(f"FedDF Round {round_num}")
            
            # Collect teacher predictions
            teacher_predictions = self._collect_teacher_predictions(clients, synthetic_data)
            
            # Train global model via distillation
            self._distill_global_model(teacher_predictions, synthetic_data)
            
            # Evaluate
            if round_num % 2 == 0:
                results = self._evaluate_global_model(clients)
                results['round'] = round_num
                self.training_history.append(results)
                
                logger.info(f"FedDF Round {round_num}: Accuracy {results['accuracy']:.4f}")
        
        return {'training_history': self.training_history}
    
    def _generate_synthetic_data(self) -> List[Dict]:
        """Generate synthetic data (simplified implementation)"""
        # In practice, this would use a generative model
        # For now, we'll use a simple approach
        synthetic_samples = []
        
        # Generate random text-like data
        vocab_size = 1000
        seq_length = 128
        num_samples = 1000
        
        for _ in range(num_samples):
            # Random token IDs
            input_ids = torch.randint(0, vocab_size, (seq_length,))
            attention_mask = torch.ones(seq_length)
            
            synthetic_samples.append({
                'input_ids': input_ids,
                'attention_mask': attention_mask
            })
        
        return synthetic_samples
    
    def _collect_teacher_predictions(self, clients: Dict[int, FederatedClient], 
                                   synthetic_data: List[Dict]) -> torch.Tensor:
        """Collect predictions from teacher models"""
        all_predictions = []
        
        for client in clients.values():
            client.teacher_model.eval()
            predictions = []
            
            with torch.no_grad():
                for sample in synthetic_data:
                    input_ids = sample['input_ids'].unsqueeze(0).to(self.device)
                    attention_mask = sample['attention_mask'].unsqueeze(0).to(self.device)
                    
                    outputs = client.teacher_model(
                        input_ids=input_ids,
                        attention_mask=attention_mask
                    )
                    predictions.append(outputs.logits)
            
            all_predictions.append(torch.cat(predictions, dim=0))
        
        # Average predictions
        ensemble_predictions = torch.stack(all_predictions).mean(dim=0)
        return ensemble_predictions
    
    def _distill_global_model(self, teacher_predictions: torch.Tensor, 
                            synthetic_data: List[Dict]):
        """Distill knowledge into global model"""
        self.global_model.train()
        optimizer = torch.optim.AdamW(
            self.global_model.parameters(),
            lr=self.config.training.distillation_lr
        )
        
        batch_size = 32
        num_epochs = 3
        
        for epoch in range(num_epochs):
            for i in range(0, len(synthetic_data), batch_size):
                batch_data = synthetic_data[i:i+batch_size]
                batch_teacher_preds = teacher_predictions[i:i+batch_size]
                
                # Prepare batch
                input_ids = torch.stack([sample['input_ids'] for sample in batch_data]).to(self.device)
                attention_mask = torch.stack([sample['attention_mask'] for sample in batch_data]).to(self.device)
                
                # Student predictions
                student_outputs = self.global_model(
                    input_ids=input_ids,
                    attention_mask=attention_mask
                )
                
                # KL divergence loss
                loss = nn.functional.kl_div(
                    nn.functional.log_softmax(student_outputs.logits, dim=1),
                    nn.functional.softmax(batch_teacher_preds, dim=1),
                    reduction='batchmean'
                )
                
                # Backward pass
                optimizer.zero_grad()
                loss.backward()
                optimizer.step()
    
    def _evaluate_global_model(self, clients: Dict[int, FederatedClient]) -> Dict[str, float]:
        """Evaluate global model"""
        # Similar to FedAvg evaluation but using student model
        self.global_model.eval()
        
        total_loss = 0
        total_correct = 0
        total_samples = 0
        all_predictions = []
        all_labels = []
        
        criterion = nn.CrossEntropyLoss()
        
        with torch.no_grad():
            for client in clients.values():
                for batch in client.test_dataloader:
                    input_ids = batch['input_ids'].to(self.device)
                    attention_mask = batch['attention_mask'].to(self.device)
                    labels = batch['labels'].to(self.device)
                    
                    outputs = self.global_model(
                        input_ids=input_ids,
                        attention_mask=attention_mask
                    )
                    
                    loss = criterion(outputs.logits, labels)
                    total_loss += loss.item()
                    
                    predictions = torch.argmax(outputs.logits, dim=1)
                    total_correct += (predictions == labels).sum().item()
                    total_samples += labels.size(0)
                    
                    all_predictions.extend(predictions.cpu().numpy())
                    all_labels.extend(labels.cpu().numpy())
        
        accuracy = total_correct / total_samples
        avg_loss = total_loss / total_samples
        
        from sklearn.metrics import f1_score
        f1_macro = f1_score(all_labels, all_predictions, average='macro')
        f1_weighted = f1_score(all_labels, all_predictions, average='weighted')
        
        return {
            'loss': avg_loss,
            'accuracy': accuracy,
            'f1_macro': f1_macro,
            'f1_weighted': f1_weighted
        }
