"""
Test script to verify contrastive learning fixes
"""
import torch
import torch.nn.functional as F
import sys
import os

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.models.contrastive_learning import SupervisedContrastiveLoss, CrossDomainContrastiveTrainer

def test_contrastive_loss_batch_mismatch():
    """Test contrastive loss with batch size mismatches"""
    print("🧪 Testing Contrastive Loss with batch size mismatches...")
    
    contrastive_loss = SupervisedContrastiveLoss(temperature=0.07)
    
    test_cases = [
        (8, 8),    # Normal case
        (8, 6),    # More features than labels
        (6, 8),    # More labels than features
        (1, 1),    # Single sample
        (0, 0),    # Empty batch
    ]
    
    for feat_size, label_size in test_cases:
        print(f"\n📊 Testing features={feat_size}, labels={label_size}")
        
        if feat_size > 0 and label_size > 0:
            features = F.normalize(torch.randn(feat_size, 128), dim=1)
            labels = torch.randint(0, 5, (label_size,))
        else:
            features = torch.empty(0, 128)
            labels = torch.empty(0, dtype=torch.long)
        
        try:
            loss = contrastive_loss(features, labels)
            print(f"   ✅ Success: Loss = {loss.item():.6f}")
            
            # Check for NaN
            if torch.isnan(loss):
                print(f"   ⚠️  NaN detected in loss")
                return False
                
        except Exception as e:
            print(f"   ❌ Failed: {e}")
            return False
    
    return True

def test_contrastive_trainer():
    """Test ContrastiveTrainer with various scenarios"""
    print("\n🔬 Testing ContrastiveTrainer...")

    # Create a simple model
    class SimpleModel(torch.nn.Module):
        def __init__(self):
            super().__init__()
            self.linear = torch.nn.Linear(10, 5)

        def forward(self, input_ids, attention_mask, output_hidden_states=False):
            # Simulate transformer output
            batch_size = input_ids.shape[0]
            logits = self.linear(torch.randn(batch_size, 10))

            if output_hidden_states:
                hidden_states = [torch.randn(batch_size, 8, 768)]  # Simulate hidden states
                return type('obj', (object,), {
                    'logits': logits,
                    'hidden_states': hidden_states
                })
            return type('obj', (object,), {'logits': logits})

    # Create a mock config
    class MockConfig:
        def __init__(self):
            self.experiment = type('obj', (object,), {'device': 'cpu'})
            self.training = type('obj', (object,), {'contrastive_temperature': 0.07})
            self.data = type('obj', (object,), {'domains': ['Books', 'Electronics', 'Home_and_Kitchen']})

    model = SimpleModel()
    config = MockConfig()
    trainer = CrossDomainContrastiveTrainer(config)
    
    # Test case 1: Normal batch
    print("\n📊 Test case 1: Normal batch")
    input_batch = {
        'input_ids': torch.randint(0, 1000, (4, 8)),
        'attention_mask': torch.ones(4, 8)
    }
    pseudo_labels = torch.randint(0, 5, (4,))
    domains = ['Books', 'Electronics', 'Books', 'Electronics']
    
    try:
        loss = trainer.compute_contrastive_loss(model, input_batch, pseudo_labels, domains)
        print(f"   ✅ Normal batch: Loss = {loss.item():.6f}")
    except Exception as e:
        print(f"   ❌ Normal batch failed: {e}")
        return False
    
    # Test case 2: Mismatched sizes
    print("\n📊 Test case 2: Mismatched sizes")
    input_batch = {
        'input_ids': torch.randint(0, 1000, (4, 8)),
        'attention_mask': torch.ones(4, 8)
    }
    pseudo_labels = torch.randint(0, 5, (6,))  # More labels than features
    domains = ['Books', 'Electronics', 'Books', 'Electronics', 'Books', 'Electronics']  # Match label count
    
    try:
        loss = trainer.compute_contrastive_loss(model, input_batch, pseudo_labels, domains)
        print(f"   ✅ Mismatched sizes: Loss = {loss.item():.6f}")
    except Exception as e:
        print(f"   ❌ Mismatched sizes failed: {e}")
        return False
    
    # Test case 3: Unknown domain
    print("\n📊 Test case 3: Unknown domain")
    input_batch = {
        'input_ids': torch.randint(0, 1000, (2, 8)),
        'attention_mask': torch.ones(2, 8)
    }
    pseudo_labels = torch.randint(0, 5, (2,))
    domains = ['Books', 'UnknownDomain']  # Unknown domain
    
    try:
        loss = trainer.compute_contrastive_loss(model, input_batch, pseudo_labels, domains)
        print(f"   ✅ Unknown domain: Loss = {loss.item():.6f}")
    except Exception as e:
        print(f"   ❌ Unknown domain failed: {e}")
        return False
    
    return True

def test_gradient_flow():
    """Test gradient flow with contrastive loss"""
    print("\n🔄 Testing gradient flow...")

    # Create model and optimizer
    model = torch.nn.Linear(128, 5)
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
    contrastive_loss = SupervisedContrastiveLoss()

    # Test data - features need to require gradients
    x = torch.randn(8, 128, requires_grad=True)
    features = F.normalize(x, dim=1)
    labels = torch.randint(0, 5, (8,))

    try:
        # Forward pass
        loss = contrastive_loss(features, labels)

        # Backward pass
        optimizer.zero_grad()
        loss.backward()

        # Check gradients on input
        has_input_grad = x.grad is not None and not torch.isnan(x.grad).any()

        # Check gradients on model parameters
        has_param_grad = any(param.grad is not None and not torch.isnan(param.grad).any()
                           for param in model.parameters() if param.requires_grad)

        if has_input_grad:
            print("   ✅ Gradient flow is working")
            return True
        else:
            print("   ❌ No valid gradients found")
            return False

    except Exception as e:
        print(f"   ❌ Gradient flow failed: {e}")
        return False

def main():
    """Main test function"""
    print("=" * 60)
    print("🔧 Testing Contrastive Learning Fixes")
    print("=" * 60)
    
    # Run tests
    batch_test = test_contrastive_loss_batch_mismatch()
    trainer_test = test_contrastive_trainer()
    grad_test = test_gradient_flow()
    
    print("\n" + "=" * 60)
    print("📊 Test Results:")
    print(f"   Batch Mismatch Tests: {'✅ PASS' if batch_test else '❌ FAIL'}")
    print(f"   Trainer Tests: {'✅ PASS' if trainer_test else '❌ FAIL'}")
    print(f"   Gradient Flow: {'✅ PASS' if grad_test else '❌ FAIL'}")
    
    all_passed = batch_test and trainer_test and grad_test
    
    if all_passed:
        print("\n🎉 All tests passed! Contrastive learning fixes are working correctly.")
        print("You can now restart the experiment.")
        return 0
    else:
        print("\n⚠️  Some tests failed. Please check the fixes.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
