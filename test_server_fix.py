"""
Test script to verify server distillation fixes
"""
import torch
import sys
import os

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_batch_indexing():
    """Test batch indexing logic"""
    print("🧪 Testing batch indexing logic...")
    
    # Simulate teacher predictions
    teacher_predictions = {
        'Books': torch.randn(100, 5),  # 100 samples, 5 classes
        'Electronics': torch.randn(100, 5),
        'Home_and_Kitchen': torch.randn(100, 5)
    }
    
    # Simulate domain weights
    domain_weights = {
        'Books': torch.randn(100),
        'Electronics': torch.randn(100),
        'Home_and_Kitchen': torch.randn(100)
    }
    
    # Test parameters
    distillation_batch_size = 16
    num_batches_to_test = 7  # This should cover 112 samples (7 * 16)
    
    print(f"   Teacher predictions: {len(teacher_predictions)} domains, 100 samples each")
    print(f"   Batch size: {distillation_batch_size}")
    print(f"   Testing {num_batches_to_test} batches")
    
    # Test batch extraction
    for batch_idx in range(num_batches_to_test):
        print(f"\n   📊 Batch {batch_idx}:")
        
        batch_teacher_preds = {}
        batch_domain_weights = {}
        current_batch_size = distillation_batch_size
        
        # Simulate the logic from server.py
        for domain in teacher_predictions:
            start_idx = batch_idx * distillation_batch_size
            end_idx = start_idx + current_batch_size
            
            # Check if we have enough predictions
            if start_idx < teacher_predictions[domain].size(0):
                actual_end_idx = min(end_idx, teacher_predictions[domain].size(0))
                batch_teacher_preds[domain] = teacher_predictions[domain][start_idx:actual_end_idx]
                
                if domain in domain_weights and start_idx < domain_weights[domain].size(0):
                    actual_end_idx_weights = min(end_idx, domain_weights[domain].size(0))
                    batch_domain_weights[domain] = domain_weights[domain][start_idx:actual_end_idx_weights]
            else:
                print(f"      ⚠️  No predictions available for domain {domain}")
                continue
        
        # Check results
        if batch_teacher_preds:
            for domain, preds in batch_teacher_preds.items():
                print(f"      {domain}: {preds.shape}")
            print(f"      ✅ Valid batch with {len(batch_teacher_preds)} domains")
        else:
            print(f"      ❌ Empty batch - no valid predictions")
            break
    
    return True

def test_empty_predictions():
    """Test handling of empty predictions"""
    print("\n🔬 Testing empty predictions handling...")
    
    # Test case 1: Empty teacher predictions
    teacher_predictions = {}
    
    if not teacher_predictions:
        print("   ✅ Empty teacher predictions detected correctly")
    else:
        print("   ❌ Failed to detect empty teacher predictions")
        return False
    
    # Test case 2: Teacher predictions with zero samples (intentional test case)
    print("   Testing mixed valid/empty predictions scenario...")
    teacher_predictions = {
        'Books': torch.empty(0, 5),  # Intentionally empty for testing
        'Electronics': torch.randn(10, 5)  # Valid tensor
    }

    valid_domains = 0
    for domain, preds in teacher_predictions.items():
        if preds.size(0) > 0:
            valid_domains += 1
            print(f"   ✅ Domain {domain}: {preds.shape} (valid)")
        else:
            print(f"   📝 Domain {domain}: {preds.shape} (empty - test case)")

    if valid_domains > 0:
        print(f"   ✅ Found {valid_domains} valid domains (expected behavior)")
    else:
        print("   ❌ No valid domains found")
        return False
    
    return True

def test_ensemble_computation():
    """Test ensemble prediction computation"""
    print("\n🎯 Testing ensemble computation...")
    
    # Create mock ensemble teacher
    class MockEnsembleTeacher:
        def compute_ensemble_predictions(self, teacher_preds, domain_weights):
            if not teacher_preds:
                return torch.empty(0, 5)
            
            # Simple averaging for testing
            all_preds = []
            for domain, preds in teacher_preds.items():
                if preds.size(0) > 0:
                    all_preds.append(preds)
            
            if all_preds:
                return torch.stack(all_preds).mean(dim=0)
            else:
                return torch.empty(0, 5)
    
    ensemble_teacher = MockEnsembleTeacher()
    
    # Test case 1: Valid predictions
    teacher_preds = {
        'Books': torch.randn(16, 5),
        'Electronics': torch.randn(16, 5)
    }
    domain_weights = {
        'Books': torch.randn(16),
        'Electronics': torch.randn(16)
    }
    
    try:
        ensemble_logits = ensemble_teacher.compute_ensemble_predictions(teacher_preds, domain_weights)
        print(f"   ✅ Valid case: Ensemble shape {ensemble_logits.shape}")
    except Exception as e:
        print(f"   ❌ Valid case failed: {e}")
        return False
    
    # Test case 2: Empty predictions
    teacher_preds = {}
    domain_weights = {}
    
    try:
        ensemble_logits = ensemble_teacher.compute_ensemble_predictions(teacher_preds, domain_weights)
        if ensemble_logits.size(0) == 0:
            print(f"   ✅ Empty case: Correctly returned empty tensor {ensemble_logits.shape}")
        else:
            print(f"   ❌ Empty case: Expected empty tensor, got {ensemble_logits.shape}")
            return False
    except Exception as e:
        print(f"   ❌ Empty case failed: {e}")
        return False
    
    return True

def main():
    """Main test function"""
    print("=" * 60)
    print("🔧 Testing Server Distillation Fixes")
    print("=" * 60)
    
    # Run tests
    indexing_test = test_batch_indexing()
    empty_test = test_empty_predictions()
    ensemble_test = test_ensemble_computation()
    
    print("\n" + "=" * 60)
    print("📊 Test Results:")
    print(f"   Batch Indexing: {'✅ PASS' if indexing_test else '❌ FAIL'}")
    print(f"   Empty Predictions: {'✅ PASS' if empty_test else '❌ FAIL'}")
    print(f"   Ensemble Computation: {'✅ PASS' if ensemble_test else '❌ FAIL'}")
    
    all_passed = indexing_test and empty_test and ensemble_test
    
    if all_passed:
        print("\n🎉 All tests passed! Server fixes are working correctly.")
        print("The teacher prediction indexing issue should be resolved.")
        return 0
    else:
        print("\n⚠️  Some tests failed. Please check the fixes.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
