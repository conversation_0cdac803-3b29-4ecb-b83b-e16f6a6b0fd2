2025-08-03 12:15:29,414 - __main__ - INFO - T5质量测试日志初始化完成. 日志文件: logs/t5_quality_test_20250803_121529.log
2025-08-03 12:15:29,414 - __main__ - INFO - 开始T5文本生成器质量测试
2025-08-03 12:15:29,414 - __main__ - INFO - ============================================================
2025-08-03 12:15:30,881 - __main__ - INFO - 使用设备: cuda
2025-08-03 12:15:30,881 - __main__ - INFO - 生成器模型: t5-small
2025-08-03 12:15:30,881 - __main__ - INFO - 步骤 1: 加载原始数据
2025-08-03 12:15:30,881 - __main__ - INFO - 加载原始Amazon评论数据...
2025-08-03 12:15:30,881 - __main__ - ERROR - 测试过程中发生错误: __init__() missing 3 required positional arguments: 'labels', 'domains', and 'tokenizer'
2025-08-03 12:15:30,884 - __main__ - ERROR - Traceback (most recent call last):
  File "e:\amazon-re-fd\test_t5_generator_quality.py", line 359, in main
    original_data = load_original_data(config)
  File "e:\amazon-re-fd\test_t5_generator_quality.py", line 76, in load_original_data
    dataset = AmazonReviewDataset(config)
TypeError: __init__() missing 3 required positional arguments: 'labels', 'domains', and 'tokenizer'

