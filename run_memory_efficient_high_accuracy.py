"""
Memory-efficient high accuracy D³AFD experiment runner
Optimized to achieve 60-80% accuracy while managing GPU memory efficiently
"""
import os
import sys
import logging
import torch
import gc
from datetime import datetime

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.config import get_default_config
from src.federated.d3afd_framework import D3AFDFramework

def setup_memory_efficient_high_accuracy_config():
    """Create memory-efficient high accuracy configuration"""
    config = get_default_config()
    
    # Balanced model selection - better than DistilBERT but manageable memory
    config.model.teacher_model = "distilbert-base-uncased"  # Keep DistilBERT for memory efficiency
    config.model.student_model = "distilbert-base-uncased"  # Same as teacher
    config.model.generator_model = "t5-small"  # Keep T5-small for memory efficiency
    config.model.generator_max_length = 128  # Reasonable length
    
    # Optimized data configuration
    config.data.num_clients = 8  # Good balance
    config.data.samples_per_client = 500  # More samples but manageable
    config.data.max_seq_length = 256  # Reasonable length, not 512
    
    # Extensive but memory-conscious training
    config.training.federated_rounds = 12  # More rounds for convergence
    config.training.distillation_rounds = 8  # More distillation
    config.training.local_epochs = 4  # More local training
    config.training.distillation_epochs = 3  # Balanced
    config.training.personalization_epochs = 5  # More personalization
    
    # Memory-efficient batch sizes
    config.training.local_batch_size = 2  # Very small for memory efficiency
    config.training.distillation_batch_size = 4  # Small batch
    
    # Optimized learning rates
    config.training.local_lr = 2e-5  # Slightly higher to compensate for smaller batches
    config.training.distillation_lr = 5e-5  # Good for knowledge transfer
    config.training.personalization_lr = 8e-5  # Higher for adaptation
    
    # Regularization
    config.training.weight_decay = 0.01
    config.training.dropout_rate = 0.1
    
    # Improved loss weights
    config.training.contrastive_weight = 0.4  # Increase contrastive learning
    config.training.dkd_alpha = 0.6  # Favor target class knowledge
    config.training.dkd_beta = 0.4
    
    # More pseudo data but memory-conscious
    config.training.pseudo_samples_per_domain = 150  # More than before but not excessive
    config.training.mixed_domain_samples = 50
    
    # Output settings
    config.experiment.output_dir = "memory_efficient_high_accuracy_outputs"
    config.experiment.data_dir = "memory_efficient_high_accuracy_data"
    
    return config

def clear_gpu_memory():
    """Aggressively clear GPU memory"""
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        torch.cuda.ipc_collect()
        gc.collect()
        # Force garbage collection multiple times
        for _ in range(3):
            gc.collect()

def setup_memory_optimized_environment():
    """Setup environment for memory optimization"""
    # Set CUDA memory allocation configuration
    os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'expandable_segments:True,max_split_size_mb:64'
    os.environ['TOKENIZERS_PARALLELISM'] = 'false'
    
    # Set other memory optimization flags
    os.environ['CUDA_LAUNCH_BLOCKING'] = '0'  # Disable for better memory management
    
    # Enable memory-efficient attention if available
    try:
        torch.backends.cuda.enable_flash_sdp(True)
    except:
        pass

def setup_logging():
    """Setup logging"""
    os.makedirs('logs', exist_ok=True)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_filename = f"logs/memory_efficient_high_accuracy_{timestamp}.log"
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_filename, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    logger = logging.getLogger(__name__)
    logger.info(f"Memory-efficient high accuracy experiment logging initialized. Log file: {log_filename}")
    return logger

def check_memory_requirements():
    """Check if we have enough memory"""
    if not torch.cuda.is_available():
        return False, "CUDA not available"
    
    gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
    
    if gpu_memory < 8:
        return False, f"Insufficient GPU memory: {gpu_memory:.2f}GB (need at least 8GB)"
    
    return True, f"GPU memory: {gpu_memory:.2f}GB"

def main():
    """Main function for memory-efficient high accuracy experiment"""
    logger = setup_logging()
    
    logger.info("Starting Memory-Efficient High Accuracy D³AFD Experiment")
    logger.info("=" * 70)
    logger.info("🎯 TARGET: Achieve 60-80% global accuracy with efficient memory usage")
    logger.info("🔧 Memory optimizations applied:")
    logger.info("   - DistilBERT models for memory efficiency")
    logger.info("   - T5-small generator for manageable memory")
    logger.info("   - Smaller batch sizes (2/4)")
    logger.info("   - Reasonable sequence length (256)")
    logger.info("   - Aggressive memory management")
    logger.info("   - More training rounds to compensate")
    
    # Setup memory optimization
    setup_memory_optimized_environment()
    
    # Check memory requirements
    memory_ok, memory_msg = check_memory_requirements()
    logger.info(f"Memory check: {memory_msg}")
    
    if not memory_ok:
        logger.error("❌ Memory requirements not met!")
        return 1
    
    # Clear initial memory
    clear_gpu_memory()
    
    # Set conservative memory fraction
    gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
    if gpu_memory > 12:
        memory_fraction = 0.80
    elif gpu_memory > 8:
        memory_fraction = 0.75
    else:
        memory_fraction = 0.70
    
    torch.cuda.set_per_process_memory_fraction(memory_fraction)
    logger.info(f"Set GPU memory fraction to {memory_fraction*100:.0f}%")
    
    # Get memory-efficient config
    config = setup_memory_efficient_high_accuracy_config()
    
    logger.info("Memory-Efficient High Accuracy Configuration:")
    logger.info(f"  🏢 Clients: {config.data.num_clients}")
    logger.info(f"  📊 Samples per client: {config.data.samples_per_client}")
    logger.info(f"  📏 Max sequence length: {config.data.max_seq_length}")
    logger.info(f"  🤖 Teacher model: {config.model.teacher_model}")
    logger.info(f"  📝 Generator model: {config.model.generator_model}")
    logger.info(f"  🔄 Federated rounds: {config.training.federated_rounds}")
    logger.info(f"  🧪 Distillation rounds: {config.training.distillation_rounds}")
    logger.info(f"  🎭 Pseudo samples per domain: {config.training.pseudo_samples_per_domain}")
    logger.info(f"  📦 Local batch size: {config.training.local_batch_size}")
    logger.info(f"  📦 Distillation batch size: {config.training.distillation_batch_size}")
    
    try:
        # Initialize framework
        logger.info("Initializing D³AFD Framework...")
        framework = D3AFDFramework(config)
        
        # Clear memory after initialization
        clear_gpu_memory()
        
        # Monitor memory usage
        if torch.cuda.is_available():
            allocated = torch.cuda.memory_allocated(0) / 1024**3
            reserved = torch.cuda.memory_reserved(0) / 1024**3
            logger.info(f"Memory after initialization - Allocated: {allocated:.2f}GB, Reserved: {reserved:.2f}GB")
        
        # Run training
        logger.info("Starting memory-efficient high accuracy training...")
        logger.info("💡 Strategy:")
        logger.info("   - Use smaller models but train longer")
        logger.info("   - Smaller batches but more epochs")
        logger.info("   - Better loss balancing")
        logger.info("   - Aggressive memory management")
        
        results = framework.run_complete_training(force_reload_data=False)
        
        # Analyze results
        logger.info("Training completed successfully!")
        logger.info("=" * 70)
        logger.info("📊 FINAL RESULTS ANALYSIS:")
        
        if 'global_model' in results:
            global_acc = results['global_model'].get('accuracy', 0)
            global_f1 = results['global_model'].get('f1_macro', 0)
            logger.info(f"🌍 Global Model Performance:")
            logger.info(f"   Accuracy: {global_acc:.4f} ({global_acc*100:.2f}%)")
            logger.info(f"   F1-Macro: {global_f1:.4f}")
            
            # Achievement analysis
            if global_acc >= 0.8:
                logger.info("   🎉 EXCELLENT! Exceeded 80% target!")
            elif global_acc >= 0.6:
                logger.info("   ✅ SUCCESS! Achieved 60-80% target range!")
            elif global_acc >= 0.4:
                logger.info("   📈 GOOD PROGRESS! Significant improvement from baseline")
            elif global_acc >= 0.3:
                logger.info("   📊 MODERATE PROGRESS! Better than previous attempts")
            else:
                logger.info("   ⚠️  NEEDS IMPROVEMENT! Consider further optimization")
        
        if 'personalized_models' in results:
            personal_accs = [r.get('accuracy', 0) for r in results['personalized_models'].values()]
            if personal_accs:
                avg_personal_acc = sum(personal_accs) / len(personal_accs)
                max_personal_acc = max(personal_accs)
                min_personal_acc = min(personal_accs)
                
                logger.info(f"👤 Personalized Models Performance:")
                logger.info(f"   Average: {avg_personal_acc:.4f} ({avg_personal_acc*100:.2f}%)")
                logger.info(f"   Best: {max_personal_acc:.4f} ({max_personal_acc*100:.2f}%)")
                logger.info(f"   Worst: {min_personal_acc:.4f} ({min_personal_acc*100:.2f}%)")
        
        logger.info("=" * 70)
        logger.info(f"📁 Results saved to: {config.experiment.output_dir}")
        
        return 0
        
    except torch.cuda.OutOfMemoryError as e:
        logger.error("❌ CUDA Out of Memory Error!")
        logger.error("Even memory-efficient configuration failed.")
        logger.error("Recommendations:")
        logger.error("   1. Try run_memory_optimized.py for even smaller configuration")
        logger.error("   2. Use CPU-only mode: python run_cpu_demo.py")
        logger.error("   3. Reduce batch sizes further")
        logger.error(f"Error details: {e}")
        return 1
        
    except Exception as e:
        logger.error(f"Experiment failed: {e}")
        logger.error("Full traceback:")
        import traceback
        logger.error(traceback.format_exc())
        return 1
    
    finally:
        clear_gpu_memory()
        logger.info("Memory-efficient high accuracy experiment completed.")

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
