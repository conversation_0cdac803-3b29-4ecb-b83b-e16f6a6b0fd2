"""
Test script to verify NaN fixes are working
"""
import torch
import torch.nn.functional as F
import sys
import os

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.models.dkd_distillation import DKDLoss
from src.models.contrastive_learning import SupervisedContrastiveLoss

def test_dkd_loss():
    """Test DKD loss with edge cases"""
    print("🧪 Testing DKD Loss...")
    
    dkd_loss = DKDLoss(alpha=0.7, beta=0.3, temperature=3.0)
    
    # Test case 1: Normal case
    student_logits = torch.randn(4, 5)
    teacher_logits = torch.randn(4, 5)
    
    losses = dkd_loss(student_logits, teacher_logits)
    print(f"   Normal case - DKD Loss: {losses['dkd_loss'].item():.4f}")
    
    # Test case 2: Extreme values
    student_logits_extreme = torch.tensor([[100.0, -100.0, 0.0, 0.0, 0.0],
                                          [-100.0, 100.0, 0.0, 0.0, 0.0],
                                          [0.0, 0.0, 100.0, -100.0, 0.0],
                                          [0.0, 0.0, 0.0, 0.0, 100.0]])
    teacher_logits_extreme = torch.tensor([[100.0, -100.0, 0.0, 0.0, 0.0],
                                          [-100.0, 100.0, 0.0, 0.0, 0.0],
                                          [0.0, 0.0, 100.0, -100.0, 0.0],
                                          [0.0, 0.0, 0.0, 0.0, 100.0]])
    
    losses_extreme = dkd_loss(student_logits_extreme, teacher_logits_extreme)
    print(f"   Extreme case - DKD Loss: {losses_extreme['dkd_loss'].item():.4f}")
    
    # Test case 3: All zeros (should not produce NaN)
    student_logits_zero = torch.zeros(4, 5)
    teacher_logits_zero = torch.zeros(4, 5)
    
    losses_zero = dkd_loss(student_logits_zero, teacher_logits_zero)
    print(f"   Zero case - DKD Loss: {losses_zero['dkd_loss'].item():.4f}")
    
    # Check for NaN
    all_losses = [losses['dkd_loss'], losses_extreme['dkd_loss'], losses_zero['dkd_loss']]
    has_nan = any(torch.isnan(loss) for loss in all_losses)
    
    if has_nan:
        print("   ❌ NaN detected in DKD loss!")
        return False
    else:
        print("   ✅ DKD loss is numerically stable")
        return True

def test_contrastive_loss():
    """Test contrastive loss with edge cases"""
    print("🧪 Testing Contrastive Loss...")
    
    contrastive_loss = SupervisedContrastiveLoss(temperature=0.07)
    
    # Test case 1: Normal case
    features = F.normalize(torch.randn(8, 128), dim=1)
    labels = torch.tensor([0, 0, 1, 1, 2, 2, 3, 3])
    
    loss = contrastive_loss(features, labels)
    print(f"   Normal case - Contrastive Loss: {loss.item():.4f}")
    
    # Test case 2: All same label (edge case)
    labels_same = torch.zeros(8, dtype=torch.long)
    loss_same = contrastive_loss(features, labels_same)
    print(f"   Same labels - Contrastive Loss: {loss_same.item():.4f}")
    
    # Test case 3: All different labels (no positive pairs)
    labels_diff = torch.arange(8)
    loss_diff = contrastive_loss(features, labels_diff)
    print(f"   Different labels - Contrastive Loss: {loss_diff.item():.4f}")
    
    # Test case 4: Zero features
    features_zero = torch.zeros(8, 128)
    loss_zero = contrastive_loss(features_zero, labels)
    print(f"   Zero features - Contrastive Loss: {loss_zero.item():.4f}")
    
    # Check for NaN
    all_losses = [loss, loss_same, loss_diff, loss_zero]
    has_nan = any(torch.isnan(loss) for loss in all_losses)
    
    if has_nan:
        print("   ❌ NaN detected in contrastive loss!")
        return False
    else:
        print("   ✅ Contrastive loss is numerically stable")
        return True

def test_gradient_flow():
    """Test gradient flow with the fixed losses"""
    print("🧪 Testing Gradient Flow...")
    
    # Create simple model
    model = torch.nn.Linear(10, 5)
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
    
    # Test data
    x = torch.randn(4, 10)
    student_logits = model(x)
    teacher_logits = torch.randn(4, 5)
    
    # Test DKD loss gradient flow
    dkd_loss = DKDLoss()
    losses = dkd_loss(student_logits, teacher_logits)
    
    optimizer.zero_grad()
    losses['dkd_loss'].backward()
    
    # Check gradients
    has_nan_grad = False
    for param in model.parameters():
        if param.grad is not None and torch.isnan(param.grad).any():
            has_nan_grad = True
            break
    
    if has_nan_grad:
        print("   ❌ NaN detected in gradients!")
        return False
    else:
        print("   ✅ Gradient flow is stable")
        return True

def main():
    """Main test function"""
    print("=" * 50)
    print("🔧 Testing NaN Fixes")
    print("=" * 50)
    
    # Set device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Run tests
    dkd_ok = test_dkd_loss()
    contrastive_ok = test_contrastive_loss()
    gradient_ok = test_gradient_flow()
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"   DKD Loss: {'✅ PASS' if dkd_ok else '❌ FAIL'}")
    print(f"   Contrastive Loss: {'✅ PASS' if contrastive_ok else '❌ FAIL'}")
    print(f"   Gradient Flow: {'✅ PASS' if gradient_ok else '❌ FAIL'}")
    
    all_passed = dkd_ok and contrastive_ok and gradient_ok
    
    if all_passed:
        print("\n🎉 All tests passed! NaN fixes are working correctly.")
        print("You can now restart the experiment with confidence.")
        return 0
    else:
        print("\n⚠️  Some tests failed. Please check the fixes.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
