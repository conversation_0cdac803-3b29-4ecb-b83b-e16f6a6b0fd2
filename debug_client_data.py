#!/usr/bin/env python3
"""
Debug script to check client data flow
"""

import sys
import os

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def debug_client_setup():
    """Debug the client setup process"""
    print("=" * 60)
    print("DEBUG: Client Data Flow")
    print("=" * 60)
    
    try:
        # Step 1: Create config
        from src.utils.config import Config
        from quick_test_algorithms import setup_quick_test_config
        
        config_dict = setup_quick_test_config('dkdm')
        config = Config(config_dict)
        print("✅ Step 1: Config created successfully")
        print(f"   Domains: {config.data.domains}")
        
        # Step 2: Create dataset
        from src.data.amazon_dataset import AmazonReviewDataset
        dataset = AmazonReviewDataset(config=config)
        print(f"✅ Step 2: Dataset created with {len(dataset)} samples")
        print(f"   Available domains: {list(set(dataset.domains))}")
        
        # Step 3: Test domain data retrieval for each domain
        for domain in config.data.domains:
            domain_data = dataset.get_domain_data(domain)
            print(f"✅ Step 3: Domain '{domain}' has {len(domain_data)} samples")
            if domain_data:
                print(f"   Sample: {domain_data[0]}")
        
        # Step 4: Test client creation
        print("\n" + "-" * 40)
        print("Testing Client Creation:")
        print("-" * 40)
        
        from src.federated.client import FederatedClient
        
        for i, domain in enumerate(config.data.domains):
            client_id = f"client_{i}_{domain}"
            domain_data = dataset.get_domain_data(domain)
            
            print(f"\nCreating client {client_id}:")
            print(f"  Domain: {domain}")
            print(f"  Data length: {len(domain_data)}")
            print(f"  Data type: {type(domain_data)}")
            print(f"  Data is None: {domain_data is None}")
            
            # Create client
            client = FederatedClient(
                client_id=client_id,
                domain=domain,
                config=config,
                local_data=domain_data
            )
            
            print(f"  Client created: {client.client_id}")
            print(f"  Has train_dataloader: {client.train_dataloader is not None}")
            print(f"  Has tokenizer: {client.tokenizer is not None}")
            
            if client.train_dataloader:
                print(f"  Train dataloader length: {len(client.train_dataloader)}")
                
                # Test a batch
                try:
                    batch = next(iter(client.train_dataloader))
                    print(f"  Batch keys: {list(batch.keys())}")
                    print(f"  Batch input_ids shape: {batch['input_ids'].shape}")
                    print(f"  Batch attention_mask shape: {batch['attention_mask'].shape}")
                except Exception as e:
                    print(f"  ❌ Error getting batch: {e}")
            else:
                print(f"  ❌ No train_dataloader created")
        
        print("\n" + "=" * 60)
        print("DEBUG COMPLETED")
        print("=" * 60)
        
    except Exception as e:
        import traceback
        print(f"❌ Error in debug: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    debug_client_setup()
