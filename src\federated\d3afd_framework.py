"""
D³AFD Framework Implementation
Main orchestrator for the complete federated learning process
"""
import torch
import logging
import os
import json
import numpy as np
from typing import Dict, List, Optional, Any
from tqdm import tqdm

from .server import FederatedServer
from .client import FederatedClient
from ..data.amazon_dataset import AmazonDataProcessor
from ..models.domain_discriminator import DomainDiscriminatorTrainer

logger = logging.getLogger(__name__)

class D3AFDFramework:
    """Complete D³AFD framework implementation"""
    
    def __init__(self, config):
        self.config = config
        self.device = torch.device(config.experiment.device)
        
        # Initialize components
        self.server = FederatedServer(config)
        self.clients = {}
        self.data_processor = AmazonDataProcessor(config)
        
        # Training state
        self.current_round = 0
        self.training_complete = False
        
        # Results storage
        self.experiment_results = {
            'global_model_performance': [],
            'client_performances': {},
            'training_history': []
        }
        
        logger.info("D³AFD Framework initialized")
    
    def setup_data(self, force_reload: bool = False) -> Dict[int, Dict[str, List]]:
        """Setup federated data distribution"""
        logger.info("Setting up federated data distribution...")
        
        # Check if federated data already exists
        federated_data_dir = os.path.join(self.config.experiment.data_dir, "federated_split")
        
        if not force_reload and os.path.exists(federated_data_dir):
            logger.info("Loading existing federated data split...")
            clients_data = self.data_processor.load_federated_data(federated_data_dir)
        else:
            # Load and process Amazon Reviews dataset
            df = self.data_processor.load_amazon_reviews()
            
            # Create federated split
            clients_data = self.data_processor.create_federated_split(df)
            
            # Save federated data
            self.data_processor.save_federated_data(clients_data, federated_data_dir)
        
        # Print statistics
        stats = self.data_processor.get_domain_statistics(clients_data)
        logger.info(f"Federated data setup complete:")
        logger.info(f"  - Total clients: {stats['total_clients']}")
        logger.info(f"  - Domain distribution: {dict(stats['domain_distribution'])}")
        
        return clients_data
    
    def initialize_clients(self, clients_data: Dict[int, Dict[str, List]]):
        """Initialize federated clients"""
        logger.info("Initializing federated clients...")
        
        for client_id in range(self.config.data.num_clients):
            if client_id in clients_data:
                # Create data loaders for this client
                train_loader = self.data_processor.create_dataloaders(
                    clients_data, client_id, 'train'
                )
                val_loader = self.data_processor.create_dataloaders(
                    clients_data, client_id, 'val'
                )
                test_loader = self.data_processor.create_dataloaders(
                    clients_data, client_id, 'test'
                )
                
                # Create client
                client = FederatedClient(
                    client_id=client_id,
                    config=self.config,
                    train_dataloader=train_loader,
                    val_dataloader=val_loader,
                    test_dataloader=test_loader
                )
                
                # Initialize client models
                client.initialize_models()
                
                # Register client with server
                self.server.register_client(client_id, client.client_domains)
                
                self.clients[client_id] = client
                
                logger.info(f"Client {client_id} initialized with {len(clients_data[client_id]['texts'])} samples")
        
        logger.info(f"Initialized {len(self.clients)} clients")
    

    
    def run_federated_rounds(self):
        """Run complete federated learning rounds following D³AFD four-stage design"""
        logger.info("=== D³AFD Federated Learning Rounds ===")
        logger.info(f"Total federated rounds: {self.config.training.federated_rounds}")
        logger.info("Each round includes: Local Training → Pseudo Data Generation → DKD Distillation → Personalization")

        for fed_round in range(1, self.config.training.federated_rounds + 1):
            logger.info(f"\n{'='*60}")
            logger.info(f"FEDERATED ROUND {fed_round}/{self.config.training.federated_rounds}")
            logger.info(f"{'='*60}")

            # Stage 1: Local Model Training (except first round)
            if fed_round > 1:
                logger.info("Stage 1: Local Model Re-training")
                for client_id, client in self.clients.items():
                    stats = client.train_local_teacher(num_epochs=2)  # Shorter re-training
                    logger.info(f"Client {client_id} re-training: Accuracy {stats['accuracy']:.4f}")
            else:
                logger.info("Stage 1: Using initial local models")

            # Stage 2: Pseudo Data Generation & Stage 3: Weighted DKD Distillation
            logger.info("Stage 2-3: Pseudo Data Generation & DKD Distillation")
            for dist_round in range(1, self.config.training.distillation_rounds + 1):
                global_round_num = (fed_round - 1) * self.config.training.distillation_rounds + dist_round
                logger.info(f"  Distillation Round {dist_round}/{self.config.training.distillation_rounds}")

                # Perform distillation round (includes pseudo data generation and DKD)
                round_stats = self.server.distillation_round(self.clients, global_round_num)
                round_stats['federated_round'] = fed_round
                round_stats['distillation_round'] = dist_round

                # Store results
                self.experiment_results['training_history'].append(round_stats)

            # Stage 4: Personalized Fine-tuning
            logger.info("Stage 4: Personalized Fine-tuning")

            # Distribute current global model to clients
            self.server.distribute_global_model(self.clients)

            # Fine-tune personalized models
            for client_id, client in self.clients.items():
                stats = client.fine_tune_personalized_model(num_epochs=2)  # Quick personalization
                logger.info(f"Client {client_id} personalization: Accuracy {stats['accuracy']:.4f}")

            # CRITICAL FIX: Perform federated averaging after each round
            logger.info("🔄 Performing Federated Averaging...")
            self.server.federated_averaging(self.clients)

            # Evaluate global model after each federated round
            logger.info(f"📊 Evaluating Global Model after Federated Round {fed_round}")
            self._evaluate_global_model(f"Federated_Round_{fed_round}")

            # Optional: Early stopping based on convergence
            if self._check_convergence():
                logger.info(f"Early stopping at federated round {fed_round} due to convergence")
                break

            # Print round summary
            logger.info(f"✅ Federated Round {fed_round}/{self.config.training.federated_rounds} completed")
            logger.info(f"{'='*60}")

        logger.info("🎉 All federated learning rounds completed!")
    
    def initial_setup_phase(self):
        """Initial setup: Train local models and domain discriminators once"""
        logger.info("=== Initial Setup Phase ===")

        # Train local teacher models
        logger.info("Training initial local teacher models...")
        for client_id, client in tqdm(self.clients.items(), desc="Training teachers"):
            stats = client.train_local_teacher()
            logger.info(f"Client {client_id} teacher training: Accuracy {stats['accuracy']:.4f}")

        # Train domain discriminators
        logger.info("Training domain discriminators...")

        # Collect samples from all clients for negative sampling
        all_client_samples = {}
        for client_id, client in self.clients.items():
            client_samples = []
            for batch in client.train_dataloader:
                client_samples.extend(batch['text'])
            all_client_samples[client_id] = client_samples[:200]  # Limit for efficiency

        # Train discriminators
        for client_id, client in tqdm(self.clients.items(), desc="Training discriminators"):
            # Create negative samples from other clients
            negative_samples = []
            for other_id, other_samples in all_client_samples.items():
                if other_id != client_id:
                    negative_samples.extend(other_samples[:50])  # Sample from each other client

            stats = client.train_domain_discriminator(negative_samples)
            logger.info(f"Client {client_id} discriminator training: Accuracy {stats['accuracy']:.4f}")

        logger.info("Initial setup completed")
    
    def run_complete_training(self, force_reload_data: bool = False) -> Dict[str, Any]:
        """Run the complete D³AFD training process following the four-stage design"""
        logger.info("Starting complete D³AFD training process...")
        logger.info("Following D³AFD four-stage design: Local Training → Pseudo Data → DKD Distillation → Personalization")

        try:
            # Setup data
            clients_data = self.setup_data(force_reload_data)

            # Initialize clients
            self.initialize_clients(clients_data)

            # Initial setup: Train local models and discriminators once
            self.initial_setup_phase()

            # Run federated rounds (each round includes all four stages)
            self.run_federated_rounds()

            # Final evaluation
            final_results = self._final_evaluation()

            self.training_complete = True

            logger.info("D³AFD training process completed successfully!")

            return final_results

        except Exception as e:
            logger.error(f"Training failed with error: {e}")
            raise e
    
    def _evaluate_global_model(self, round_identifier: str):
        """Evaluate global model on a global test set"""
        # Create a combined test set from all clients
        all_test_samples = []
        
        for client in self.clients.values():
            for batch in client.test_dataloader:
                for i in range(len(batch['input_ids'])):
                    all_test_samples.append({
                        'text': batch['text'][i],
                        'label': batch['labels'][i].item(),
                        'domain': batch['domain'][i]
                    })
        
        # Create global test dataloader
        from ..data.amazon_dataset import AmazonReviewDataset
        from transformers import AutoTokenizer
        
        tokenizer = AutoTokenizer.from_pretrained(self.config.model.student_model)
        
        test_dataset = AmazonReviewDataset(
            texts=[s['text'] for s in all_test_samples],
            labels=[s['label'] for s in all_test_samples],
            domains=[s['domain'] for s in all_test_samples],
            tokenizer=tokenizer,
            max_length=self.config.data.max_seq_length
        )
        
        test_dataloader = torch.utils.data.DataLoader(
            test_dataset, batch_size=32, shuffle=False
        )
        
        # Evaluate
        results = self.server.evaluate_global_model(test_dataloader)
        results['round'] = round_identifier

        self.experiment_results['global_model_performance'].append(results)

        logger.info(f"🎯 {round_identifier} Global Model Evaluation:")
        logger.info(f"   📊 Accuracy: {results['accuracy']:.4f} ({results['accuracy']*100:.2f}%)")
        logger.info(f"   📈 F1-Macro: {results['f1_macro']:.4f}")
        logger.info(f"   📉 Loss: {results['loss']:.4f}")
        logger.info(f"   📋 Test Samples: {len(all_test_samples)}")
    
    def _final_evaluation(self) -> Dict[str, Any]:
        """Perform final comprehensive evaluation"""
        logger.info("Performing final evaluation...")
        
        results = {
            'global_model': {},
            'personalized_models': {},
            'domain_analysis': {},
            'training_summary': {}
        }
        
        # Evaluate global model
        self._evaluate_global_model('final')
        results['global_model'] = self.experiment_results['global_model_performance'][-1]
        
        # Evaluate personalized models
        for client_id, client in self.clients.items():
            if client.personalized_model is not None:
                # Test on local test set
                local_results = client.evaluate_model(
                    client.personalized_model, client.test_dataloader
                )
                
                # Test on global test set (if available)
                # ... (implementation similar to global model evaluation)
                
                results['personalized_models'][client_id] = local_results
                
                logger.info(f"Client {client_id} Personalized Model - "
                           f"Accuracy: {local_results['accuracy']:.4f}")
        
        # Training summary
        results['training_summary'] = self.server.get_training_statistics()
        
        # Save results
        self._save_experiment_results(results)
        
        return results
    
    def _save_experiment_results(self, results: Dict[str, Any]):
        """Save experiment results to disk"""
        output_dir = self.config.experiment.output_dir
        os.makedirs(output_dir, exist_ok=True)
        
        # Save detailed results
        results_path = os.path.join(output_dir, "experiment_results.json")
        with open(results_path, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        # Save server state
        server_dir = os.path.join(output_dir, "server_state")
        self.server.save_server_state(server_dir)
        
        logger.info(f"Experiment results saved to {output_dir}")
    
    def load_experiment_state(self, load_dir: str):
        """Load experiment state from disk"""
        # Load server state
        server_dir = os.path.join(load_dir, "server_state")
        if os.path.exists(server_dir):
            self.server.load_server_state(server_dir)
        
        # Load experiment results
        results_path = os.path.join(load_dir, "experiment_results.json")
        if os.path.exists(results_path):
            with open(results_path, 'r') as f:
                self.experiment_results = json.load(f)
        
        logger.info(f"Experiment state loaded from {load_dir}")

    def _check_convergence(self, patience: int = 3, min_improvement: float = 0.001) -> bool:
        """Check if the model has converged"""
        if len(self.experiment_results['global_model_performance']) < patience + 1:
            return False

        # Get recent accuracies
        recent_accs = [
            result['accuracy']
            for result in self.experiment_results['global_model_performance'][-patience-1:]
        ]

        # Check if improvement is minimal
        max_recent_improvement = max(
            recent_accs[i+1] - recent_accs[i]
            for i in range(len(recent_accs)-1)
        )

        return max_recent_improvement < min_improvement

    def get_federated_round_summary(self) -> Dict[str, Any]:
        """Get summary of federated training progress"""
        if not self.experiment_results['training_history']:
            return {}

        # Group by federated rounds
        fed_round_stats = {}
        for round_data in self.experiment_results['training_history']:
            fed_round = round_data.get('federated_round', 1)
            if fed_round not in fed_round_stats:
                fed_round_stats[fed_round] = {
                    'distillation_losses': [],
                    'contrastive_losses': [],
                    'total_losses': []
                }

            fed_round_stats[fed_round]['distillation_losses'].append(
                round_data.get('dkd_loss', 0)
            )
            fed_round_stats[fed_round]['contrastive_losses'].append(
                round_data.get('contrastive_loss', 0)
            )
            fed_round_stats[fed_round]['total_losses'].append(
                round_data.get('total_loss', 0)
            )

        # Calculate averages for each federated round
        summary = {}
        for fed_round, stats in fed_round_stats.items():
            summary[f"fed_round_{fed_round}"] = {
                'avg_dkd_loss': np.mean(stats['distillation_losses']),
                'avg_contrastive_loss': np.mean(stats['contrastive_losses']),
                'avg_total_loss': np.mean(stats['total_losses']),
                'num_distillation_rounds': len(stats['total_losses'])
            }

        return summary
