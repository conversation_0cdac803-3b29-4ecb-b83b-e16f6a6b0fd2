"""
Restart D³AFD experiment with NaN fixes applied
This script applies all the numerical stability fixes and restarts the experiment
"""
import os
import sys
import subprocess
import logging
import torch

def setup_logging():
    """Setup logging"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def clear_gpu_memory():
    """Clear GPU memory"""
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        torch.cuda.ipc_collect()
        import gc
        gc.collect()

def check_gpu_status():
    """Check GPU memory status"""
    logger = logging.getLogger(__name__)
    
    if torch.cuda.is_available():
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        allocated = torch.cuda.memory_allocated(0) / 1024**3
        reserved = torch.cuda.memory_reserved(0) / 1024**3
        
        logger.info(f"GPU Memory Status:")
        logger.info(f"  Total: {gpu_memory:.2f} GB")
        logger.info(f"  Allocated: {allocated:.2f} GB")
        logger.info(f"  Reserved: {reserved:.2f} GB")
        logger.info(f"  Free: {gpu_memory - reserved:.2f} GB")
        
        return gpu_memory - reserved
    else:
        logger.info("CUDA not available")
        return 0

def apply_environment_fixes():
    """Apply environment variable fixes"""
    logger = logging.getLogger(__name__)
    
    # Set CUDA memory allocation configuration
    os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'expandable_segments:True,max_split_size_mb:128'
    
    # Set other optimization variables
    os.environ['CUDA_LAUNCH_BLOCKING'] = '1'
    os.environ['PYTHONHASHSEED'] = '0'
    
    logger.info("✅ Applied environment variable fixes:")
    logger.info(f"   PYTORCH_CUDA_ALLOC_CONF = {os.environ.get('PYTORCH_CUDA_ALLOC_CONF')}")

def main():
    """Main function"""
    logger = setup_logging()
    
    logger.info("=" * 60)
    logger.info("🔧 Restarting D³AFD Experiment with NaN Fixes")
    logger.info("=" * 60)
    
    # Apply environment fixes
    apply_environment_fixes()
    
    # Clear GPU memory
    logger.info("🧹 Clearing GPU memory...")
    clear_gpu_memory()
    
    # Check GPU status
    free_memory = check_gpu_status()
    
    # Determine which configuration to use
    if free_memory < 4:
        config_choice = "ultra_low"
        script_name = "run_ultra_low_memory.py"
        logger.info("🔧 Using ultra-low memory configuration (< 4GB free)")
    elif free_memory < 8:
        config_choice = "memory_optimized"
        script_name = "run_memory_optimized.py"
        logger.info("🔧 Using memory-optimized configuration (< 8GB free)")
    else:
        config_choice = "memory_optimized"  # Still use optimized for safety
        script_name = "run_memory_optimized.py"
        logger.info("🔧 Using memory-optimized configuration (safe choice)")
    
    logger.info(f"📝 Applied NaN fixes:")
    logger.info(f"   ✅ DKD loss numerical stability")
    logger.info(f"   ✅ Contrastive loss NaN handling")
    logger.info(f"   ✅ Server-side NaN detection and skipping")
    logger.info(f"   ✅ Gradient NaN checking")
    logger.info(f"   ✅ Division by zero protection")
    
    # Start the experiment
    logger.info(f"🚀 Starting experiment with {script_name}...")
    
    try:
        # Run the experiment
        result = subprocess.run(
            [sys.executable, script_name],
            env=os.environ.copy(),
            cwd=os.getcwd()
        )
        
        if result.returncode == 0:
            logger.info("✅ Experiment completed successfully!")
        else:
            logger.error(f"❌ Experiment failed with return code: {result.returncode}")
            
            # Try fallback to CPU if GPU experiment failed
            if config_choice != "cpu":
                logger.info("🔄 Trying CPU fallback...")
                result_cpu = subprocess.run(
                    [sys.executable, "run_cpu_demo.py"],
                    env=os.environ.copy(),
                    cwd=os.getcwd()
                )
                
                if result_cpu.returncode == 0:
                    logger.info("✅ CPU experiment completed successfully!")
                else:
                    logger.error("❌ CPU experiment also failed")
                    return 1
        
        return 0
        
    except FileNotFoundError as e:
        logger.error(f"❌ Script not found: {e}")
        logger.error("Please make sure you're in the correct directory")
        return 1
    except Exception as e:
        logger.error(f"❌ Unexpected error: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
