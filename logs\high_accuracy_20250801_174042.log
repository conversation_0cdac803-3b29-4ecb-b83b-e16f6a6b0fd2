2025-08-02 01:30:25,714 - __main__ - INFO - Memory-efficient high accuracy experiment logging initialized. Log file: logs/memory_efficient_high_accuracy_20250802_013025.log
2025-08-02 01:30:25,714 - __main__ - INFO - Starting Memory-Efficient High Accuracy D³AFD Experiment
2025-08-02 01:30:25,714 - __main__ - INFO - ======================================================================
2025-08-02 01:30:25,714 - __main__ - INFO - 🎯 TARGET: Achieve 60-80% global accuracy with efficient memory usage
2025-08-02 01:30:25,714 - __main__ - INFO - 🔧 Memory optimizations applied:
2025-08-02 01:30:25,714 - __main__ - INFO -    - DistilBERT models for memory efficiency
2025-08-02 01:30:25,714 - __main__ - INFO -    - T5-small generator for manageable memory
2025-08-02 01:30:25,714 - __main__ - INFO -    - Smaller batch sizes (2/4)
2025-08-02 01:30:25,714 - __main__ - INFO -    - Reasonable sequence length (256)
2025-08-02 01:30:25,714 - __main__ - INFO -    - Aggressive memory management
2025-08-02 01:30:25,714 - __main__ - INFO -    - More training rounds to compensate
2025-08-02 01:30:25,771 - __main__ - INFO - Memory check: GPU memory: 14.58GB
2025-08-02 01:30:28,205 - __main__ - INFO - Set GPU memory fraction to 80%
2025-08-02 01:30:28,205 - __main__ - INFO - Memory-Efficient High Accuracy Configuration:
2025-08-02 01:30:28,205 - __main__ - INFO -   🏢 Clients: 8
2025-08-02 01:30:28,206 - __main__ - INFO -   📊 Samples per client: 500
2025-08-02 01:30:28,206 - __main__ - INFO -   📏 Max sequence length: 256
2025-08-02 01:30:28,206 - __main__ - INFO -   🤖 Teacher model: distilbert-base-uncased
2025-08-02 01:30:28,206 - __main__ - INFO -   📝 Generator model: t5-small
2025-08-02 01:30:28,206 - __main__ - INFO -   🔄 Federated rounds: 12
2025-08-02 01:30:28,206 - __main__ - INFO -   🧪 Distillation rounds: 8
2025-08-02 01:30:28,206 - __main__ - INFO -   🎭 Pseudo samples per domain: 150
2025-08-02 01:30:28,206 - __main__ - INFO -   📦 Local batch size: 2
2025-08-02 01:30:28,206 - __main__ - INFO -   📦 Distillation batch size: 4
2025-08-02 01:30:28,206 - __main__ - INFO - Initializing D³AFD Framework...
2025-08-02 01:30:31,303 - src.federated.server - INFO - Federated server initialized
2025-08-02 01:30:32,052 - src.federated.d3afd_framework - INFO - D³AFD Framework initialized
2025-08-02 01:30:32,657 - __main__ - INFO - Memory after initialization - Allocated: 0.47GB, Reserved: 0.48GB
2025-08-02 01:30:32,658 - __main__ - INFO - Starting memory-efficient high accuracy training...
2025-08-02 01:30:32,658 - __main__ - INFO - 💡 Strategy:
2025-08-02 01:30:32,658 - __main__ - INFO -    - Use smaller models but train longer
2025-08-02 01:30:32,658 - __main__ - INFO -    - Smaller batches but more epochs
2025-08-02 01:30:32,658 - __main__ - INFO -    - Better loss balancing
2025-08-02 01:30:32,658 - __main__ - INFO -    - Aggressive memory management
2025-08-02 01:30:32,658 - src.federated.d3afd_framework - INFO - Starting complete D³AFD training process...
2025-08-02 01:30:32,658 - src.federated.d3afd_framework - INFO - Following D³AFD four-stage design: Local Training → Pseudo Data → DKD Distillation → Personalization
2025-08-02 01:30:32,658 - src.federated.d3afd_framework - INFO - Setting up federated data distribution...
2025-08-02 01:30:32,658 - src.federated.d3afd_framework - INFO - Loading existing federated data split...
2025-08-02 01:30:32,660 - src.data.amazon_dataset - INFO - Loaded federated data for 8 clients
2025-08-02 01:30:32,660 - src.federated.d3afd_framework - INFO - Federated data setup complete:
2025-08-02 01:30:32,660 - src.federated.d3afd_framework - INFO -   - Total clients: 8
2025-08-02 01:30:32,660 - src.federated.d3afd_framework - INFO -   - Domain distribution: {'Books': 1500, 'Home_and_Kitchen': 1250, 'Electronics': 1250}
2025-08-02 01:30:32,660 - src.federated.d3afd_framework - INFO - Initializing federated clients...
2025-08-02 01:30:33,094 - src.federated.client - INFO - Client 0 initialized with domains: ['Electronics']
2025-08-02 01:30:33,812 - src.federated.client - INFO - Client 0: Models initialized
2025-08-02 01:30:33,812 - src.federated.server - INFO - Registered client 0 with domains: ['Electronics']
2025-08-02 01:30:33,812 - src.federated.d3afd_framework - INFO - Client 0 initialized with 500 samples
2025-08-02 01:30:34,226 - src.federated.client - INFO - Client 1 initialized with domains: ['Books']
2025-08-02 01:30:34,966 - src.federated.client - INFO - Client 1: Models initialized
2025-08-02 01:30:34,966 - src.federated.server - INFO - Registered client 1 with domains: ['Books']
2025-08-02 01:30:34,966 - src.federated.d3afd_framework - INFO - Client 1 initialized with 500 samples
2025-08-02 01:30:35,383 - src.federated.client - INFO - Client 2 initialized with domains: ['Home_and_Kitchen']
2025-08-02 01:30:36,103 - src.federated.client - INFO - Client 2: Models initialized
2025-08-02 01:30:36,103 - src.federated.server - INFO - Registered client 2 with domains: ['Home_and_Kitchen']
2025-08-02 01:30:36,103 - src.federated.d3afd_framework - INFO - Client 2 initialized with 500 samples
2025-08-02 01:30:36,505 - src.federated.client - INFO - Client 3 initialized with domains: ['Electronics', 'Books']
2025-08-02 01:30:37,229 - src.federated.client - INFO - Client 3: Models initialized
2025-08-02 01:30:37,230 - src.federated.server - INFO - Registered client 3 with domains: ['Electronics', 'Books']
2025-08-02 01:30:37,230 - src.federated.d3afd_framework - INFO - Client 3 initialized with 500 samples
2025-08-02 01:30:37,587 - src.federated.client - INFO - Client 4 initialized with domains: ['Home_and_Kitchen', 'Books']
2025-08-02 01:30:38,310 - src.federated.client - INFO - Client 4: Models initialized
2025-08-02 01:30:38,310 - src.federated.server - INFO - Registered client 4 with domains: ['Home_and_Kitchen', 'Books']
2025-08-02 01:30:38,310 - src.federated.d3afd_framework - INFO - Client 4 initialized with 500 samples
2025-08-02 01:30:38,727 - src.federated.client - INFO - Client 5 initialized with domains: ['Electronics', 'Home_and_Kitchen']
2025-08-02 01:30:39,459 - src.federated.client - INFO - Client 5: Models initialized
2025-08-02 01:30:39,459 - src.federated.server - INFO - Registered client 5 with domains: ['Electronics', 'Home_and_Kitchen']
2025-08-02 01:30:39,459 - src.federated.d3afd_framework - INFO - Client 5 initialized with 500 samples
2025-08-02 01:30:39,881 - src.federated.client - INFO - Client 6 initialized with domains: ['Electronics', 'Books']
2025-08-02 01:30:40,677 - src.federated.client - INFO - Client 6: Models initialized
2025-08-02 01:30:40,677 - src.federated.server - INFO - Registered client 6 with domains: ['Electronics', 'Books']
2025-08-02 01:30:40,677 - src.federated.d3afd_framework - INFO - Client 6 initialized with 500 samples
2025-08-02 01:30:41,083 - src.federated.client - INFO - Client 7 initialized with domains: ['Books', 'Home_and_Kitchen']
2025-08-02 01:30:41,801 - src.federated.client - INFO - Client 7: Models initialized
2025-08-02 01:30:41,802 - src.federated.server - INFO - Registered client 7 with domains: ['Books', 'Home_and_Kitchen']
2025-08-02 01:30:41,802 - src.federated.d3afd_framework - INFO - Client 7 initialized with 500 samples
2025-08-02 01:30:41,802 - src.federated.d3afd_framework - INFO - Initialized 8 clients
2025-08-02 01:30:41,802 - src.federated.d3afd_framework - INFO - === Initial Setup Phase ===
2025-08-02 01:30:41,802 - src.federated.d3afd_framework - INFO - Training initial local teacher models...
2025-08-02 01:30:41,803 - src.federated.client - INFO - Client 0: Training local teacher for 4 epochs
2025-08-02 01:30:53,848 - src.federated.client - INFO - Client 0 - Epoch 1/4: Loss: 0.5200, Accuracy: 0.9543
2025-08-02 01:31:05,191 - src.federated.client - INFO - Client 0 - Epoch 2/4: Loss: 0.3917, Accuracy: 1.0000
2025-08-02 01:31:05,191 - src.federated.client - WARNING - Client 0: Early stopping due to potential overfitting (accuracy: 1.0000)
2025-08-02 01:31:05,191 - src.federated.client - INFO - Client 0: Local teacher training completed. Final accuracy: 0.9771
2025-08-02 01:31:05,192 - src.federated.d3afd_framework - INFO - Client 0 teacher training: Accuracy 0.9771
2025-08-02 01:31:05,193 - src.federated.client - INFO - Client 1: Training local teacher for 4 epochs
2025-08-02 01:31:16,388 - src.federated.client - INFO - Client 1 - Epoch 1/4: Loss: 0.5020, Accuracy: 0.9514
2025-08-02 01:31:28,042 - src.federated.client - INFO - Client 1 - Epoch 2/4: Loss: 0.3915, Accuracy: 1.0000
2025-08-02 01:31:28,042 - src.federated.client - WARNING - Client 1: Early stopping due to potential overfitting (accuracy: 1.0000)
2025-08-02 01:31:28,043 - src.federated.client - INFO - Client 1: Local teacher training completed. Final accuracy: 0.9757
2025-08-02 01:31:28,043 - src.federated.d3afd_framework - INFO - Client 1 teacher training: Accuracy 0.9757
2025-08-02 01:31:28,044 - src.federated.client - INFO - Client 2: Training local teacher for 4 epochs
2025-08-02 01:31:39,093 - src.federated.client - INFO - Client 2 - Epoch 1/4: Loss: 0.4936, Accuracy: 0.9686
2025-08-02 01:31:50,221 - src.federated.client - INFO - Client 2 - Epoch 2/4: Loss: 0.3919, Accuracy: 1.0000
2025-08-02 01:31:50,222 - src.federated.client - WARNING - Client 2: Early stopping due to potential overfitting (accuracy: 1.0000)
2025-08-02 01:31:50,222 - src.federated.client - INFO - Client 2: Local teacher training completed. Final accuracy: 0.9843
2025-08-02 01:31:50,223 - src.federated.d3afd_framework - INFO - Client 2 teacher training: Accuracy 0.9843
2025-08-02 01:31:50,223 - src.federated.client - INFO - Client 3: Training local teacher for 4 epochs
2025-08-02 01:32:01,450 - src.federated.client - INFO - Client 3 - Epoch 1/4: Loss: 0.5737, Accuracy: 0.9257
2025-08-02 01:32:12,689 - src.federated.client - INFO - Client 3 - Epoch 2/4: Loss: 0.3921, Accuracy: 1.0000
2025-08-02 01:32:12,689 - src.federated.client - WARNING - Client 3: Early stopping due to potential overfitting (accuracy: 1.0000)
2025-08-02 01:32:12,689 - src.federated.client - INFO - Client 3: Local teacher training completed. Final accuracy: 0.9629
2025-08-02 01:32:12,690 - src.federated.d3afd_framework - INFO - Client 3 teacher training: Accuracy 0.9629
2025-08-02 01:32:12,691 - src.federated.client - INFO - Client 4: Training local teacher for 4 epochs
2025-08-02 01:32:24,191 - src.federated.client - INFO - Client 4 - Epoch 1/4: Loss: 0.5729, Accuracy: 0.9257
2025-08-02 01:32:36,234 - src.federated.client - INFO - Client 4 - Epoch 2/4: Loss: 0.3919, Accuracy: 1.0000
2025-08-02 01:32:36,235 - src.federated.client - WARNING - Client 4: Early stopping due to potential overfitting (accuracy: 1.0000)
2025-08-02 01:32:36,235 - src.federated.client - INFO - Client 4: Local teacher training completed. Final accuracy: 0.9629
2025-08-02 01:32:36,235 - src.federated.d3afd_framework - INFO - Client 4 teacher training: Accuracy 0.9629
2025-08-02 01:32:36,236 - src.federated.client - INFO - Client 5: Training local teacher for 4 epochs
2025-08-02 01:32:47,580 - src.federated.client - INFO - Client 5 - Epoch 1/4: Loss: 0.5754, Accuracy: 0.9343
2025-08-02 01:32:59,028 - src.federated.client - INFO - Client 5 - Epoch 2/4: Loss: 0.3915, Accuracy: 1.0000
2025-08-02 01:32:59,029 - src.federated.client - WARNING - Client 5: Early stopping due to potential overfitting (accuracy: 1.0000)
2025-08-02 01:32:59,029 - src.federated.client - INFO - Client 5: Local teacher training completed. Final accuracy: 0.9671
2025-08-02 01:32:59,030 - src.federated.d3afd_framework - INFO - Client 5 teacher training: Accuracy 0.9671
2025-08-02 01:32:59,030 - src.federated.client - INFO - Client 6: Training local teacher for 4 epochs
2025-08-02 01:33:10,211 - src.federated.client - INFO - Client 6 - Epoch 1/4: Loss: 0.5754, Accuracy: 0.9114
2025-08-02 01:33:21,828 - src.federated.client - INFO - Client 6 - Epoch 2/4: Loss: 0.3920, Accuracy: 1.0000
2025-08-02 01:33:21,829 - src.federated.client - WARNING - Client 6: Early stopping due to potential overfitting (accuracy: 1.0000)
2025-08-02 01:33:21,829 - src.federated.client - INFO - Client 6: Local teacher training completed. Final accuracy: 0.9557
2025-08-02 01:33:21,830 - src.federated.d3afd_framework - INFO - Client 6 teacher training: Accuracy 0.9557
2025-08-02 01:33:21,830 - src.federated.client - INFO - Client 7: Training local teacher for 4 epochs
2025-08-02 01:33:33,304 - src.federated.client - INFO - Client 7 - Epoch 1/4: Loss: 0.5678, Accuracy: 0.9343
2025-08-02 01:33:44,725 - src.federated.client - INFO - Client 7 - Epoch 2/4: Loss: 0.3919, Accuracy: 1.0000
2025-08-02 01:33:44,725 - src.federated.client - WARNING - Client 7: Early stopping due to potential overfitting (accuracy: 1.0000)
2025-08-02 01:33:44,725 - src.federated.client - INFO - Client 7: Local teacher training completed. Final accuracy: 0.9671
2025-08-02 01:33:44,726 - src.federated.d3afd_framework - INFO - Client 7 teacher training: Accuracy 0.9671
2025-08-02 01:33:44,727 - src.federated.d3afd_framework - INFO - Training domain discriminators...
2025-08-02 01:33:48,238 - src.federated.client - INFO - Client 0: Training domain discriminator
2025-08-02 01:48:18,491 - src.federated.client - INFO - Client 0: Domain discriminator training completed. Accuracy: 0.8810
2025-08-02 01:48:18,496 - src.federated.d3afd_framework - INFO - Client 0 discriminator training: Accuracy 0.8810
2025-08-02 01:48:18,497 - src.federated.client - INFO - Client 1: Training domain discriminator
2025-08-02 02:02:38,985 - src.federated.client - INFO - Client 1: Domain discriminator training completed. Accuracy: 0.8429
2025-08-02 02:02:38,989 - src.federated.d3afd_framework - INFO - Client 1 discriminator training: Accuracy 0.8429
2025-08-02 02:02:38,990 - src.federated.client - INFO - Client 2: Training domain discriminator
2025-08-02 02:17:02,058 - src.federated.client - INFO - Client 2: Domain discriminator training completed. Accuracy: 0.8852
2025-08-02 02:17:02,064 - src.federated.d3afd_framework - INFO - Client 2 discriminator training: Accuracy 0.8852
2025-08-02 02:17:02,065 - src.federated.client - INFO - Client 3: Training domain discriminator
2025-08-02 02:31:38,852 - src.federated.client - INFO - Client 3: Domain discriminator training completed. Accuracy: 0.6462
2025-08-02 02:31:38,856 - src.federated.d3afd_framework - INFO - Client 3 discriminator training: Accuracy 0.6462
2025-08-02 02:31:38,857 - src.federated.client - INFO - Client 4: Training domain discriminator
2025-08-02 02:46:11,165 - src.federated.client - INFO - Client 4: Domain discriminator training completed. Accuracy: 0.6671
2025-08-02 02:46:11,170 - src.federated.d3afd_framework - INFO - Client 4 discriminator training: Accuracy 0.6671
2025-08-02 02:46:11,170 - src.federated.client - INFO - Client 5: Training domain discriminator
2025-08-02 03:00:30,242 - src.federated.client - INFO - Client 5: Domain discriminator training completed. Accuracy: 0.7086
2025-08-02 03:00:30,248 - src.federated.d3afd_framework - INFO - Client 5 discriminator training: Accuracy 0.7086
2025-08-02 03:00:30,248 - src.federated.client - INFO - Client 6: Training domain discriminator
2025-08-02 03:14:46,006 - src.federated.client - INFO - Client 6: Domain discriminator training completed. Accuracy: 0.6452
2025-08-02 03:14:46,011 - src.federated.d3afd_framework - INFO - Client 6 discriminator training: Accuracy 0.6452
2025-08-02 03:14:46,012 - src.federated.client - INFO - Client 7: Training domain discriminator
2025-08-02 03:29:25,882 - src.federated.client - INFO - Client 7: Domain discriminator training completed. Accuracy: 0.6514
2025-08-02 03:29:25,888 - src.federated.d3afd_framework - INFO - Client 7 discriminator training: Accuracy 0.6514
2025-08-02 03:29:25,889 - src.federated.d3afd_framework - INFO - Initial setup completed
2025-08-02 03:29:25,890 - src.federated.d3afd_framework - INFO - === D³AFD Federated Learning Rounds ===
2025-08-02 03:29:25,890 - src.federated.d3afd_framework - INFO - Total federated rounds: 12
2025-08-02 03:29:25,890 - src.federated.d3afd_framework - INFO - Each round includes: Local Training → Pseudo Data Generation → DKD Distillation → Personalization
2025-08-02 03:29:25,890 - src.federated.d3afd_framework - INFO - 
============================================================
2025-08-02 03:29:25,890 - src.federated.d3afd_framework - INFO - FEDERATED ROUND 1/12
2025-08-02 03:29:25,890 - src.federated.d3afd_framework - INFO - ============================================================
2025-08-02 03:29:25,890 - src.federated.d3afd_framework - INFO - Stage 1: Using initial local models
2025-08-02 03:29:25,890 - src.federated.d3afd_framework - INFO - Stage 2-3: Pseudo Data Generation & DKD Distillation
2025-08-02 03:29:25,891 - src.federated.d3afd_framework - INFO -   Distillation Round 1/8
2025-08-02 03:29:25,891 - src.federated.server - INFO - Starting distillation round 1
2025-08-02 03:29:25,891 - src.utils.memory_manager - INFO - GPU Memory start_distillation_round_1: Allocated: 8.41GB, Reserved: 11.12GB, Free: 3.46GB
2025-08-02 03:29:25,891 - src.federated.server - INFO - Round 1: Generating pseudo data
2025-08-02 03:29:25,891 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Books
2025-08-02 03:30:18,156 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Electronics
2025-08-02 03:31:09,864 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Home_and_Kitchen
2025-08-02 03:32:01,326 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Sports_and_Outdoors
2025-08-02 03:32:52,801 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Toys_and_Games
2025-08-02 03:33:44,242 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Clothing_Shoes_and_Jewelry
2025-08-02 03:34:34,529 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Health_and_Personal_Care
2025-08-02 03:35:26,929 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Automotive
2025-08-02 03:36:18,740 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Tools_and_Home_Improvement
2025-08-02 03:37:10,321 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Beauty
2025-08-02 03:38:01,696 - src.models.text_generator - INFO - Generating 50 mixed domain samples
2025-08-02 03:38:21,511 - src.federated.server - INFO - Generated 1550 pseudo samples
2025-08-02 03:38:22,457 - src.federated.server - INFO - Collecting teacher predictions from clients
2025-08-02 03:43:35,400 - src.federated.server - INFO - Domain Electronics: Teacher predictions shape torch.Size([1550, 5])
2025-08-02 03:43:35,400 - src.federated.server - INFO - Domain Books: Teacher predictions shape torch.Size([1550, 5])
2025-08-02 03:43:35,401 - src.federated.server - INFO - Domain Home_and_Kitchen: Teacher predictions shape torch.Size([1550, 5])
2025-08-02 03:43:35,561 - src.utils.memory_manager - INFO - GPU Memory after_teacher_predictions_round_1: Allocated: 8.41GB, Reserved: 8.60GB, Free: 5.98GB
2025-08-02 03:43:35,561 - src.federated.server - INFO - Collecting domain weights from clients
2025-08-02 03:52:33,854 - src.federated.server - INFO - Processed 1164 valid batches out of 388 total batches per epoch
2025-08-02 03:52:33,854 - src.federated.server - INFO - Round 1 completed: DKD Loss: 0.0012, Contrastive Loss: 1.0652, Total Loss: 0.4273, Processed Batches: 1164
2025-08-02 03:52:34,468 - src.utils.memory_manager - INFO - GPU Memory end_distillation_round_1: Allocated: 9.16GB, Reserved: 9.42GB, Free: 5.16GB
2025-08-02 03:52:34,476 - src.federated.d3afd_framework - INFO -   Distillation Round 2/8
2025-08-02 03:52:34,476 - src.federated.server - INFO - Starting distillation round 2
2025-08-02 03:52:34,477 - src.utils.memory_manager - INFO - GPU Memory start_distillation_round_2: Allocated: 8.66GB, Reserved: 9.42GB, Free: 5.16GB
2025-08-02 03:52:34,477 - src.federated.server - INFO - Round 2: Generating pseudo data
2025-08-02 03:52:34,477 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Books
2025-08-02 03:53:28,172 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Electronics
2025-08-02 03:54:19,685 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Home_and_Kitchen
2025-08-02 03:55:11,441 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Sports_and_Outdoors
2025-08-02 03:56:02,061 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Toys_and_Games
2025-08-02 03:56:53,574 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Clothing_Shoes_and_Jewelry
2025-08-02 03:57:45,138 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Health_and_Personal_Care
2025-08-02 03:58:36,635 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Automotive
2025-08-02 03:59:27,948 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Tools_and_Home_Improvement
2025-08-02 04:00:19,217 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Beauty
2025-08-02 04:01:12,145 - src.models.text_generator - INFO - Generating 50 mixed domain samples
2025-08-02 04:01:31,160 - src.federated.server - INFO - Generated 1550 pseudo samples
2025-08-02 04:01:32,093 - src.federated.server - INFO - Collecting teacher predictions from clients
2025-08-02 04:06:49,047 - src.federated.server - INFO - Domain Electronics: Teacher predictions shape torch.Size([1550, 5])
2025-08-02 04:06:49,047 - src.federated.server - INFO - Domain Books: Teacher predictions shape torch.Size([1550, 5])
2025-08-02 04:06:49,047 - src.federated.server - INFO - Domain Home_and_Kitchen: Teacher predictions shape torch.Size([1550, 5])
2025-08-02 04:06:49,210 - src.utils.memory_manager - INFO - GPU Memory after_teacher_predictions_round_2: Allocated: 8.66GB, Reserved: 9.03GB, Free: 5.55GB
2025-08-02 04:06:49,210 - src.federated.server - INFO - Collecting domain weights from clients
2025-08-02 04:15:47,329 - src.federated.server - INFO - Processed 1164 valid batches out of 388 total batches per epoch
2025-08-02 04:15:47,329 - src.federated.server - INFO - Round 2 completed: DKD Loss: 0.0011, Contrastive Loss: 1.0532, Total Loss: 0.4224, Processed Batches: 1164
2025-08-02 04:15:47,972 - src.utils.memory_manager - INFO - GPU Memory end_distillation_round_2: Allocated: 9.16GB, Reserved: 9.44GB, Free: 5.14GB
2025-08-02 04:15:47,979 - src.federated.d3afd_framework - INFO -   Distillation Round 3/8
2025-08-02 04:15:47,979 - src.federated.server - INFO - Starting distillation round 3
2025-08-02 04:15:47,980 - src.utils.memory_manager - INFO - GPU Memory start_distillation_round_3: Allocated: 8.66GB, Reserved: 9.44GB, Free: 5.14GB
2025-08-02 04:15:47,980 - src.federated.server - INFO - Round 3: Generating pseudo data
2025-08-02 04:15:47,980 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Books
2025-08-02 04:16:40,523 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Electronics
2025-08-02 04:17:30,833 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Home_and_Kitchen
2025-08-02 04:18:22,947 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Sports_and_Outdoors
2025-08-02 04:19:14,366 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Toys_and_Games
2025-08-02 04:20:06,275 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Clothing_Shoes_and_Jewelry
2025-08-02 04:20:57,707 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Health_and_Personal_Care
2025-08-02 04:21:48,485 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Automotive
2025-08-02 04:22:39,693 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Tools_and_Home_Improvement
2025-08-02 04:23:30,465 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Beauty
2025-08-02 04:24:21,190 - src.models.text_generator - INFO - Generating 50 mixed domain samples
2025-08-02 04:24:40,571 - src.federated.server - INFO - Generated 1550 pseudo samples
2025-08-02 04:24:41,499 - src.federated.server - INFO - Collecting teacher predictions from clients
2025-08-02 04:29:53,328 - src.federated.server - INFO - Domain Electronics: Teacher predictions shape torch.Size([1550, 5])
2025-08-02 04:29:53,329 - src.federated.server - INFO - Domain Books: Teacher predictions shape torch.Size([1550, 5])
2025-08-02 04:29:53,329 - src.federated.server - INFO - Domain Home_and_Kitchen: Teacher predictions shape torch.Size([1550, 5])
2025-08-02 04:29:53,493 - src.utils.memory_manager - INFO - GPU Memory after_teacher_predictions_round_3: Allocated: 8.66GB, Reserved: 9.01GB, Free: 5.57GB
2025-08-02 04:29:53,493 - src.federated.server - INFO - Collecting domain weights from clients
2025-08-02 04:38:53,601 - src.federated.server - INFO - Processed 1164 valid batches out of 388 total batches per epoch
2025-08-02 04:38:53,601 - src.federated.server - INFO - Round 3 completed: DKD Loss: 0.0011, Contrastive Loss: 1.0398, Total Loss: 0.4170, Processed Batches: 1164
2025-08-02 04:38:54,237 - src.utils.memory_manager - INFO - GPU Memory end_distillation_round_3: Allocated: 9.16GB, Reserved: 9.44GB, Free: 5.14GB
2025-08-02 04:38:54,245 - src.federated.d3afd_framework - INFO -   Distillation Round 4/8
2025-08-02 04:38:54,245 - src.federated.server - INFO - Starting distillation round 4
2025-08-02 04:38:54,246 - src.utils.memory_manager - INFO - GPU Memory start_distillation_round_4: Allocated: 8.66GB, Reserved: 9.44GB, Free: 5.14GB
2025-08-02 04:38:54,246 - src.federated.server - INFO - Round 4: Generating pseudo data
2025-08-02 04:38:54,246 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Books
2025-08-02 04:39:48,110 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Electronics
2025-08-02 04:40:39,925 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Home_and_Kitchen
2025-08-02 04:41:31,184 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Sports_and_Outdoors
2025-08-02 04:42:22,726 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Toys_and_Games
2025-08-02 04:43:14,839 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Clothing_Shoes_and_Jewelry
2025-08-02 04:44:06,004 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Health_and_Personal_Care
2025-08-02 04:44:58,074 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Automotive
2025-08-02 04:45:49,253 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Tools_and_Home_Improvement
2025-08-02 04:46:40,801 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Beauty
2025-08-02 04:47:31,865 - src.models.text_generator - INFO - Generating 50 mixed domain samples
2025-08-02 04:47:50,461 - src.federated.server - INFO - Generated 1550 pseudo samples
2025-08-02 04:47:51,416 - src.federated.server - INFO - Collecting teacher predictions from clients
2025-08-02 04:53:07,805 - src.federated.server - INFO - Domain Electronics: Teacher predictions shape torch.Size([1550, 5])
2025-08-02 04:53:07,806 - src.federated.server - INFO - Domain Books: Teacher predictions shape torch.Size([1550, 5])
2025-08-02 04:53:07,806 - src.federated.server - INFO - Domain Home_and_Kitchen: Teacher predictions shape torch.Size([1550, 5])
2025-08-02 04:53:07,965 - src.utils.memory_manager - INFO - GPU Memory after_teacher_predictions_round_4: Allocated: 8.66GB, Reserved: 9.03GB, Free: 5.55GB
2025-08-02 04:53:07,965 - src.federated.server - INFO - Collecting domain weights from clients
2025-08-02 05:02:04,922 - src.federated.server - INFO - Processed 1164 valid batches out of 388 total batches per epoch
2025-08-02 05:02:04,923 - src.federated.server - INFO - Round 4 completed: DKD Loss: 0.0012, Contrastive Loss: 1.0601, Total Loss: 0.4252, Processed Batches: 1164
2025-08-02 05:02:05,567 - src.utils.memory_manager - INFO - GPU Memory end_distillation_round_4: Allocated: 9.16GB, Reserved: 9.42GB, Free: 5.16GB
2025-08-02 05:02:05,574 - src.federated.d3afd_framework - INFO -   Distillation Round 5/8
2025-08-02 05:02:05,575 - src.federated.server - INFO - Starting distillation round 5
2025-08-02 05:02:05,575 - src.utils.memory_manager - INFO - GPU Memory start_distillation_round_5: Allocated: 8.66GB, Reserved: 9.42GB, Free: 5.16GB
2025-08-02 05:02:05,575 - src.federated.server - INFO - Round 5: Generating pseudo data
2025-08-02 05:02:05,575 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Books
2025-08-02 05:02:59,103 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Electronics
2025-08-02 05:03:50,000 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Home_and_Kitchen
2025-08-02 05:04:42,368 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Sports_and_Outdoors
2025-08-02 05:05:34,085 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Toys_and_Games
2025-08-02 05:06:26,593 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Clothing_Shoes_and_Jewelry
2025-08-02 05:07:16,147 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Health_and_Personal_Care
2025-08-02 05:08:08,689 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Automotive
2025-08-02 05:09:00,483 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Tools_and_Home_Improvement
2025-08-02 05:09:52,616 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Beauty
2025-08-02 05:10:43,542 - src.models.text_generator - INFO - Generating 50 mixed domain samples
2025-08-02 05:11:02,558 - src.federated.server - INFO - Generated 1550 pseudo samples
2025-08-02 05:11:03,486 - src.federated.server - INFO - Collecting teacher predictions from clients
2025-08-02 05:16:14,455 - src.federated.server - INFO - Domain Electronics: Teacher predictions shape torch.Size([1550, 5])
2025-08-02 05:16:14,455 - src.federated.server - INFO - Domain Books: Teacher predictions shape torch.Size([1550, 5])
2025-08-02 05:16:14,456 - src.federated.server - INFO - Domain Home_and_Kitchen: Teacher predictions shape torch.Size([1550, 5])
2025-08-02 05:16:14,616 - src.utils.memory_manager - INFO - GPU Memory after_teacher_predictions_round_5: Allocated: 8.66GB, Reserved: 8.95GB, Free: 5.63GB
2025-08-02 05:16:14,617 - src.federated.server - INFO - Collecting domain weights from clients
2025-08-02 05:25:10,000 - src.federated.server - INFO - Processed 1164 valid batches out of 388 total batches per epoch
2025-08-02 05:25:10,000 - src.federated.server - INFO - Round 5 completed: DKD Loss: 0.0011, Contrastive Loss: 1.0374, Total Loss: 0.4161, Processed Batches: 1164
2025-08-02 05:25:10,614 - src.utils.memory_manager - INFO - GPU Memory end_distillation_round_5: Allocated: 9.16GB, Reserved: 9.42GB, Free: 5.16GB
2025-08-02 05:25:10,621 - src.federated.d3afd_framework - INFO -   Distillation Round 6/8
2025-08-02 05:25:10,622 - src.federated.server - INFO - Starting distillation round 6
2025-08-02 05:25:10,622 - src.utils.memory_manager - INFO - GPU Memory start_distillation_round_6: Allocated: 8.66GB, Reserved: 9.42GB, Free: 5.16GB
2025-08-02 05:25:10,622 - src.federated.server - INFO - Round 6: Generating pseudo data
2025-08-02 05:25:10,622 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Books
2025-08-02 05:26:03,094 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Electronics
2025-08-02 05:26:55,634 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Home_and_Kitchen
2025-08-02 05:27:48,165 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Sports_and_Outdoors
2025-08-02 05:28:40,281 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Toys_and_Games
2025-08-02 05:29:33,680 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Clothing_Shoes_and_Jewelry
2025-08-02 05:30:25,502 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Health_and_Personal_Care
2025-08-02 05:31:16,449 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Automotive
2025-08-02 05:32:09,152 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Tools_and_Home_Improvement
2025-08-02 05:33:00,248 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Beauty
2025-08-02 05:33:51,129 - src.models.text_generator - INFO - Generating 50 mixed domain samples
2025-08-02 05:34:11,353 - src.federated.server - INFO - Generated 1550 pseudo samples
2025-08-02 05:34:12,326 - src.federated.server - INFO - Collecting teacher predictions from clients
2025-08-02 05:39:25,815 - src.federated.server - INFO - Domain Electronics: Teacher predictions shape torch.Size([1550, 5])
2025-08-02 05:39:25,815 - src.federated.server - INFO - Domain Books: Teacher predictions shape torch.Size([1550, 5])
2025-08-02 05:39:25,816 - src.federated.server - INFO - Domain Home_and_Kitchen: Teacher predictions shape torch.Size([1550, 5])
2025-08-02 05:39:25,975 - src.utils.memory_manager - INFO - GPU Memory after_teacher_predictions_round_6: Allocated: 8.66GB, Reserved: 9.06GB, Free: 5.51GB
2025-08-02 05:39:25,976 - src.federated.server - INFO - Collecting domain weights from clients
2025-08-02 05:48:22,133 - src.federated.server - INFO - Processed 1164 valid batches out of 388 total batches per epoch
2025-08-02 05:48:22,133 - src.federated.server - INFO - Round 6 completed: DKD Loss: 0.0010, Contrastive Loss: 1.0483, Total Loss: 0.4203, Processed Batches: 1164
2025-08-02 05:48:22,774 - src.utils.memory_manager - INFO - GPU Memory end_distillation_round_6: Allocated: 9.16GB, Reserved: 9.36GB, Free: 5.22GB
2025-08-02 05:48:22,782 - src.federated.d3afd_framework - INFO -   Distillation Round 7/8
2025-08-02 05:48:22,782 - src.federated.server - INFO - Starting distillation round 7
2025-08-02 05:48:22,783 - src.utils.memory_manager - INFO - GPU Memory start_distillation_round_7: Allocated: 8.66GB, Reserved: 9.36GB, Free: 5.22GB
2025-08-02 05:48:22,783 - src.federated.server - INFO - Round 7: Generating pseudo data
2025-08-02 05:48:22,783 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Books
2025-08-02 05:49:15,100 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Electronics
2025-08-02 05:50:06,967 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Home_and_Kitchen
2025-08-02 05:50:59,154 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Sports_and_Outdoors
2025-08-02 05:51:51,193 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Toys_and_Games
2025-08-02 05:52:43,993 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Clothing_Shoes_and_Jewelry
2025-08-02 05:53:33,924 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Health_and_Personal_Care
2025-08-02 05:54:26,328 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Automotive
2025-08-02 05:55:17,682 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Tools_and_Home_Improvement
2025-08-02 05:56:09,285 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Beauty
2025-08-02 05:57:00,311 - src.models.text_generator - INFO - Generating 50 mixed domain samples
2025-08-02 05:57:19,080 - src.federated.server - INFO - Generated 1550 pseudo samples
2025-08-02 05:57:19,992 - src.federated.server - INFO - Collecting teacher predictions from clients
2025-08-02 06:02:31,486 - src.federated.server - INFO - Domain Electronics: Teacher predictions shape torch.Size([1550, 5])
2025-08-02 06:02:31,487 - src.federated.server - INFO - Domain Books: Teacher predictions shape torch.Size([1550, 5])
2025-08-02 06:02:31,487 - src.federated.server - INFO - Domain Home_and_Kitchen: Teacher predictions shape torch.Size([1550, 5])
2025-08-02 06:02:31,658 - src.utils.memory_manager - INFO - GPU Memory after_teacher_predictions_round_7: Allocated: 8.66GB, Reserved: 8.99GB, Free: 5.59GB
2025-08-02 06:02:31,659 - src.federated.server - INFO - Collecting domain weights from clients
2025-08-02 06:11:28,006 - src.federated.server - INFO - Processed 1164 valid batches out of 388 total batches per epoch
2025-08-02 06:11:28,006 - src.federated.server - INFO - Round 7 completed: DKD Loss: 0.0011, Contrastive Loss: 1.0345, Total Loss: 0.4149, Processed Batches: 1164
2025-08-02 06:11:28,598 - src.utils.memory_manager - INFO - GPU Memory end_distillation_round_7: Allocated: 9.16GB, Reserved: 9.42GB, Free: 5.16GB
2025-08-02 06:11:28,606 - src.federated.d3afd_framework - INFO -   Distillation Round 8/8
2025-08-02 06:11:28,606 - src.federated.server - INFO - Starting distillation round 8
2025-08-02 06:11:28,607 - src.utils.memory_manager - INFO - GPU Memory start_distillation_round_8: Allocated: 8.66GB, Reserved: 9.42GB, Free: 5.16GB
2025-08-02 06:11:28,607 - src.federated.server - INFO - Round 8: Generating pseudo data
2025-08-02 06:11:28,607 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Books
2025-08-02 06:12:22,303 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Electronics
2025-08-02 06:13:14,081 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Home_and_Kitchen
2025-08-02 06:14:06,958 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Sports_and_Outdoors
2025-08-02 06:14:58,717 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Toys_and_Games
2025-08-02 06:15:51,293 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Clothing_Shoes_and_Jewelry
2025-08-02 06:16:41,317 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Health_and_Personal_Care
2025-08-02 06:17:31,766 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Automotive
2025-08-02 06:18:22,966 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Tools_and_Home_Improvement
2025-08-02 06:19:14,574 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Beauty
2025-08-02 06:20:06,510 - src.models.text_generator - INFO - Generating 50 mixed domain samples
2025-08-02 06:20:26,321 - src.federated.server - INFO - Generated 1550 pseudo samples
2025-08-02 06:20:27,245 - src.federated.server - INFO - Collecting teacher predictions from clients
2025-08-02 06:25:38,416 - src.federated.server - INFO - Domain Electronics: Teacher predictions shape torch.Size([1550, 5])
2025-08-02 06:25:38,417 - src.federated.server - INFO - Domain Books: Teacher predictions shape torch.Size([1550, 5])
2025-08-02 06:25:38,417 - src.federated.server - INFO - Domain Home_and_Kitchen: Teacher predictions shape torch.Size([1550, 5])
2025-08-02 06:25:38,578 - src.utils.memory_manager - INFO - GPU Memory after_teacher_predictions_round_8: Allocated: 8.66GB, Reserved: 9.08GB, Free: 5.49GB
2025-08-02 06:25:38,578 - src.federated.server - INFO - Collecting domain weights from clients
2025-08-02 06:34:35,557 - src.federated.server - INFO - Processed 1164 valid batches out of 388 total batches per epoch
2025-08-02 06:34:35,557 - src.federated.server - INFO - Round 8 completed: DKD Loss: 0.0011, Contrastive Loss: 1.0371, Total Loss: 0.4159, Processed Batches: 1164
2025-08-02 06:34:36,190 - src.utils.memory_manager - INFO - GPU Memory end_distillation_round_8: Allocated: 9.16GB, Reserved: 9.38GB, Free: 5.20GB
2025-08-02 06:34:36,197 - src.federated.d3afd_framework - INFO - Stage 4: Personalized Fine-tuning
2025-08-02 06:34:36,197 - src.federated.server - INFO - Distributing global model to clients
2025-08-02 06:34:36,207 - src.federated.client - INFO - Client 0: Creating personalized model
2025-08-02 06:34:36,209 - src.federated.server - INFO - Distributed global model to client 0
2025-08-02 06:34:36,217 - src.federated.client - INFO - Client 1: Creating personalized model
2025-08-02 06:34:36,218 - src.federated.server - INFO - Distributed global model to client 1
2025-08-02 06:34:36,229 - src.federated.client - INFO - Client 2: Creating personalized model
2025-08-02 06:34:36,230 - src.federated.server - INFO - Distributed global model to client 2
2025-08-02 06:34:36,242 - src.federated.client - INFO - Client 3: Creating personalized model
2025-08-02 06:34:36,243 - src.federated.server - INFO - Distributed global model to client 3
2025-08-02 06:34:36,254 - src.federated.client - INFO - Client 4: Creating personalized model
2025-08-02 06:34:36,256 - src.federated.server - INFO - Distributed global model to client 4
2025-08-02 06:34:36,266 - src.federated.client - INFO - Client 5: Creating personalized model
2025-08-02 06:34:36,268 - src.federated.server - INFO - Distributed global model to client 5
2025-08-02 06:34:36,279 - src.federated.client - INFO - Client 6: Creating personalized model
2025-08-02 06:34:36,281 - src.federated.server - INFO - Distributed global model to client 6
2025-08-02 06:34:36,292 - src.federated.client - INFO - Client 7: Creating personalized model
2025-08-02 06:34:36,294 - src.federated.server - INFO - Distributed global model to client 7
2025-08-02 06:34:36,294 - src.federated.client - INFO - Client 0: Fine-tuning personalized model for 2 epochs
2025-08-02 06:34:41,710 - src.federated.client - INFO - Client 0 - Personalization Epoch 1/2: Loss: 1.6271, Accuracy: 0.2000
2025-08-02 06:34:47,119 - src.federated.client - INFO - Client 0 - Personalization Epoch 2/2: Loss: 1.6198, Accuracy: 0.1743
2025-08-02 06:34:47,119 - src.federated.client - INFO - Client 0: Personalized model fine-tuning completed. Final accuracy: 0.1871
2025-08-02 06:34:47,120 - src.federated.d3afd_framework - INFO - Client 0 personalization: Accuracy 0.1871
2025-08-02 06:34:47,120 - src.federated.client - INFO - Client 1: Fine-tuning personalized model for 2 epochs
2025-08-02 06:34:52,547 - src.federated.client - INFO - Client 1 - Personalization Epoch 1/2: Loss: 1.6287, Accuracy: 0.1914
2025-08-02 06:34:57,976 - src.federated.client - INFO - Client 1 - Personalization Epoch 2/2: Loss: 1.6229, Accuracy: 0.1743
2025-08-02 06:34:57,976 - src.federated.client - INFO - Client 1: Personalized model fine-tuning completed. Final accuracy: 0.1829
2025-08-02 06:34:57,976 - src.federated.d3afd_framework - INFO - Client 1 personalization: Accuracy 0.1829
2025-08-02 06:34:57,976 - src.federated.client - INFO - Client 2: Fine-tuning personalized model for 2 epochs
2025-08-02 06:35:03,399 - src.federated.client - INFO - Client 2 - Personalization Epoch 1/2: Loss: 1.6263, Accuracy: 0.1971
2025-08-02 06:35:08,839 - src.federated.client - INFO - Client 2 - Personalization Epoch 2/2: Loss: 1.6179, Accuracy: 0.2171
2025-08-02 06:35:08,839 - src.federated.client - INFO - Client 2: Personalized model fine-tuning completed. Final accuracy: 0.2071
2025-08-02 06:35:08,839 - src.federated.d3afd_framework - INFO - Client 2 personalization: Accuracy 0.2071
2025-08-02 06:35:08,840 - src.federated.client - INFO - Client 3: Fine-tuning personalized model for 2 epochs
2025-08-02 06:35:14,294 - src.federated.client - INFO - Client 3 - Personalization Epoch 1/2: Loss: 1.6348, Accuracy: 0.1543
2025-08-02 06:35:19,742 - src.federated.client - INFO - Client 3 - Personalization Epoch 2/2: Loss: 1.6258, Accuracy: 0.1657
2025-08-02 06:35:19,743 - src.federated.client - INFO - Client 3: Personalized model fine-tuning completed. Final accuracy: 0.1600
2025-08-02 06:35:19,743 - src.federated.d3afd_framework - INFO - Client 3 personalization: Accuracy 0.1600
2025-08-02 06:35:19,743 - src.federated.client - INFO - Client 4: Fine-tuning personalized model for 2 epochs
2025-08-02 06:35:25,212 - src.federated.client - INFO - Client 4 - Personalization Epoch 1/2: Loss: 1.6230, Accuracy: 0.1857
2025-08-02 06:35:30,675 - src.federated.client - INFO - Client 4 - Personalization Epoch 2/2: Loss: 1.6226, Accuracy: 0.1600
2025-08-02 06:35:30,675 - src.federated.client - INFO - Client 4: Personalized model fine-tuning completed. Final accuracy: 0.1729
2025-08-02 06:35:30,675 - src.federated.d3afd_framework - INFO - Client 4 personalization: Accuracy 0.1729
2025-08-02 06:35:30,675 - src.federated.client - INFO - Client 5: Fine-tuning personalized model for 2 epochs
2025-08-02 06:35:36,170 - src.federated.client - INFO - Client 5 - Personalization Epoch 1/2: Loss: 1.6193, Accuracy: 0.1971
2025-08-02 06:35:41,623 - src.federated.client - INFO - Client 5 - Personalization Epoch 2/2: Loss: 1.6141, Accuracy: 0.1914
2025-08-02 06:35:41,623 - src.federated.client - INFO - Client 5: Personalized model fine-tuning completed. Final accuracy: 0.1943
2025-08-02 06:35:41,623 - src.federated.d3afd_framework - INFO - Client 5 personalization: Accuracy 0.1943
2025-08-02 06:35:41,624 - src.federated.client - INFO - Client 6: Fine-tuning personalized model for 2 epochs
2025-08-02 06:35:47,125 - src.federated.client - INFO - Client 6 - Personalization Epoch 1/2: Loss: 1.6194, Accuracy: 0.2229
2025-08-02 06:35:52,611 - src.federated.client - INFO - Client 6 - Personalization Epoch 2/2: Loss: 1.6167, Accuracy: 0.2314
2025-08-02 06:35:52,611 - src.federated.client - INFO - Client 6: Personalized model fine-tuning completed. Final accuracy: 0.2271
2025-08-02 06:35:52,612 - src.federated.d3afd_framework - INFO - Client 6 personalization: Accuracy 0.2271
2025-08-02 06:35:52,612 - src.federated.client - INFO - Client 7: Fine-tuning personalized model for 2 epochs
2025-08-02 06:35:58,108 - src.federated.client - INFO - Client 7 - Personalization Epoch 1/2: Loss: 1.6208, Accuracy: 0.2114
2025-08-02 06:36:03,595 - src.federated.client - INFO - Client 7 - Personalization Epoch 2/2: Loss: 1.6159, Accuracy: 0.2114
2025-08-02 06:36:03,595 - src.federated.client - INFO - Client 7: Personalized model fine-tuning completed. Final accuracy: 0.2114
2025-08-02 06:36:03,596 - src.federated.d3afd_framework - INFO - Client 7 personalization: Accuracy 0.2114
2025-08-02 06:36:03,596 - src.federated.d3afd_framework - INFO - 📊 Evaluating Global Model after Federated Round 1
2025-08-02 06:36:05,209 - src.federated.server - INFO - Evaluating global model
2025-08-02 06:36:09,285 - src.federated.server - INFO - Global model evaluation: Accuracy: 0.1917, F1-Macro: 0.0643, F1-Weighted: 0.0617
2025-08-02 06:36:09,286 - src.federated.d3afd_framework - INFO - 🎯 Federated_Round_1 Global Model Evaluation:
2025-08-02 06:36:09,286 - src.federated.d3afd_framework - INFO -    📊 Accuracy: 0.1917 (19.17%)
2025-08-02 06:36:09,286 - src.federated.d3afd_framework - INFO -    📈 F1-Macro: 0.0643
2025-08-02 06:36:09,286 - src.federated.d3afd_framework - INFO -    📉 Loss: 1.6377
2025-08-02 06:36:09,286 - src.federated.d3afd_framework - INFO -    📋 Test Samples: 600
2025-08-02 06:36:09,291 - src.federated.d3afd_framework - INFO - ✅ Federated Round 1/12 completed
2025-08-02 06:36:09,292 - src.federated.d3afd_framework - INFO - ============================================================
2025-08-02 06:36:09,292 - src.federated.d3afd_framework - INFO - 
============================================================
2025-08-02 06:36:09,292 - src.federated.d3afd_framework - INFO - FEDERATED ROUND 2/12
2025-08-02 06:36:09,292 - src.federated.d3afd_framework - INFO - ============================================================
2025-08-02 06:36:09,292 - src.federated.d3afd_framework - INFO - Stage 1: Local Model Re-training
2025-08-02 06:36:09,292 - src.federated.client - INFO - Client 0: Training local teacher for 2 epochs
2025-08-02 06:36:21,093 - src.federated.client - INFO - Client 0 - Epoch 1/2: Loss: 0.3931, Accuracy: 1.0000
2025-08-02 06:36:33,086 - src.federated.client - INFO - Client 0 - Epoch 2/2: Loss: 0.3916, Accuracy: 1.0000
2025-08-02 06:36:33,087 - src.federated.client - WARNING - Client 0: Early stopping due to potential overfitting (accuracy: 1.0000)
2025-08-02 06:36:33,087 - src.federated.client - INFO - Client 0: Local teacher training completed. Final accuracy: 1.0000
2025-08-02 06:36:33,088 - src.federated.d3afd_framework - INFO - Client 0 re-training: Accuracy 1.0000
2025-08-02 06:36:33,088 - src.federated.client - INFO - Client 1: Training local teacher for 2 epochs
2025-08-02 06:36:45,192 - src.federated.client - INFO - Client 1 - Epoch 1/2: Loss: 0.3934, Accuracy: 1.0000
2025-08-02 06:36:57,453 - src.federated.client - INFO - Client 1 - Epoch 2/2: Loss: 0.3914, Accuracy: 1.0000
2025-08-02 06:36:57,453 - src.federated.client - WARNING - Client 1: Early stopping due to potential overfitting (accuracy: 1.0000)
2025-08-02 06:36:57,453 - src.federated.client - INFO - Client 1: Local teacher training completed. Final accuracy: 1.0000
2025-08-02 06:36:57,454 - src.federated.d3afd_framework - INFO - Client 1 re-training: Accuracy 1.0000
2025-08-02 06:36:57,455 - src.federated.client - INFO - Client 2: Training local teacher for 2 epochs
2025-08-02 06:37:09,626 - src.federated.client - INFO - Client 2 - Epoch 1/2: Loss: 0.3936, Accuracy: 1.0000
2025-08-02 06:37:20,760 - src.federated.client - INFO - Client 2 - Epoch 2/2: Loss: 0.3917, Accuracy: 1.0000
2025-08-02 06:37:20,761 - src.federated.client - WARNING - Client 2: Early stopping due to potential overfitting (accuracy: 1.0000)
2025-08-02 06:37:20,761 - src.federated.client - INFO - Client 2: Local teacher training completed. Final accuracy: 1.0000
2025-08-02 06:37:20,762 - src.federated.d3afd_framework - INFO - Client 2 re-training: Accuracy 1.0000
2025-08-02 06:37:20,762 - src.federated.client - INFO - Client 3: Training local teacher for 2 epochs
2025-08-02 06:37:32,738 - src.federated.client - INFO - Client 3 - Epoch 1/2: Loss: 0.3942, Accuracy: 1.0000
2025-08-02 06:37:45,022 - src.federated.client - INFO - Client 3 - Epoch 2/2: Loss: 0.3920, Accuracy: 1.0000
2025-08-02 06:37:45,023 - src.federated.client - WARNING - Client 3: Early stopping due to potential overfitting (accuracy: 1.0000)
2025-08-02 06:37:45,023 - src.federated.client - INFO - Client 3: Local teacher training completed. Final accuracy: 1.0000
2025-08-02 06:37:45,024 - src.federated.d3afd_framework - INFO - Client 3 re-training: Accuracy 1.0000
2025-08-02 06:37:45,024 - src.federated.client - INFO - Client 4: Training local teacher for 2 epochs
2025-08-02 06:37:56,890 - src.federated.client - INFO - Client 4 - Epoch 1/2: Loss: 0.3952, Accuracy: 1.0000
2025-08-02 06:38:08,954 - src.federated.client - INFO - Client 4 - Epoch 2/2: Loss: 0.3918, Accuracy: 1.0000
2025-08-02 06:38:08,954 - src.federated.client - WARNING - Client 4: Early stopping due to potential overfitting (accuracy: 1.0000)
2025-08-02 06:38:08,954 - src.federated.client - INFO - Client 4: Local teacher training completed. Final accuracy: 1.0000
2025-08-02 06:38:08,956 - src.federated.d3afd_framework - INFO - Client 4 re-training: Accuracy 1.0000
2025-08-02 06:38:08,956 - src.federated.client - INFO - Client 5: Training local teacher for 2 epochs
2025-08-02 06:38:20,001 - src.federated.client - INFO - Client 5 - Epoch 1/2: Loss: 0.3933, Accuracy: 1.0000
2025-08-02 06:38:31,242 - src.federated.client - INFO - Client 5 - Epoch 2/2: Loss: 0.3920, Accuracy: 1.0000
2025-08-02 06:38:31,242 - src.federated.client - WARNING - Client 5: Early stopping due to potential overfitting (accuracy: 1.0000)
2025-08-02 06:38:31,242 - src.federated.client - INFO - Client 5: Local teacher training completed. Final accuracy: 1.0000
2025-08-02 06:38:31,243 - src.federated.d3afd_framework - INFO - Client 5 re-training: Accuracy 1.0000
2025-08-02 06:38:31,244 - src.federated.client - INFO - Client 6: Training local teacher for 2 epochs
2025-08-02 06:38:43,237 - src.federated.client - INFO - Client 6 - Epoch 1/2: Loss: 0.3939, Accuracy: 1.0000
2025-08-02 06:38:54,516 - src.federated.client - INFO - Client 6 - Epoch 2/2: Loss: 0.3920, Accuracy: 1.0000
2025-08-02 06:38:54,517 - src.federated.client - WARNING - Client 6: Early stopping due to potential overfitting (accuracy: 1.0000)
2025-08-02 06:38:54,517 - src.federated.client - INFO - Client 6: Local teacher training completed. Final accuracy: 1.0000
2025-08-02 06:38:54,518 - src.federated.d3afd_framework - INFO - Client 6 re-training: Accuracy 1.0000
2025-08-02 06:38:54,518 - src.federated.client - INFO - Client 7: Training local teacher for 2 epochs
2025-08-02 06:39:06,701 - src.federated.client - INFO - Client 7 - Epoch 1/2: Loss: 0.3934, Accuracy: 1.0000
2025-08-02 06:39:19,040 - src.federated.client - INFO - Client 7 - Epoch 2/2: Loss: 0.3918, Accuracy: 1.0000
2025-08-02 06:39:19,041 - src.federated.client - WARNING - Client 7: Early stopping due to potential overfitting (accuracy: 1.0000)
2025-08-02 06:39:19,041 - src.federated.client - INFO - Client 7: Local teacher training completed. Final accuracy: 1.0000
2025-08-02 06:39:19,042 - src.federated.d3afd_framework - INFO - Client 7 re-training: Accuracy 1.0000
2025-08-02 06:39:19,042 - src.federated.d3afd_framework - INFO - Stage 2-3: Pseudo Data Generation & DKD Distillation
2025-08-02 06:39:19,042 - src.federated.d3afd_framework - INFO -   Distillation Round 1/8
2025-08-02 06:39:19,042 - src.federated.server - INFO - Starting distillation round 9
2025-08-02 06:39:19,043 - src.utils.memory_manager - INFO - GPU Memory start_distillation_round_9: Allocated: 10.64GB, Reserved: 11.51GB, Free: 3.07GB
2025-08-02 06:39:19,043 - src.federated.server - INFO - Round 9: Generating pseudo data
2025-08-02 06:39:19,043 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Books
2025-08-02 06:40:11,817 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Electronics
2025-08-02 06:41:04,013 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Home_and_Kitchen
2025-08-02 06:41:55,214 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Sports_and_Outdoors
2025-08-02 06:42:47,539 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Toys_and_Games
2025-08-02 06:43:40,537 - src.models.text_generator - INFO - Generating 150 pseudo samples for domain: Clothing_Shoes_and_Jewelry
