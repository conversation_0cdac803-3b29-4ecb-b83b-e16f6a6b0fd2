"""
Federated Learning Server for D³AFD with Multi-Algorithm Support
"""
import torch
import torch.nn as nn
from torch.utils.data import DataLoader, Dataset
from typing import Dict, List, Optional, Tuple, Any
import copy
import logging
import os
from enum import Enum

from ..models.base_models import StudentModel
from ..models.text_generator import T5TextGenerator
from ..models.dkd_distillation import DKDDistillationTrainer
from ..models.contrastive_learning import CrossDomainContrastiveTrainer
from ..models.dynamic_domain_descriptor import DynamicDomainDescriptor
from ..data.amazon_dataset import AmazonReviewDataset
from ..utils.memory_manager import get_memory_manager, memory_cleanup_decorator, memory_cleanup_decorator, log_memory_usage

# Import distillation algorithms
from ..algorithms.dkdm_distillation import DKDMDistillationTrainer
from ..algorithms.diffdfkd_distillation import DiffDFKDDistillationTrainer
from ..algorithms.diffkd_distillation import DiffKDDistillationTrainer

logger = logging.getLogger(__name__)

class DistillationAlgorithm(Enum):
    """Supported distillation algorithms"""
    DKD = "dkd"           # Original DKD method
    DKDM = "dkdm"         # DKDM dynamic iterative distillation
    DIFFDFKD = "diffdfkd" # DiffDFKD with T5 text generation
    DIFFKD = "diffkd"     # DiffKD feature-level distillation

class PseudoDataset(Dataset):
    """Dataset for pseudo samples"""
    
    def __init__(self, pseudo_samples: List[Dict], tokenizer, max_length: int = 512):
        self.pseudo_samples = pseudo_samples
        self.tokenizer = tokenizer
        self.max_length = max_length
    
    def __len__(self):
        return len(self.pseudo_samples)
    
    def __getitem__(self, idx):
        sample = self.pseudo_samples[idx]
        text = sample['text']
        label = sample['label']
        domain = sample['domain']
        
        # Tokenize
        encoding = self.tokenizer(
            text,
            truncation=True,
            padding='max_length',
            max_length=self.max_length,
            return_tensors='pt'
        )
        
        return {
            'input_ids': encoding['input_ids'].flatten(),
            'attention_mask': encoding['attention_mask'].flatten(),
            'labels': torch.tensor(label, dtype=torch.long),
            'domain': domain,
            'text': text
        }

class FederatedServer:
    """Federated learning server for D³AFD"""
    
    def __init__(self, config):
        self.config = config
        self.device = torch.device(config.experiment.device)

        # Initialize global student model
        self.global_model = StudentModel(config).to(self.device)

        # Initialize text generator (T5-based)
        self.text_generator = T5TextGenerator(config)

        # Initialize distillation algorithm based on config
        self.distillation_algorithm = DistillationAlgorithm(config.training.distillation_algorithm)
        self.distillation_trainer = self._initialize_distillation_trainer()

        # Initialize contrastive learning trainer
        self.contrastive_trainer = CrossDomainContrastiveTrainer(config)

        # Training history
        self.training_history = []

        # Client information
        self.clients = {}
        self.client_domains = {}

        # Cache for pseudo data to avoid regeneration
        self.pseudo_data_cache = {}
        self.cache_enabled = True
        self.cache_refresh_interval = 3  # Refresh cache every N rounds

        logger.info(f"Initialized D³AFD server with {self.distillation_algorithm.value} distillation algorithm")

        # 动态域描述符管理
        self.domain_descriptor_generators = {}  # 每个客户端的域描述符生成器
        self.current_domain_descriptors = {}  # 当前轮次的域描述符

        logger.info("Federated server initialized")

    def _initialize_distillation_trainer(self):
        """Initialize the appropriate distillation trainer based on algorithm choice"""
        if self.distillation_algorithm == DistillationAlgorithm.DKD:
            return DKDDistillationTrainer(self.config)
        elif self.distillation_algorithm == DistillationAlgorithm.DKDM:
            return DKDMDistillationTrainer(self.config)
        elif self.distillation_algorithm == DistillationAlgorithm.DIFFDFKD:
            return DiffDFKDDistillationTrainer(self.config)
        elif self.distillation_algorithm == DistillationAlgorithm.DIFFKD:
            return DiffKDDistillationTrainer(self.config)
        else:
            raise ValueError(f"Unsupported distillation algorithm: {self.distillation_algorithm}")

    def get_distillation_algorithm_info(self) -> Dict[str, Any]:
        """Get information about the current distillation algorithm"""
        return {
            "algorithm": self.distillation_algorithm.value,
            "supports_data_free": self.distillation_algorithm in [DistillationAlgorithm.DKDM, DistillationAlgorithm.DIFFDFKD],
            "supports_feature_distillation": self.distillation_algorithm in [DistillationAlgorithm.DIFFKD, DistillationAlgorithm.DKD],
            "requires_text_generation": self.distillation_algorithm == DistillationAlgorithm.DIFFDFKD,
            "supports_dynamic_iteration": self.distillation_algorithm == DistillationAlgorithm.DKDM
        }
    
    def register_client(self, client_id: int, client_domains: List[str]):
        """Register a client with the server"""
        self.client_domains[client_id] = client_domains
        logger.info(f"Registered client {client_id} with domains: {client_domains}")
    
    def generate_pseudo_data(self, round_num: int) -> List[Dict]:
        """Generate pseudo data for current round with caching"""
        cache_key = f"round_{round_num}"

        # Check if we can use cached data
        if (self.cache_enabled and
            cache_key in self.pseudo_data_cache and
            round_num % self.cache_refresh_interval != 0):
            logger.info(f"Using cached pseudo data for round {round_num} (cache hit)")
            return self.pseudo_data_cache[cache_key]

        logger.info(f"Round {round_num}: Generating new pseudo data")

        all_pseudo_samples = []

        # Generate domain-specific samples
        for domain in self.config.data.domains:
            domain_samples = self.text_generator.generate_pseudo_samples(
                domain=domain,
                num_samples=self.config.training.pseudo_samples_per_domain
            )
            all_pseudo_samples.extend(domain_samples)

        # Generate mixed domain samples
        mixed_samples = self.text_generator.generate_mixed_domain_samples(
            domains=self.config.data.domains,
            num_samples=self.config.training.mixed_domain_samples
        )
        all_pseudo_samples.extend(mixed_samples)

        # Cache the generated data
        if self.cache_enabled:
            self.pseudo_data_cache[cache_key] = all_pseudo_samples
            # Keep only recent cache entries to save memory
            if len(self.pseudo_data_cache) > 5:
                oldest_key = min(self.pseudo_data_cache.keys())
                del self.pseudo_data_cache[oldest_key]
        
        logger.info(f"Generated {len(all_pseudo_samples)} pseudo samples")
        return all_pseudo_samples
    
    def create_pseudo_dataloader(self, pseudo_samples: List[Dict]) -> DataLoader:
        """Create DataLoader for pseudo samples"""
        from transformers import AutoTokenizer
        tokenizer = AutoTokenizer.from_pretrained(self.config.model.model_name)
        
        dataset = PseudoDataset(
            pseudo_samples=pseudo_samples,
            tokenizer=tokenizer,
            max_length=self.config.data.max_length
        )
        
        return DataLoader(
            dataset,
            batch_size=self.config.training.distillation_batch_size,
            shuffle=True,
            num_workers=2
        )
    
    def collect_teacher_predictions(self, clients: Dict, pseudo_samples: List[Dict]) -> Dict[str, torch.Tensor]:
        """Collect teacher predictions from all clients"""
        logger.info("Collecting teacher predictions from clients")
        
        teacher_predictions = {}
        
        for client_id, client in clients.items():
            client_domains = self.client_domains.get(client_id, [])
            
            if client_domains:
                # Get predictions from this client's teacher
                predictions = client.get_teacher_predictions(pseudo_samples)
                
                # Store predictions for each domain this client represents
                for domain in client_domains:
                    if domain not in teacher_predictions:
                        teacher_predictions[domain] = []
                    teacher_predictions[domain].append(predictions)
        
        # Average predictions for each domain if multiple clients represent the same domain
        averaged_predictions = {}
        for domain, pred_list in teacher_predictions.items():
            if len(pred_list) == 1:
                averaged_predictions[domain] = pred_list[0]
            else:
                # Average multiple predictions
                averaged_predictions[domain] = torch.stack(pred_list).mean(dim=0)

            # Log prediction shapes for debugging
            logger.info(f"Domain {domain}: Teacher predictions shape {averaged_predictions[domain].shape}")

        return averaged_predictions
    
    def collect_domain_weights(self, clients: Dict, pseudo_samples: List[Dict]) -> Dict[str, torch.Tensor]:
        """Collect domain weights from all clients"""
        logger.info("Collecting domain weights from clients")
        
        domain_weights = {}
        texts = [sample['text'] for sample in pseudo_samples]
        
        for client_id, client in clients.items():
            client_domains = self.client_domains.get(client_id, [])
            
            if client_domains:
                # Get domain weights from this client
                weights = client.get_domain_weights(texts)
                
                # Store weights for each domain this client represents
                for domain in client_domains:
                    domain_weights[domain] = weights
        
        return domain_weights
    
    @memory_cleanup_decorator
    def distillation_round(self, clients: Dict, round_num: int) -> Dict[str, float]:
        """Perform one round of distillation with memory management"""
        logger.info(f"Starting distillation round {round_num}")

        # Initialize memory manager
        memory_manager = get_memory_manager()
        memory_manager.log_memory_usage(f"start_distillation_round_{round_num}")

        # 🔄 生成动态域描述符 (关键改进!)
        logger.info("🔄 生成动态域描述符...")
        dynamic_descriptors = self.generate_dynamic_domain_descriptors(clients, round_num)

        # Generate pseudo data based on algorithm requirements
        if self.distillation_algorithm == DistillationAlgorithm.DIFFDFKD:
            # DiffDFKD generates its own synthetic data using T5
            pseudo_dataloader = None
            teacher_predictions = None
            domain_weights = None
            logger.info("DiffDFKD will generate synthetic data using T5")
        else:
            # Other algorithms use traditional pseudo data generation
            pseudo_samples = self.generate_pseudo_data(round_num)
            memory_manager.clear_cache()  # Clear after generation
            pseudo_dataloader = self.create_pseudo_dataloader(pseudo_samples)

            # Collect teacher predictions and domain weights
            teacher_predictions = self.collect_teacher_predictions(clients, pseudo_samples)
            memory_manager.clear_cache()  # Clear after collection
            memory_manager.log_memory_usage(f"after_teacher_predictions_round_{round_num}")

            domain_weights = self.collect_domain_weights(clients, pseudo_samples)
            memory_manager.clear_cache()  # Clear after collection
        
        # Train global model using selected distillation algorithm
        logger.info(f"Training global student model with {self.distillation_algorithm.value} distillation...")

        # Create teacher models dictionary for distillation
        teacher_models = {}
        for client_id, client in clients.items():
            if hasattr(client, 'domain') and hasattr(client, 'model'):
                teacher_models[client.domain] = client.model

        # Use algorithm-specific training
        if self.distillation_algorithm in [DistillationAlgorithm.DKDM, DistillationAlgorithm.DIFFDFKD, DistillationAlgorithm.DIFFKD]:
            # Use new algorithm trainers
            distillation_stats = self.distillation_trainer.train_student_model(
                self.global_model,
                teacher_models,
                pseudo_dataloader,
                num_epochs=self.config.training.distillation_epochs
            )

            # Log algorithm-specific metrics
            logger.info(f"Distillation completed with {self.distillation_algorithm.value}: {distillation_stats}")

            return {
                'round': round_num,
                'algorithm': self.distillation_algorithm.value,
                **distillation_stats
            }

        # Fallback to original DKD implementation for backward compatibility
        self.global_model.train()

        # Choose optimizer based on configuration
        optimizer_type = getattr(self.config.training, 'optimizer_type', 'adamw')

        if optimizer_type.lower() == 'sgd':
            optimizer = torch.optim.SGD(
                self.global_model.parameters(),
                lr=self.config.training.distillation_lr,
                momentum=getattr(self.config.training, 'momentum', 0.9),
                weight_decay=getattr(self.config.training, 'weight_decay', 1e-4),
                nesterov=getattr(self.config.training, 'nesterov', True)
            )
        else:
            optimizer = torch.optim.AdamW(
                self.global_model.parameters(),
                lr=self.config.training.distillation_lr,
                weight_decay=getattr(self.config.training, 'weight_decay', 0.01)
            )
        
        total_dkd_loss = 0
        total_contrastive_loss = 0
        total_loss = 0
        num_batches = 0

        for epoch in range(self.config.training.distillation_epochs):
            batch_idx = 0  # Reset batch index for each epoch

            # 强制内存清理在每个epoch开始
            memory_manager.clear_cache(force=True)

            for batch in pseudo_dataloader:
                # Move batch to device
                input_batch = {
                    'input_ids': batch['input_ids'].to(self.device),
                    'attention_mask': batch['attention_mask'].to(self.device)
                }

                # Get student predictions
                student_outputs = self.global_model(
                    input_ids=input_batch['input_ids'],
                    attention_mask=input_batch['attention_mask']
                )

                # Compute ensemble teacher predictions
                batch_teacher_preds = {}
                batch_domain_weights = {}
                current_batch_size = input_batch['input_ids'].size(0)

                for domain in teacher_predictions:
                    start_idx = batch_idx * self.config.training.distillation_batch_size
                    end_idx = start_idx + current_batch_size
                    total_predictions = teacher_predictions[domain].size(0)

                    # Check if we have enough predictions
                    if start_idx < total_predictions:
                        actual_end_idx = min(end_idx, total_predictions)
                        actual_batch_size = actual_end_idx - start_idx

                        if actual_batch_size > 0:
                            batch_teacher_preds[domain] = teacher_predictions[domain][start_idx:actual_end_idx]

                            if domain in domain_weights and start_idx < domain_weights[domain].size(0):
                                actual_end_idx_weights = min(end_idx, domain_weights[domain].size(0))
                                batch_domain_weights[domain] = domain_weights[domain][start_idx:actual_end_idx_weights]
                        else:
                            logger.debug(f"Domain {domain}: No valid predictions for batch {batch_idx}")
                    else:
                        logger.debug(f"Domain {domain}: Batch {batch_idx} exceeds available predictions ({start_idx} >= {total_predictions})")

                # Check if we have any valid predictions for this batch
                if not batch_teacher_preds:
                    logger.debug(f"No teacher predictions available for batch {batch_idx} (epoch {epoch+1}), skipping")
                    batch_idx += 1
                    continue

                # Compute ensemble predictions
                try:
                    ensemble_logits = self.dkd_trainer.ensemble_teacher.compute_ensemble_predictions(
                        batch_teacher_preds, batch_domain_weights
                    )

                    # Check if ensemble predictions are valid
                    if ensemble_logits.size(0) == 0:
                        logger.warning(f"Empty ensemble predictions for batch {batch_idx}, skipping")
                        batch_idx += 1
                        continue

                except Exception as e:
                    logger.warning(f"Failed to compute ensemble predictions for batch {batch_idx}: {e}, skipping")
                    batch_idx += 1
                    continue

                # Compute DKD loss
                dkd_losses = self.dkd_trainer.dkd_loss(student_outputs.logits, ensemble_logits)
                dkd_loss = dkd_losses['dkd_loss']

                # Compute contrastive loss
                pseudo_labels = torch.argmax(ensemble_logits, dim=1)
                domains = batch['domain']

                # 条件性计算对比损失以节省内存
                if self.config.training.contrastive_weight > 0:
                    contrastive_loss = self.contrastive_trainer.compute_contrastive_loss(
                        self.global_model, input_batch, pseudo_labels, domains
                    )
                else:
                    contrastive_loss = torch.tensor(0.0, device=self.device, requires_grad=True)

                # Check for NaN values and skip if found
                if torch.isnan(dkd_loss) or torch.isnan(contrastive_loss):
                    logger.warning(f"NaN detected in losses (DKD: {dkd_loss.item()}, Contrastive: {contrastive_loss.item()}), skipping batch")
                    batch_idx += 1
                    continue

                # Total loss
                total_loss_batch = dkd_loss + self.config.training.contrastive_weight * contrastive_loss

                # Check total loss for NaN
                if torch.isnan(total_loss_batch):
                    logger.warning(f"NaN detected in total loss, skipping batch")
                    continue

                # Backward pass
                optimizer.zero_grad()
                total_loss_batch.backward()

                # Check gradients for NaN
                has_nan_grad = False
                for param in self.global_model.parameters():
                    if param.grad is not None and torch.isnan(param.grad).any():
                        has_nan_grad = True
                        break

                if has_nan_grad:
                    logger.warning("NaN detected in gradients, skipping optimizer step")
                    continue

                optimizer.step()

                # Accumulate losses
                total_dkd_loss += dkd_loss.item()
                total_contrastive_loss += contrastive_loss.item()
                total_loss += total_loss_batch.item()
                num_batches += 1
                batch_idx += 1
        
        # Calculate average losses (avoid division by zero)
        if num_batches > 0:
            avg_dkd_loss = total_dkd_loss / num_batches
            avg_contrastive_loss = total_contrastive_loss / num_batches
            avg_total_loss = total_loss / num_batches
            logger.info(f"Processed {num_batches} valid batches out of {len(pseudo_dataloader)} total batches per epoch")
        else:
            logger.warning("No valid batches processed, setting losses to NaN")
            avg_dkd_loss = float('nan')
            avg_contrastive_loss = float('nan')
            avg_total_loss = float('nan')
        
        round_stats = {
            'round': round_num,
            'dkd_loss': avg_dkd_loss,
            'contrastive_loss': avg_contrastive_loss,
            'total_loss': avg_total_loss,
            'num_pseudo_samples': len(pseudo_samples),
            'processed_batches': num_batches
        }

        self.training_history.append(round_stats)

        logger.info(f"Round {round_num} completed: "
                   f"DKD Loss: {avg_dkd_loss:.4f}, "
                   f"Contrastive Loss: {avg_contrastive_loss:.4f}, "
                   f"Total Loss: {avg_total_loss:.4f}, "
                   f"Processed Batches: {num_batches}")

        # Final memory cleanup for this round
        memory_manager.clear_cache(force=True)
        memory_manager.log_memory_usage(f"end_distillation_round_{round_num}")

        return round_stats

    def generate_dynamic_domain_descriptors(self, clients: Dict, round_num: int) -> Dict[int, Dict]:
        """为每个客户端生成动态域描述符 - 优化内存管理"""
        logger.info(f"第 {round_num} 轮: 生成动态域描述符")

        # 获取内存管理器
        memory_manager = get_memory_manager()
        memory_manager.log_memory_usage(f"start_dynamic_descriptors_round_{round_num}")

        dynamic_descriptors = {}

        # 逐个处理客户端以节省内存
        for client_id, client in clients.items():
            try:
                logger.info(f"处理客户端 {client_id} 的域描述符...")
                memory_manager.clear_cache()  # 清理缓存

                # 为客户端创建域描述符生成器 (如果不存在)
                if client_id not in self.domain_descriptor_generators:
                    # 检查客户端是否有teacher_model，如果没有则跳过或使用默认
                    if hasattr(client, 'teacher_model') and client.teacher_model is not None:
                        self.domain_descriptor_generators[client_id] = DynamicDomainDescriptor(
                            self.config, client.teacher_model
                        )
                    else:
                        logger.warning(f"客户端 {client_id} 没有teacher_model，跳过域描述符生成")
                        dynamic_descriptors[client_id] = self._create_default_descriptor(client_id, round_num)
                        continue

                descriptor_generator = self.domain_descriptor_generators[client_id]

                # 生成当前轮次的域描述符
                domain_descriptor = descriptor_generator.generate_domain_descriptor(
                    client_id=client_id,
                    dataloader=client.train_dataloader,
                    round_num=round_num
                )

                # 分析域演化
                evolution_analysis = descriptor_generator.analyze_domain_evolution(
                    client_id, round_num
                )

                # 合并描述符和演化分析
                domain_descriptor['evolution'] = evolution_analysis
                dynamic_descriptors[client_id] = domain_descriptor

                logger.info(f"客户端 {client_id} 域描述符生成完成")
                logger.info(f"  演化趋势: {evolution_analysis['convergence_trend']}")
                logger.info(f"  稳定性: {evolution_analysis['stability']:.4f}")

                # 强制清理内存
                memory_manager.clear_cache(force=True)

            except torch.cuda.OutOfMemoryError as e:
                logger.error(f"客户端 {client_id} 域描述符生成时GPU内存不足: {e}")
                memory_manager.clear_cache(force=True)
                # 使用默认描述符
                dynamic_descriptors[client_id] = self._create_default_descriptor(client_id, round_num)

            except Exception as e:
                logger.error(f"客户端 {client_id} 域描述符生成失败: {e}")
                # 使用默认描述符
                dynamic_descriptors[client_id] = self._create_default_descriptor(client_id, round_num)

        # 更新当前域描述符
        self.current_domain_descriptors = dynamic_descriptors

        # 更新T5生成器的域描述符
        self.text_generator.update_domain_descriptors(dynamic_descriptors)

        # 最终内存清理
        memory_manager.clear_cache(force=True)
        memory_manager.log_memory_usage(f"end_dynamic_descriptors_round_{round_num}")

        logger.info(f"动态域描述符生成完成，客户端数量: {len(dynamic_descriptors)}")
        return dynamic_descriptors

    def _create_default_descriptor(self, client_id: int, round_num: int) -> Dict:
        """创建默认域描述符"""
        return {
            'center': torch.randn(128),
            'variance': torch.ones(128) * 0.5,
            'range': torch.ones(128),
            'density': torch.tensor(1.0),
            'round': round_num,
            'client_id': client_id,
            'sample_count': 100,
            'evolution': {
                'stability': 0.5,
                'learning_progress': 0.5,
                'convergence_trend': 'unknown'
            }
        }

    def federated_averaging(self, clients: Dict) -> None:
        """Perform federated averaging to update global model"""
        logger.info("Performing federated averaging...")

        # Collect client model parameters and sample counts
        client_params = []
        client_weights = []

        total_samples = 0
        for client_id, client in clients.items():
            # Get client model parameters
            client_state = client.get_teacher_model_state_dict()
            client_sample_count = len(client.train_data)

            client_params.append(client_state)
            client_weights.append(client_sample_count)
            total_samples += client_sample_count

        if not client_params:
            logger.warning("No client parameters to aggregate")
            return

        # Normalize weights
        client_weights = [w / total_samples for w in client_weights]

        # Perform weighted averaging
        global_state = {}
        for key in client_params[0].keys():
            # Initialize with zeros
            global_state[key] = torch.zeros_like(client_params[0][key])

            # Weighted sum
            for client_param, weight in zip(client_params, client_weights):
                global_state[key] += weight * client_param[key]

        # Update global model
        self.global_model.load_state_dict(global_state)
        logger.info(f"Global model updated using {len(client_params)} client models")

    def get_global_model_state_dict(self) -> Dict:
        """Get global model state dictionary"""
        return self.global_model.state_dict()

    def distribute_global_model(self, clients: Dict):
        """Distribute global model to clients for personalization"""
        logger.info("Distributing global model to clients")

        global_state_dict = self.get_global_model_state_dict()

        for client_id, client in clients.items():
            # Create personalized model for client
            client.create_personalized_model(copy.deepcopy(self.global_model))
            logger.info(f"Distributed global model to client {client_id}")

    def evaluate_global_model(self, test_data: List[Dict]) -> Dict[str, float]:
        """Evaluate global model on test data"""
        if not test_data:
            logger.warning("No test data provided for evaluation")
            return {'accuracy': 0.0, 'f1_macro': 0.0}
            
        logger.info(f"Evaluating global model on {len(test_data)} test samples")
        
        # Create test dataloader
        test_dataloader = self._create_test_dataloader(test_data)
        if test_dataloader is None:
            logger.error("Failed to create test dataloader")
            return {'accuracy': 0.0, 'f1_macro': 0.0}
        
        # Evaluate
        self.global_model.eval()
        all_predictions = []
        all_labels = []
        
        with torch.no_grad():
            for batch in test_dataloader:
                input_ids = batch['input_ids'].to(self.device)
                attention_mask = batch['attention_mask'].to(self.device)
                labels = batch['labels'].to(self.device)
                
                outputs = self.global_model(
                    input_ids=input_ids,
                    attention_mask=attention_mask
                )
                
                predictions = torch.argmax(outputs.logits, dim=-1)
                all_predictions.extend(predictions.cpu().numpy())
                all_labels.extend(labels.cpu().numpy())
        
        # Calculate metrics
        from sklearn.metrics import accuracy_score, f1_score
        accuracy = accuracy_score(all_labels, all_predictions)
        f1_macro = f1_score(all_labels, all_predictions, average='macro')
        
        metrics = {
            'accuracy': accuracy,
            'f1_macro': f1_macro
        }
        
        logger.info(f"Evaluation results: {metrics}")
        return metrics

    def save_server_state(self, save_dir: str):
        """Save server state including global model and generators"""
        os.makedirs(save_dir, exist_ok=True)

        # Save global model
        global_model_path = os.path.join(save_dir, "global_model.pt")
        torch.save(self.global_model.state_dict(), global_model_path)

        # Save text generator
        generator_path = os.path.join(save_dir, "text_generator")
        self.text_generator.save_generator(generator_path)

        # Save training history
        import json
        history_path = os.path.join(save_dir, "training_history.json")
        with open(history_path, 'w') as f:
            json.dump(self.training_history, f, indent=2)

        logger.info(f"Server state saved to {save_dir}")

    def load_server_state(self, load_dir: str):
        """Load server state"""
        # Load global model
        global_model_path = os.path.join(load_dir, "global_model.pt")
        if os.path.exists(global_model_path):
            self.global_model.load_state_dict(torch.load(global_model_path))
            logger.info("Global model loaded")

        # Load text generator
        generator_path = os.path.join(load_dir, "text_generator")
        if os.path.exists(generator_path):
            self.text_generator.load_generator(generator_path)
            logger.info("Text generator loaded")

        # Load training history
        import json
        history_path = os.path.join(load_dir, "training_history.json")
        if os.path.exists(history_path):
            with open(history_path, 'r') as f:
                self.training_history = json.load(f)
            logger.info("Training history loaded")

    def get_training_statistics(self) -> Dict[str, Any]:
        """Get comprehensive training statistics"""
        if not self.training_history:
            return {}

        stats = {
            'total_rounds': len(self.training_history),
            'final_dkd_loss': self.training_history[-1]['dkd_loss'],
            'final_contrastive_loss': self.training_history[-1]['contrastive_loss'],
            'final_total_loss': self.training_history[-1]['total_loss'],
            'loss_history': {
                'dkd_loss': [round_stats['dkd_loss'] for round_stats in self.training_history],
                'contrastive_loss': [round_stats['contrastive_loss'] for round_stats in self.training_history],
                'total_loss': [round_stats['total_loss'] for round_stats in self.training_history]
            }
        }

        return stats

    def save_checkpoint(self, checkpoint_path: str) -> None:
        """
        Save training checkpoint

        Args:
            checkpoint_path: Path to save checkpoint
        """
        checkpoint = {
            'global_model_state_dict': self.global_model.state_dict(),
            'training_history': self.training_history,
            'algorithm': self.distillation_algorithm.value,
            'config': self.config.to_dict() if hasattr(self.config, 'to_dict') else str(self.config)
        }

        torch.save(checkpoint, checkpoint_path)
        logger.info(f"Checkpoint saved to {checkpoint_path}")

    def save_model(self, model_path: str) -> None:
        """
        Save final model

        Args:
            model_path: Path to save model
        """
        torch.save(self.global_model.state_dict(), model_path)
        logger.info(f"Model saved to {model_path}")

    def load_checkpoint(self, checkpoint_path: str) -> None:
        """
        Load training checkpoint

        Args:
            checkpoint_path: Path to checkpoint file
        """
        checkpoint = torch.load(checkpoint_path, map_location=self.device)

        self.global_model.load_state_dict(checkpoint['global_model_state_dict'])
        self.training_history = checkpoint.get('training_history', [])

        logger.info(f"Checkpoint loaded from {checkpoint_path}")

    def _create_test_dataloader(self, test_data: List[Dict]) -> Optional[DataLoader]:
        """Create test DataLoader from test data list"""
        if not test_data:
            logger.warning("No test data provided")
            return None
            
        try:
            # Extract text, labels, and domains
            texts = [item['text'] for item in test_data]
            labels = [item['label'] for item in test_data]
            domains = [item['domain'] for item in test_data]
            
            # Create dataset
            from ..data.amazon_dataset import AmazonReviewDataset
            dataset = AmazonReviewDataset(
                texts=texts,
                labels=labels,
                domains=domains,
                tokenizer=self.tokenizer,
                max_length=self.config.data.max_seq_length
            )
            
            # Create DataLoader
            return DataLoader(
                dataset,
                batch_size=32,
                shuffle=False,
                num_workers=2
            )
            
        except Exception as e:
            logger.error(f"Error creating test dataloader: {e}")
            return None
