"""
Configuration file for D³AFD implementation
"""
import os
from dataclasses import dataclass
from typing import List, Dict, Any

@dataclass
class DataConfig:
    """Data configuration"""
    dataset_name: str = "amazon_reviews_multi"
    domains: List[str] = None  # Will be set to product categories
    num_clients: int = 10
    samples_per_client: int = 1000
    test_split: float = 0.2
    val_split: float = 0.1
    max_seq_length: int = 512
    min_samples_per_domain: int = 100
    
    def __post_init__(self):
        if self.domains is None:
            self.domains = [
                "Books", "Electronics", "Home_and_Kitchen", 
                "Sports_and_Outdoors", "Toys_and_Games",
                "Clothing_Shoes_and_Jewelry", "Health_and_Personal_Care",
                "Automotive", "Tools_and_Home_Improvement", "Beauty"
            ]

@dataclass
class ModelConfig:
    """Model configuration"""
    # Text generator (T5-based)
    generator_model: str = "t5-base"
    generator_max_length: int = 256
    
    # Local teacher models
    teacher_model: str = "bert-base-uncased"
    teacher_hidden_size: int = 768
    teacher_num_layers: int = 12
    
    # Global student model
    student_model: str = "distilbert-base-uncased"
    student_hidden_size: int = 768
    student_num_layers: int = 6
    
    # Domain discriminator
    discriminator_hidden_size: int = 256
    discriminator_num_layers: int = 3
    
    # Classification
    num_classes: int = 5  # 1-5 star ratings
    dropout_rate: float = 0.1

@dataclass
class TrainingConfig:
    """Training configuration for D³AFD four-stage design"""
    # D³AFD federated rounds (each round includes all four stages)
    federated_rounds: int = 20  # Total federated learning rounds

    # Stage 1: Local training parameters - ANTI-OVERFITTING
    local_epochs: int = 3  # Fewer epochs to prevent overfitting
    local_batch_size: int = 16
    local_lr: float = 5e-4  # Higher but controlled LR
    label_smoothing: float = 0.2  # Strong label smoothing to prevent overconfidence
    weight_decay: float = 0.1  # Strong regularization

    # Stage 3: DKD distillation parameters
    distillation_rounds: int = 10  # Distillation iterations within each federated round
    distillation_epochs: int = 3   # Epochs per distillation iteration
    distillation_batch_size: int = 32
    distillation_lr: float = 1e-4
    
    # DKD parameters - optimized for better knowledge transfer
    alpha: float = 0.7  # Target class weight (increased for better target learning)
    beta: float = 0.3   # Non-target class weight (decreased to focus on targets)
    temperature: float = 3.0  # Lower temperature for sharper distributions and better gradients
    
    # Stage 3: Contrastive learning parameters
    contrastive_weight: float = 0.1
    contrastive_temperature: float = 0.07

    # Stage 4: Personalization parameters
    personalization_epochs: int = 3
    personalization_lr: float = 1e-4
    mu: float = 0.5  # Local KD weight for personalization

    # Stage 2: Pseudo data generation parameters
    pseudo_samples_per_domain: int = 500
    mixed_domain_samples: int = 200

@dataclass
class ExperimentConfig:
    """Experiment configuration"""
    seed: int = 42
    device: str = "cuda"
    log_interval: int = 10
    save_interval: int = 5
    output_dir: str = "outputs"
    log_dir: str = "logs"
    data_dir: str = "data"
    
    # Evaluation
    eval_metrics: List[str] = None
    
    def __post_init__(self):
        if self.eval_metrics is None:
            self.eval_metrics = ["accuracy", "f1_macro", "f1_weighted"]

@dataclass
class D3AFDConfig:
    """Complete D³AFD configuration"""
    data: DataConfig = None
    model: ModelConfig = None
    training: TrainingConfig = None
    experiment: ExperimentConfig = None
    
    def __post_init__(self):
        if self.data is None:
            self.data = DataConfig()
        if self.model is None:
            self.model = ModelConfig()
        if self.training is None:
            self.training = TrainingConfig()
        if self.experiment is None:
            self.experiment = ExperimentConfig()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert config to dictionary"""
        return {
            "data": self.data.__dict__,
            "model": self.model.__dict__,
            "training": self.training.__dict__,
            "experiment": self.experiment.__dict__
        }

def get_default_config() -> D3AFDConfig:
    """Get default configuration"""
    return D3AFDConfig()

def get_memory_optimized_config() -> D3AFDConfig:
    """Get memory-optimized configuration for GPU-constrained environments"""
    config = D3AFDConfig()

    # Use smaller models but reasonable sizes
    config.model.teacher_model = "distilbert-base-uncased"
    config.model.student_model = "distilbert-base-uncased"
    config.model.generator_model = "t5-small"
    config.model.generator_max_length = 128  # Increased for better text generation

    # Balanced data configuration for better learning
    config.data.num_clients = 5  # More clients for better federated learning
    config.data.samples_per_client = 300  # More samples per client
    config.data.max_seq_length = 128  # Reasonable sequence length

    # Reasonable batch sizes
    config.training.local_batch_size = 4
    config.training.distillation_batch_size = 8

    # More training iterations for better convergence
    config.training.federated_rounds = 5  # More federated rounds
    config.training.distillation_rounds = 5  # More distillation rounds
    config.training.local_epochs = 3  # More local epochs
    config.training.distillation_epochs = 2  # More distillation epochs

    # More pseudo data for better knowledge transfer
    config.training.pseudo_samples_per_domain = 100  # More pseudo samples
    config.training.mixed_domain_samples = 30  # More mixed samples

    return config

def get_balanced_performance_config() -> D3AFDConfig:
    """Get balanced configuration for good performance with reasonable memory usage"""
    config = D3AFDConfig()

    # Use efficient models
    config.model.teacher_model = "distilbert-base-uncased"
    config.model.student_model = "distilbert-base-uncased"
    config.model.generator_model = "t5-small"
    config.model.generator_max_length = 128

    # Balanced data configuration
    config.data.num_clients = 8  # More clients for better federated learning
    config.data.samples_per_client = 400  # Sufficient samples per client
    config.data.max_seq_length = 256  # Full sequence length for better understanding

    # Reasonable batch sizes
    config.training.local_batch_size = 8
    config.training.distillation_batch_size = 16

    # Sufficient training iterations
    config.training.federated_rounds = 10  # More federated rounds
    config.training.distillation_rounds = 8  # More distillation rounds
    config.training.local_epochs = 3
    config.training.distillation_epochs = 3
    config.training.personalization_epochs = 3

    # Adequate pseudo data for knowledge transfer
    config.training.pseudo_samples_per_domain = 150
    config.training.mixed_domain_samples = 50

    return config

def get_ultra_low_memory_config() -> D3AFDConfig:
    """Get ultra-low memory configuration for severely GPU-constrained environments"""
    config = D3AFDConfig()

    # Use smallest possible models
    config.model.teacher_model = "distilbert-base-uncased"
    config.model.student_model = "distilbert-base-uncased"
    config.model.generator_model = "t5-small"
    config.model.generator_max_length = 32

    # Minimal data sizes
    config.data.num_clients = 2
    config.data.samples_per_client = 100
    config.data.max_seq_length = 64

    # Minimal batch sizes
    config.training.local_batch_size = 2
    config.training.distillation_batch_size = 4

    # Minimal training iterations
    config.training.federated_rounds = 2
    config.training.distillation_rounds = 1
    config.training.local_epochs = 1
    config.training.distillation_epochs = 1

    # Minimal pseudo data
    config.training.pseudo_samples_per_domain = 20
    config.training.mixed_domain_samples = 10

    return config

def get_cpu_config() -> D3AFDConfig:
    """Get CPU-only configuration"""
    config = D3AFDConfig()

    # Force CPU
    config.experiment.device = "cpu"

    # Minimal configuration
    config.data.num_clients = 3
    config.data.samples_per_client = 200
    config.data.max_seq_length = 128

    # Small models
    config.model.teacher_model = "distilbert-base-uncased"
    config.model.student_model = "distilbert-base-uncased"
    config.model.generator_model = "t5-small"
    config.model.generator_max_length = 64

    # Small batches
    config.training.local_batch_size = 4
    config.training.distillation_batch_size = 8

    # Minimal training
    config.training.federated_rounds = 2
    config.training.distillation_rounds = 2
    config.training.local_epochs = 2
    config.training.distillation_epochs = 1

    # Minimal pseudo data
    config.training.pseudo_samples_per_domain = 50
    config.training.mixed_domain_samples = 20

    return config
