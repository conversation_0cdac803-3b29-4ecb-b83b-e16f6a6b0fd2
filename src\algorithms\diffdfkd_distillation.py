"""
DiffDFKD (Data-Free Knowledge Distillation with Diffusion Models) Implementation for D³AFD
Uses T5 text generation for creating synthetic Amazon review data
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import DataLoader, Dataset
from typing import Dict, List, Optional, Tuple, Any
import logging
import numpy as np
from transformers import T5Tokenizer, T5ForConditionalGeneration
import random

from ..models.base_models import StudentModel
from ..utils.memory_manager import get_memory_manager, memory_cleanup_decorator
from ..utils.logger import get_logger

logger = get_logger(__name__)


class SyntheticReviewDataset(Dataset):
    """Dataset for synthetic Amazon reviews generated by T5"""
    
    def __init__(self, synthetic_reviews: List[Dict], tokenizer, max_length: int = 512):
        self.synthetic_reviews = synthetic_reviews
        self.tokenizer = tokenizer
        self.max_length = max_length
    
    def __len__(self):
        return len(self.synthetic_reviews)
    
    def __getitem__(self, idx):
        review = self.synthetic_reviews[idx]
        text = review['text']
        label = review['label']
        domain = review['domain']
        
        # Tokenize
        encoding = self.tokenizer(
            text,
            truncation=True,
            padding='max_length',
            max_length=self.max_length,
            return_tensors='pt'
        )
        
        return {
            'input_ids': encoding['input_ids'].flatten(),
            'attention_mask': encoding['attention_mask'].flatten(),
            'labels': torch.tensor(label, dtype=torch.long),
            'domain': domain,
            'text': text
        }


class DiffDFKDDistillationTrainer:
    """
    DiffDFKD-based distillation trainer for D³AFD framework
    Uses T5 for synthetic text generation and multi-loss optimization
    """
    
    def __init__(self, config):
        self.config = config
        self.device = torch.device(config.experiment.device)
        
        # DiffDFKD specific parameters
        self.guided_scale = config.diffdfkd.guided_scale if hasattr(config, 'diffdfkd') else 7.0
        self.inference_steps = config.diffdfkd.inference_steps if hasattr(config, 'diffdfkd') else 50
        self.oh_weight = config.diffdfkd.oh_weight if hasattr(config, 'diffdfkd') else 1.0
        self.bn_weight = config.diffdfkd.bn_weight if hasattr(config, 'diffdfkd') else 0.1
        self.adv_weight = config.diffdfkd.adv_weight if hasattr(config, 'diffdfkd') else 1.0
        
        # Initialize T5 for text generation
        self.t5_model_name = config.diffdfkd.t5_model if hasattr(config, 'diffdfkd') else "t5-base"
        self.tokenizer = T5Tokenizer.from_pretrained(self.t5_model_name)
        self.t5_model = T5ForConditionalGeneration.from_pretrained(self.t5_model_name).to(self.device)
        
        # Domain-specific prompts for Amazon reviews
        self.domain_prompts = {
            'Electronics': [
                "Generate a review for an electronic device:",
                "Write a product review for electronics:",
                "Create a customer review for a gadget:"
            ],
            'Books': [
                "Generate a book review:",
                "Write a review for a book:",
                "Create a customer review for literature:"
            ],
            'Home_and_Kitchen': [
                "Generate a review for a home appliance:",
                "Write a review for kitchen equipment:",
                "Create a customer review for household items:"
            ],
            'Clothing_Shoes_and_Jewelry': [
                "Generate a review for clothing:",
                "Write a review for fashion items:",
                "Create a customer review for accessories:"
            ]
        }
        
        # Feature hooks for BatchNorm regularization
        self.feature_hooks = []
        
        logger.info("DiffDFKD distillation trainer initialized with T5 text generation")
    
    def register_feature_hooks(self, model: nn.Module):
        """Register forward hooks for BatchNorm feature regularization"""
        self.feature_hooks = []
        
        for name, module in model.named_modules():
            if isinstance(module, (nn.BatchNorm1d, nn.BatchNorm2d)):
                hook = FeatureHook(module)
                self.feature_hooks.append(hook)
        
        logger.info(f"Registered {len(self.feature_hooks)} feature hooks for BatchNorm regularization")
    
    def generate_synthetic_reviews(self, domain: str, num_samples: int, 
                                 sentiment_distribution: Optional[List[float]] = None) -> List[Dict]:
        """
        Generate synthetic Amazon reviews using T5
        
        Args:
            domain: Product domain (e.g., 'Electronics', 'Books')
            num_samples: Number of reviews to generate
            sentiment_distribution: Distribution of sentiments [negative, neutral, positive]
            
        Returns:
            List of synthetic review dictionaries
        """
        if sentiment_distribution is None:
            sentiment_distribution = [0.2, 0.3, 0.5]  # Default distribution
        
        synthetic_reviews = []
        prompts = self.domain_prompts.get(domain, self.domain_prompts['Electronics'])
        
        for i in range(num_samples):
            # Sample sentiment
            sentiment_idx = np.random.choice(3, p=sentiment_distribution)
            sentiment_labels = ['negative', 'neutral', 'positive']
            sentiment = sentiment_labels[sentiment_idx]
            
            # Create prompt
            base_prompt = random.choice(prompts)
            prompt = f"{base_prompt} Write a {sentiment} review."
            
            # Tokenize prompt
            input_ids = self.tokenizer.encode(prompt, return_tensors='pt').to(self.device)
            
            # Generate review
            with torch.no_grad():
                outputs = self.t5_model.generate(
                    input_ids,
                    max_length=150,
                    num_return_sequences=1,
                    temperature=0.8,
                    do_sample=True,
                    pad_token_id=self.tokenizer.pad_token_id
                )
            
            # Decode generated text
            generated_text = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
            
            # Clean up the text (remove prompt if present)
            if prompt in generated_text:
                generated_text = generated_text.replace(prompt, "").strip()
            
            synthetic_reviews.append({
                'text': generated_text,
                'label': sentiment_idx,  # 0: negative, 1: neutral, 2: positive
                'domain': domain,
                'sentiment': sentiment
            })
        
        logger.info(f"Generated {len(synthetic_reviews)} synthetic reviews for domain {domain}")
        return synthetic_reviews
    
    def compute_multi_loss(self, student_model: nn.Module, teacher_model: nn.Module,
                          inputs: torch.Tensor, target_labels: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        Compute multi-component loss for DiffDFKD
        
        Args:
            student_model: Student model
            teacher_model: Teacher model
            inputs: Input data
            target_labels: Target labels
            
        Returns:
            Dictionary of loss components
        """
        # Get model outputs
        student_outputs = student_model(inputs)
        with torch.no_grad():
            teacher_outputs = teacher_model(inputs)
        
        # One-hot loss (classification accuracy)
        loss_oh = F.cross_entropy(student_outputs, target_labels)
        
        # BatchNorm regularization loss
        loss_bn = torch.tensor(0.0, device=self.device)
        if self.feature_hooks:
            loss_bn = sum([hook.r_feature for hook in self.feature_hooks])
        
        # Adversarial loss (teacher-student consistency)
        student_probs = F.softmax(student_outputs, dim=1)
        teacher_probs = F.softmax(teacher_outputs, dim=1)
        
        # Only apply adversarial loss when predictions agree
        agreement_mask = (student_outputs.argmax(1) == teacher_outputs.argmax(1)).float()
        loss_adv = -(F.kl_div(F.log_softmax(student_outputs, dim=1), 
                             teacher_probs.detach(), reduction='none').sum(1) * agreement_mask).mean()
        
        return {
            'loss_oh': loss_oh,
            'loss_bn': loss_bn,
            'loss_adv': loss_adv
        }
    
    @memory_cleanup_decorator
    def train_student_model(self, student_model: nn.Module, teacher_models: Dict[str, nn.Module],
                          pseudo_dataloader: DataLoader, num_epochs: int = 3) -> Dict[str, float]:
        """
        Train student model using DiffDFKD with T5-generated synthetic data
        
        Args:
            student_model: Student model to train
            teacher_models: Dictionary of teacher models
            pseudo_dataloader: DataLoader for pseudo samples (not used, we generate our own)
            num_epochs: Number of training epochs
            
        Returns:
            Dictionary of training metrics
        """
        logger.info("Starting DiffDFKD distillation training with T5 text generation...")
        
        # Register feature hooks for BatchNorm regularization
        self.register_feature_hooks(student_model)
        
        # Generate synthetic data for each domain
        all_synthetic_reviews = []
        samples_per_domain = self.config.training.pseudo_samples_per_domain
        
        for domain in teacher_models.keys():
            domain_reviews = self.generate_synthetic_reviews(domain, samples_per_domain)
            all_synthetic_reviews.extend(domain_reviews)
        
        # Create synthetic dataset
        synthetic_dataset = SyntheticReviewDataset(all_synthetic_reviews, self.tokenizer)
        synthetic_dataloader = DataLoader(
            synthetic_dataset,
            batch_size=self.config.training.batch_size,
            shuffle=True,
            num_workers=2
        )
        
        # Setup optimizer
        optimizer = torch.optim.AdamW(
            student_model.parameters(),
            lr=self.config.training.distillation_lr
        )
        
        student_model.train()
        total_metrics = {
            'total_loss': 0,
            'oh_loss': 0,
            'bn_loss': 0,
            'adv_loss': 0
        }
        num_batches = 0
        
        for epoch in range(num_epochs):
            epoch_metrics = {key: 0 for key in total_metrics.keys()}
            epoch_batches = 0
            
            for batch in synthetic_dataloader:
                try:
                    # Move batch to device
                    input_ids = batch['input_ids'].to(self.device)
                    attention_mask = batch['attention_mask'].to(self.device)
                    labels = batch['labels'].to(self.device)
                    domains = batch['domain']
                    
                    # Get ensemble teacher predictions
                    teacher_outputs = []
                    for domain in set(domains):
                        if domain in teacher_models:
                            teacher_model = teacher_models[domain]
                            teacher_model.eval()
                            with torch.no_grad():
                                domain_mask = [d == domain for d in domains]
                                if any(domain_mask):
                                    domain_inputs = input_ids[domain_mask]
                                    domain_attention = attention_mask[domain_mask]
                                    teacher_out = teacher_model(domain_inputs, attention_mask=domain_attention)
                                    teacher_outputs.append(teacher_out)
                    
                    if not teacher_outputs:
                        continue
                    
                    # Compute multi-component loss
                    loss_components = self.compute_multi_loss(
                        student_model, teacher_models[domains[0]], 
                        input_ids, labels
                    )
                    
                    # Combine losses
                    total_loss = (self.oh_weight * loss_components['loss_oh'] +
                                self.bn_weight * loss_components['loss_bn'] +
                                self.adv_weight * loss_components['loss_adv'])
                    
                    # Backward pass
                    optimizer.zero_grad()
                    total_loss.backward()
                    optimizer.step()
                    
                    # Update metrics
                    epoch_metrics['total_loss'] += total_loss.item()
                    epoch_metrics['oh_loss'] += loss_components['loss_oh'].item()
                    epoch_metrics['bn_loss'] += loss_components['loss_bn'].item()
                    epoch_metrics['adv_loss'] += loss_components['loss_adv'].item()
                    epoch_batches += 1
                    
                except Exception as e:
                    logger.error(f"Error in DiffDFKD training batch: {e}")
                    continue
            
            # Average epoch metrics
            for key in epoch_metrics:
                if epoch_batches > 0:
                    epoch_metrics[key] /= epoch_batches
                    total_metrics[key] += epoch_metrics[key]
            
            num_batches += 1
            
            logger.info(f"DiffDFKD Epoch {epoch+1}/{num_epochs}, "
                       f"Total Loss: {epoch_metrics['total_loss']:.4f}, "
                       f"OH: {epoch_metrics['oh_loss']:.4f}, "
                       f"BN: {epoch_metrics['bn_loss']:.4f}, "
                       f"ADV: {epoch_metrics['adv_loss']:.4f}")
        
        # Average total metrics
        for key in total_metrics:
            if num_batches > 0:
                total_metrics[key] /= num_batches
        
        # Clean up hooks
        for hook in self.feature_hooks:
            hook.close()
        
        return {
            'diffdfkd_loss': total_metrics['total_loss'],
            'oh_loss': total_metrics['oh_loss'],
            'bn_loss': total_metrics['bn_loss'],
            'adv_loss': total_metrics['adv_loss'],
            'algorithm': 'diffdfkd',
            'epochs': num_epochs,
            'synthetic_samples': len(all_synthetic_reviews)
        }
    
    def get_algorithm_info(self) -> Dict[str, Any]:
        """Get information about DiffDFKD algorithm"""
        return {
            "name": "DiffDFKD",
            "description": "Data-Free Knowledge Distillation with T5 Text Generation",
            "supports_data_free": True,
            "supports_text_generation": True,
            "t5_model": self.t5_model_name,
            "guided_scale": self.guided_scale,
            "loss_weights": {
                "oh_weight": self.oh_weight,
                "bn_weight": self.bn_weight,
                "adv_weight": self.adv_weight
            }
        }


class FeatureHook:
    """Hook for capturing BatchNorm feature statistics"""
    
    def __init__(self, module):
        self.hook = module.register_forward_hook(self.hook_fn)
        self.r_feature = torch.tensor(0.0)
    
    def hook_fn(self, module, input, output):
        """Compute feature distribution regularization"""
        if len(input) > 0 and input[0] is not None:
            nch = input[0].shape[1] if len(input[0].shape) > 1 else input[0].shape[0]
            
            if len(input[0].shape) >= 2:
                mean = input[0].mean([0] + list(range(2, len(input[0].shape))))
                var = input[0].permute(1, 0, *range(2, len(input[0].shape))).contiguous().view([nch, -1]).var(1, unbiased=False)
                
                # Force mean and variance to match between distributions
                if hasattr(module, 'running_var') and hasattr(module, 'running_mean'):
                    self.r_feature = (torch.norm(module.running_var.data.type(var.type()) - var, 2) +
                                    torch.norm(module.running_mean.data.type(mean.type()) - mean, 2))
    
    def close(self):
        """Remove the hook"""
        self.hook.remove()
