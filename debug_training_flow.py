#!/usr/bin/env python3
"""
Debug script to check the training flow
"""

import sys
import os

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def debug_training_flow():
    """Debug the training flow"""
    print("=" * 60)
    print("DEBUG: Training Flow")
    print("=" * 60)
    
    try:
        # Step 1: Create config
        from src.utils.config import Config
        from quick_test_algorithms import setup_quick_test_config
        
        config_dict = setup_quick_test_config('dkdm')
        config = Config(config_dict)
        print("✅ Step 1: Config created successfully")
        
        # Step 2: Create dataset
        from src.data.amazon_dataset import AmazonReviewDataset
        dataset = AmazonReviewDataset(config=config)
        print(f"✅ Step 2: Dataset created with {len(dataset)} samples")
        
        # Step 3: Setup clients (same as in train_d3afd_multi_algorithm.py)
        from src.federated.client import FederatedClient
        
        clients = {}
        domains = config.data.domains
        
        print(f"✅ Step 3: Setting up {len(domains)} clients for domains: {domains}")
        
        for i, domain in enumerate(domains):
            client_id = f"client_{i}_{domain}"
            
            # Create domain-specific dataset
            domain_data = dataset.get_domain_data(domain)
            print(f"  Domain data for {domain}: {len(domain_data)} samples")
            
            # Create client
            client = FederatedClient(
                client_id=client_id,
                domain=domain,
                config=config,
                local_data=domain_data
            )
            
            clients[client_id] = client
            print(f"  Created client {client_id} with {len(domain_data)} samples")
            print(f"  Client has train_dataloader: {client.train_dataloader is not None}")
            
            if client.train_dataloader:
                print(f"  Train dataloader length: {len(client.train_dataloader)}")
            else:
                print(f"  ❌ No train_dataloader!")
        
        print("\n" + "=" * 60)
        print("DEBUG COMPLETED - Training flow looks good!")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        import traceback
        print(f"❌ Error in debug: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = debug_training_flow()
    if success:
        print("\n🎉 All checks passed! The issue might be elsewhere.")
    else:
        print("\n💥 Found issues in the training flow.")
