#!/usr/bin/env python3
"""
Debug script to trace the actual training execution
"""

import sys
import os

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def debug_actual_training():
    """Debug the actual training execution step by step"""
    print("=" * 60)
    print("DEBUG: Actual Training Execution")
    print("=" * 60)
    
    try:
        # Step 1: Import and setup exactly like quick_test_algorithms.py
        import importlib
        
        modules_to_reload = [
            'src.federated.client',
            'src.data.amazon_dataset',
            'src.utils.config',
            'train_d3afd_multi_algorithm'
        ]
        
        for module_name in modules_to_reload:
            if module_name in sys.modules:
                importlib.reload(sys.modules[module_name])
        
        from quick_test_algorithms import setup_quick_test_config
        from src.utils.config import Config
        from src.utils.logger import setup_logger
        
        print("✅ Step 1: Modules imported and reloaded")
        
        # Step 2: Create config exactly like quick_test_algorithms.py
        config_dict = setup_quick_test_config('dkdm')
        config = Config(config_dict)
        logger = setup_logger(config.experiment.log_level)
        
        print("✅ Step 2: Config and logger setup")
        
        # Step 3: Import and call the exact same functions as train_d3afd_multi_algorithm.py
        from train_d3afd_multi_algorithm import setup_clients
        from src.data.amazon_dataset import AmazonReviewDataset
        
        print("✅ Step 3: Training modules imported")
        
        # Step 4: Create dataset exactly like in training script
        dataset = AmazonReviewDataset(config)
        print(f"✅ Step 4: Dataset created with {len(dataset)} samples")
        
        # Step 5: Setup clients exactly like in training script
        print("\n" + "-" * 40)
        print("Setting up clients (same as training script):")
        print("-" * 40)
        
        clients = setup_clients(config, dataset)
        
        print(f"\nClients created: {len(clients)}")
        for client_id, client in clients.items():
            print(f"  {client_id}:")
            print(f"    Has train_dataloader: {client.train_dataloader is not None}")
            print(f"    Has tokenizer: {client.tokenizer is not None}")
            print(f"    Local data: {client.local_data is not None}")
            if client.local_data:
                print(f"    Local data length: {len(client.local_data)}")
            if client.train_dataloader:
                print(f"    Train dataloader length: {len(client.train_dataloader)}")
        
        print("\n" + "=" * 60)
        print("DEBUG COMPLETED")
        print("=" * 60)
        
        return clients
        
    except Exception as e:
        import traceback
        print(f"❌ Error in debug: {e}")
        traceback.print_exc()
        return None

if __name__ == "__main__":
    clients = debug_actual_training()
    if clients:
        print(f"\n🎉 Successfully created {len(clients)} clients!")
        for client_id, client in clients.items():
            has_data = client.train_dataloader is not None
            print(f"  {client_id}: {'✅' if has_data else '❌'} {'Has data' if has_data else 'No data'}")
    else:
        print("\n💥 Failed to create clients.")
