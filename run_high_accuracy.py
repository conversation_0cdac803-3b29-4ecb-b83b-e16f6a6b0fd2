"""
High accuracy D³AFD experiment runner
Optimized specifically to achieve 60-80% global accuracy
"""
import os
import sys
import logging
import torch
import gc
from datetime import datetime

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.config import get_default_config
from src.federated.d3afd_framework import D3AFDFramework

def setup_high_accuracy_config():
    """Create high accuracy configuration targeting 60-80% global accuracy"""
    config = get_default_config()
    
    # Use more powerful models for better capacity
    config.model.teacher_model = "bert-base-uncased"  # More powerful than DistilBERT
    config.model.student_model = "bert-base-uncased"  # Same capacity as teacher
    config.model.generator_model = "t5-base"  # Larger generator for better pseudo data
    config.model.generator_max_length = 256
    
    # Optimize data configuration
    config.data.num_clients = 10  # More clients for better federated learning
    config.data.samples_per_client = 600  # More samples per client
    config.data.max_seq_length = 512  # Longer sequences for better understanding
    
    # Extensive training configuration
    config.training.federated_rounds = 15  # More rounds for convergence
    config.training.distillation_rounds = 10  # More distillation rounds
    config.training.local_epochs = 4  # Sufficient local training
    config.training.distillation_epochs = 4  # More distillation epochs
    config.training.personalization_epochs = 6  # More personalization
    
    # Optimized batch sizes
    config.training.local_batch_size = 4  # Smaller for better gradients
    config.training.distillation_batch_size = 8  # Balanced
    
    # Optimized learning rates
    config.training.local_lr = 1e-5  # Lower for stability
    config.training.distillation_lr = 3e-5  # Moderate for knowledge transfer
    config.training.personalization_lr = 5e-5  # Higher for adaptation
    
    # Regularization
    config.training.weight_decay = 0.01
    config.training.dropout_rate = 0.1
    
    # Improved loss weights
    config.training.contrastive_weight = 0.3  # Balanced contrastive learning
    config.training.dkd_alpha = 0.5  # Balanced DKD components
    config.training.dkd_beta = 0.5
    
    # More pseudo data for better knowledge transfer
    config.training.pseudo_samples_per_domain = 300  # Significantly more
    config.training.mixed_domain_samples = 100
    
    # Output settings
    config.experiment.output_dir = "high_accuracy_outputs"
    config.experiment.data_dir = "high_accuracy_data"
    
    return config

def clear_gpu_memory():
    """Clear GPU memory"""
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        torch.cuda.ipc_collect()
        gc.collect()

def setup_logging():
    """Setup comprehensive logging"""
    os.makedirs('logs', exist_ok=True)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_filename = f"logs/high_accuracy_{timestamp}.log"
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_filename, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    logger = logging.getLogger(__name__)
    logger.info(f"High accuracy experiment logging initialized. Log file: {log_filename}")
    return logger

def main():
    """Main function for high accuracy experiment"""
    logger = setup_logging()
    
    logger.info("Starting High Accuracy D³AFD Experiment")
    logger.info("=" * 70)
    logger.info("🎯 TARGET: Achieve 60-80% global accuracy")
    logger.info("🔧 Optimizations applied:")
    logger.info("   - BERT-base models for higher capacity")
    logger.info("   - T5-base generator for better pseudo data")
    logger.info("   - More clients and samples")
    logger.info("   - Extensive training rounds")
    logger.info("   - Optimized learning rates and regularization")
    logger.info("   - Improved loss balancing")
    
    # Set environment variables for optimal performance
    os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'expandable_segments:True,max_split_size_mb:512'
    os.environ['TOKENIZERS_PARALLELISM'] = 'false'
    
    # Check GPU memory
    if torch.cuda.is_available():
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        logger.info(f"GPU Memory Available: {gpu_memory:.2f} GB")
        
        if gpu_memory < 12:
            logger.warning("⚠️  Less than 12GB GPU memory detected!")
            logger.warning("   Consider using run_improved_experiment.py for lower memory usage")
        
        clear_gpu_memory()
        
        # Conservative memory usage for stability
        torch.cuda.set_per_process_memory_fraction(0.85)
        logger.info("Set GPU memory fraction to 85%")
    else:
        logger.error("❌ CUDA not available! This experiment requires GPU.")
        return 1
    
    # Get high accuracy config
    config = setup_high_accuracy_config()
    
    logger.info("High Accuracy Configuration:")
    logger.info(f"  🏢 Clients: {config.data.num_clients}")
    logger.info(f"  📊 Samples per client: {config.data.samples_per_client}")
    logger.info(f"  📏 Max sequence length: {config.data.max_seq_length}")
    logger.info(f"  🤖 Teacher model: {config.model.teacher_model}")
    logger.info(f"  📝 Generator model: {config.model.generator_model}")
    logger.info(f"  🔄 Federated rounds: {config.training.federated_rounds}")
    logger.info(f"  🧪 Distillation rounds: {config.training.distillation_rounds}")
    logger.info(f"  🎭 Pseudo samples per domain: {config.training.pseudo_samples_per_domain}")
    logger.info(f"  📚 Local epochs: {config.training.local_epochs}")
    logger.info(f"  🎯 Personalization epochs: {config.training.personalization_epochs}")
    
    try:
        # Initialize framework
        logger.info("Initializing D³AFD Framework...")
        framework = D3AFDFramework(config)
        
        clear_gpu_memory()
        
        # Run training
        logger.info("Starting high accuracy training...")
        logger.info("💡 Expected improvements:")
        logger.info("   - Better model capacity with BERT-base")
        logger.info("   - Higher quality pseudo data with T5-base")
        logger.info("   - More comprehensive training")
        logger.info("   - Better regularization to prevent overfitting")
        
        results = framework.run_complete_training(force_reload_data=False)
        
        # Analyze results
        logger.info("Training completed successfully!")
        logger.info("=" * 70)
        logger.info("📊 FINAL RESULTS ANALYSIS:")
        
        if 'global_model' in results:
            global_acc = results['global_model'].get('accuracy', 0)
            global_f1 = results['global_model'].get('f1_macro', 0)
            logger.info(f"🌍 Global Model Performance:")
            logger.info(f"   Accuracy: {global_acc:.4f} ({global_acc*100:.2f}%)")
            logger.info(f"   F1-Macro: {global_f1:.4f}")
            
            # Achievement analysis
            if global_acc >= 0.8:
                logger.info("   🎉 EXCELLENT! Exceeded 80% target!")
            elif global_acc >= 0.6:
                logger.info("   ✅ SUCCESS! Achieved 60-80% target range!")
            elif global_acc >= 0.4:
                logger.info("   📈 GOOD PROGRESS! Significant improvement from baseline")
            else:
                logger.info("   ⚠️  NEEDS IMPROVEMENT! Consider further optimization")
        
        if 'personalized_models' in results:
            personal_accs = [r.get('accuracy', 0) for r in results['personalized_models'].values()]
            if personal_accs:
                avg_personal_acc = sum(personal_accs) / len(personal_accs)
                max_personal_acc = max(personal_accs)
                min_personal_acc = min(personal_accs)
                
                logger.info(f"👤 Personalized Models Performance:")
                logger.info(f"   Average: {avg_personal_acc:.4f} ({avg_personal_acc*100:.2f}%)")
                logger.info(f"   Best: {max_personal_acc:.4f} ({max_personal_acc*100:.2f}%)")
                logger.info(f"   Worst: {min_personal_acc:.4f} ({min_personal_acc*100:.2f}%)")
                
                # Personalization effectiveness
                if 'global_model' in results:
                    global_acc = results['global_model'].get('accuracy', 0)
                    improvement = avg_personal_acc - global_acc
                    logger.info(f"   Personalization Gain: {improvement:.4f} ({improvement*100:.2f}%)")
        
        logger.info("=" * 70)
        logger.info(f"📁 Results saved to: {config.experiment.output_dir}")
        
        return 0
        
    except torch.cuda.OutOfMemoryError as e:
        logger.error("CUDA Out of Memory Error!")
        logger.error("Try reducing batch sizes or using run_improved_experiment.py")
        logger.error(f"Error details: {e}")
        return 1
        
    except Exception as e:
        logger.error(f"Experiment failed: {e}")
        logger.error("Full traceback:")
        import traceback
        logger.error(traceback.format_exc())
        return 1
    
    finally:
        clear_gpu_memory()
        logger.info("High accuracy experiment completed.")

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
