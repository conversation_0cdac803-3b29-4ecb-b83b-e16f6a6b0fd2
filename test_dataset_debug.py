#!/usr/bin/env python3
"""
Debug script to test dataset loading
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.data.amazon_dataset import AmazonReviewDataset
from src.utils.config import Config

def test_dataset_loading():
    """Test if the dataset is loading properly"""
    
    # Create a minimal config
    config_dict = {
        'model': {
            'model_name': 'distilbert-base-uncased',
            'student_model': 'distilbert-base-uncased',
            'teacher_model': 'distilbert-base-uncased',
            'generator_model': 't5-small',
            'generator_max_length': 128,
            'num_labels': 3,
            'num_classes': 3,
            'hidden_size': 768,
            'dropout': 0.1,
            'dropout_rate': 0.1
        },
        'data': {
            'domains': ['Electronics', 'Books'],
            'max_length': 256,
            'max_seq_length': 256
        },
        'experiment': {
            'device': 'cpu',
            'data_dir': 'test_data'
        }
    }
    
    config = Config(config_dict)
    
    print("Testing dataset loading...")
    
    # Create dataset
    dataset = AmazonReviewDataset(config)
    
    print(f"Dataset loaded with {len(dataset)} samples")
    print(f"Texts: {len(dataset.texts)}")
    print(f"Labels: {len(dataset.labels)}")
    print(f"Domains: {len(dataset.domains)}")
    
    if len(dataset.texts) > 0:
        print(f"Sample text: {dataset.texts[0]}")
        print(f"Sample label: {dataset.labels[0]}")
        print(f"Sample domain: {dataset.domains[0]}")
        
        # Test domain data retrieval
        electronics_data = dataset.get_domain_data('Electronics')
        books_data = dataset.get_domain_data('Books')
        
        print(f"Electronics samples: {len(electronics_data)}")
        print(f"Books samples: {len(books_data)}")
        
        # Test test data retrieval
        test_data = dataset.get_test_data()
        print(f"Test data samples: {len(test_data)}")
        
    else:
        print("ERROR: No data loaded!")

if __name__ == "__main__":
    test_dataset_loading() 