"""
T5-based Text Generator for Pseudo Data Generation
Replaces diffusion models for text domain in D³AFD
"""
import os
import torch
import torch.nn as nn
from transformers import T5ForConditionalGeneration, T5Tokenizer
from typing import List, Dict, Optional, Tuple
import random
import logging
from .dynamic_domain_descriptor import DynamicDomainDescriptor

logger = logging.getLogger(__name__)

class T5TextGenerator:
    """T5-based text generator for creating pseudo review data"""
    
    def __init__(self, config, finetuned_model_path=None):
        self.config = config
        self.device = torch.device(config.experiment.device)

        # Determine which model to load
        if finetuned_model_path and os.path.exists(finetuned_model_path):
            model_path = finetuned_model_path
            self.is_finetuned = True
            logger.info(f"Loading finetuned T5 model from: {model_path}")
        else:
            model_path = config.model.generator_model
            self.is_finetuned = False
            logger.info(f"Loading pretrained T5 model: {model_path}")

        # Load T5 model and tokenizer
        self.model = T5ForConditionalGeneration.from_pretrained(model_path).to(self.device)
        self.tokenizer = T5Tokenizer.from_pretrained(model_path)

        # Domain templates for conditional generation
        self.domain_templates = self._create_domain_templates()
        self.rating_templates = self._create_rating_templates()

        # Enhanced generation parameters for finetuned model
        self.max_length = getattr(config.model, 'generator_max_length', 200)
        self.temperature = 0.8
        self.num_beams = 4

        # 动态域描述符支持
        self.domain_descriptors = {}  # 存储客户端域描述符
        self.dynamic_descriptor_generator = None  # 将在需要时初始化
        self.use_dynamic_descriptors = True  # 启用动态域描述符

        logger.info("T5生成器支持动态域描述符生成")
        
    def _create_domain_templates(self) -> Dict[str, List[str]]:
        """Create domain-specific generation templates"""
        templates = {
            'Books': [
                "Generate a review for a book about",
                "Write a book review discussing",
                "Create a review for a novel that",
                "Generate feedback about a book with"
            ],
            'Electronics': [
                "Generate a review for an electronic device that",
                "Write a review about a gadget with",
                "Create feedback for electronics that",
                "Generate a review for technology that"
            ],
            'Home_and_Kitchen': [
                "Generate a review for a kitchen appliance that",
                "Write a review about home equipment that",
                "Create feedback for household items that",
                "Generate a review for cooking tools that"
            ],
            'Sports_and_Outdoors': [
                "Generate a review for sports equipment that",
                "Write a review about outdoor gear that",
                "Create feedback for fitness products that",
                "Generate a review for athletic items that"
            ],
            'Toys_and_Games': [
                "Generate a review for a toy that",
                "Write a review about a game that",
                "Create feedback for children's products that",
                "Generate a review for entertainment items that"
            ],
            'Clothing_Shoes_and_Jewelry': [
                "Generate a review for clothing that",
                "Write a review about fashion items that",
                "Create feedback for accessories that",
                "Generate a review for apparel that"
            ],
            'Health_and_Personal_Care': [
                "Generate a review for health products that",
                "Write a review about personal care items that",
                "Create feedback for wellness products that",
                "Generate a review for beauty items that"
            ],
            'Automotive': [
                "Generate a review for car accessories that",
                "Write a review about automotive parts that",
                "Create feedback for vehicle equipment that",
                "Generate a review for car products that"
            ],
            'Tools_and_Home_Improvement': [
                "Generate a review for tools that",
                "Write a review about home improvement items that",
                "Create feedback for construction equipment that",
                "Generate a review for hardware that"
            ],
            'Beauty': [
                "Generate a review for beauty products that",
                "Write a review about cosmetics that",
                "Create feedback for skincare items that",
                "Generate a review for makeup that"
            ]
        }
        return templates
    
    def _create_rating_templates(self) -> Dict[int, List[str]]:
        """Create rating-specific generation templates"""
        templates = {
            0: [  # 1-star (very negative)
                "is terrible and disappointing",
                "has poor quality and doesn't work",
                "is a waste of money",
                "broke immediately after use",
                "is the worst product ever"
            ],
            1: [  # 2-star (negative)
                "has some issues and problems",
                "is below average quality",
                "doesn't meet expectations",
                "has several flaws",
                "is not worth the price"
            ],
            2: [  # 3-star (neutral)
                "is okay but nothing special",
                "has average quality",
                "works as expected",
                "is decent for the price",
                "has both pros and cons"
            ],
            3: [  # 4-star (positive)
                "is good quality and works well",
                "exceeds expectations",
                "is worth buying",
                "has great features",
                "is highly recommended"
            ],
            4: [  # 5-star (very positive)
                "is absolutely amazing",
                "has excellent quality",
                "is perfect in every way",
                "is the best purchase ever",
                "is outstanding and exceptional"
            ]
        }
        return templates

    def generate_pseudo_samples(self, domain: str, num_samples: int,
                               target_rating: Optional[int] = None) -> List[Dict[str, any]]:
        """Generate pseudo samples for a specific domain"""
        logger.info(f"Generating {num_samples} pseudo samples for domain: {domain}")

        pseudo_samples = []

        # Process in batches for better efficiency
        batch_size = min(10, num_samples)  # Process 10 samples at a time

        for i in range(0, num_samples, batch_size):
            current_batch_size = min(batch_size, num_samples - i)

            # Generate batch of samples
            for _ in range(current_batch_size):
                # Select rating (0-4) if not specified
                if target_rating is None:
                    rating = random.randint(0, 4)
                else:
                    rating = target_rating

                # Create generation prompt (动态或静态)
                if self.use_dynamic_descriptors and self.domain_descriptors:
                    prompt = self._create_dynamic_prompt(domain, rating, self.domain_descriptors)
                else:
                    prompt = self._create_generation_prompt(domain, rating)

                # Generate text with quality retry mechanism
                generated_text = None
                for attempt in range(3):  # 最多重试3次
                    candidate_text = self._generate_text(prompt)

                    # 质量检查
                    if self._is_quality_text(candidate_text):
                        generated_text = candidate_text
                        break
                    elif attempt == 2:  # 最后一次尝试
                        generated_text = candidate_text  # 使用最后的结果

                pseudo_samples.append({
                    'text': generated_text,
                    'label': rating,
                    'domain': domain,
                    'prompt': prompt
                })

            # Clear memory after each batch
            if torch.cuda.is_available():
                torch.cuda.empty_cache()

        return pseudo_samples

    def generate_mixed_domain_samples(self, domains: List[str], num_samples: int) -> List[Dict[str, any]]:
        """Generate mixed domain samples by combining domain characteristics"""
        logger.info(f"Generating {num_samples} mixed domain samples")

        mixed_samples = []

        for _ in range(num_samples):
            # Select 2 random domains to mix
            selected_domains = random.sample(domains, 2)
            rating = random.randint(0, 4)

            # Create mixed prompt
            prompt = self._create_mixed_domain_prompt(selected_domains, rating)

            # Generate text
            generated_text = self._generate_text(prompt)

            # Assign to one of the mixed domains randomly
            assigned_domain = random.choice(selected_domains)

            mixed_samples.append({
                'text': generated_text,
                'label': rating,
                'domain': assigned_domain,
                'mixed_domains': selected_domains,
                'prompt': prompt
            })

        return mixed_samples

    def _create_generation_prompt(self, domain: str, rating: int) -> str:
        """Create generation prompt for specific domain and rating"""
        # 使用更精确的提示工程，无论是否微调都使用改进的提示
        rating_descriptions = {
            0: "terrible and disappointing",
            1: "poor quality and doesn't work",
            2: "average and okay",
            3: "good quality and recommended",
            4: "excellent and amazing"
        }

        # 创建更具体的提示，包含产品类型信息
        domain_specifics = {
            'Books': 'book',
            'Electronics': 'electronic device',
            'Home_and_Kitchen': 'kitchen appliance',
            'Sports_and_Outdoors': 'sports equipment',
            'Toys_and_Games': 'toy',
            'Clothing_Shoes_and_Jewelry': 'clothing item',
            'Health_and_Personal_Care': 'health product',
            'Automotive': 'car accessory',
            'Tools_and_Home_Improvement': 'tool',
            'Beauty': 'beauty product'
        }

        product_type = domain_specifics.get(domain, 'product')
        rating_desc = rating_descriptions[rating]

        # 使用更自然的提示格式
        prompt = f"Review: This {product_type} is {rating_desc}."

        return prompt

    def _create_mixed_domain_prompt(self, domains: List[str], rating: int) -> str:
        """Create generation prompt for mixed domains"""
        domain1, domain2 = domains

        # Mix templates from both domains
        template1 = random.choice(self.domain_templates[domain1])
        template2 = random.choice(self.domain_templates[domain2])
        rating_template = random.choice(self.rating_templates[rating])

        # Create mixed prompt
        prompt = f"Generate a review combining {domain1.lower()} and {domain2.lower()} features that {rating_template}."
        return prompt

    def _generate_text(self, prompt: str) -> str:
        """Generate text using T5 model"""
        # Tokenize input
        input_ids = self.tokenizer.encode(
            prompt,
            return_tensors='pt',
            max_length=512,
            truncation=True
        ).to(self.device)

        # Generate with optimized parameters for better quality
        with torch.no_grad():
            outputs = self.model.generate(
                input_ids,
                max_length=self.max_length,
                min_length=20,  # 合理的最小长度
                num_return_sequences=1,
                temperature=0.8,  # 平衡创造性和一致性
                do_sample=True,
                top_p=0.95,  # 稍微提高以增加多样性
                top_k=40,  # 减少以提高质量
                repetition_penalty=1.3,  # 增强以减少重复
                length_penalty=1.1,  # 轻微鼓励更长的文本
                no_repeat_ngram_size=2,  # 避免2-gram重复
                pad_token_id=self.tokenizer.pad_token_id,
                eos_token_id=self.tokenizer.eos_token_id,
                early_stopping=True  # 启用早停
            )

        # Decode generated text
        generated_text = self.tokenizer.decode(outputs[0], skip_special_tokens=True)

        # 强化后处理：清理和改进生成文本
        generated_text = self._post_process_text(generated_text, prompt)

        # Clear intermediate tensors from memory
        del outputs, input_ids
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

        return generated_text

    def _post_process_text(self, text: str, prompt: str) -> str:
        """强化后处理生成的文本"""
        import re

        # 1. 移除提示词
        if prompt.lower() in text.lower():
            text = text.replace(prompt, "").strip()

        # 2. 移除常见的T5生成问题
        # 移除开头的"Review:"等标记
        text = re.sub(r'^(Review:|review:|REVIEW:)\s*', '', text, flags=re.IGNORECASE)

        # 3. 移除多余的标点符号和空格
        text = re.sub(r'\s+', ' ', text)  # 多个空格变成一个
        text = re.sub(r'[.]{2,}', '.', text)  # 多个句号变成一个
        text = re.sub(r'[!]{2,}', '!', text)  # 多个感叹号变成一个
        text = re.sub(r'[?]{2,}', '?', text)  # 多个问号变成一个

        # 4. 移除非英文字符（保留基本标点）
        text = re.sub(r'[^\w\s.,!?\'"-]', '', text)

        # 5. 确保句子以适当的标点结尾
        text = text.strip()
        if text and not text[-1] in '.!?':
            text += '.'

        # 6. 移除过短的文本
        if len(text.split()) < 3:
            # 如果生成的文本太短，返回一个基本的评论
            return self._generate_fallback_review()

        # 7. 移除重复的句子
        sentences = text.split('.')
        unique_sentences = []
        for sentence in sentences:
            sentence = sentence.strip()
            if sentence and sentence not in unique_sentences:
                unique_sentences.append(sentence)

        if unique_sentences:
            text = '. '.join(unique_sentences)
            if not text.endswith('.'):
                text += '.'

        return text

    def _generate_fallback_review(self) -> str:
        """生成备用评论（当主要生成失败时）"""
        fallback_reviews = [
            "This product works as expected.",
            "Good value for the price.",
            "Average quality product.",
            "Would recommend to others.",
            "Not satisfied with this purchase."
        ]
        return random.choice(fallback_reviews)

    def update_domain_descriptors(self, client_descriptors: Dict[int, Dict]) -> None:
        """更新客户端域描述符"""
        logger.info(f"更新域描述符，客户端数量: {len(client_descriptors)}")
        self.domain_descriptors = client_descriptors

        # 分析域描述符变化
        for client_id, descriptor in client_descriptors.items():
            if 'round' in descriptor:
                logger.info(f"客户端 {client_id} 第 {descriptor['round']} 轮域描述符已更新")

    def _create_dynamic_prompt(self, domain: str, rating: int, client_descriptors: Optional[Dict] = None) -> str:
        """基于动态域描述符创建提示"""
        if not self.use_dynamic_descriptors or not client_descriptors:
            # 回退到静态提示
            return self._create_generation_prompt(domain, rating)

        # 寻找最相关的客户端描述符
        best_descriptor = None
        best_similarity = -1.0

        for client_id, descriptor in client_descriptors.items():
            # 简单的域匹配策略 (可以改进)
            if 'domain_name' in descriptor and descriptor['domain_name'] == domain:
                best_descriptor = descriptor
                break
            elif best_descriptor is None:
                best_descriptor = descriptor

        if best_descriptor is None:
            return self._create_generation_prompt(domain, rating)

        # 基于域描述符特征调整提示
        center_norm = torch.norm(best_descriptor['center']).item() if 'center' in best_descriptor else 1.0
        variance_mean = torch.mean(best_descriptor['variance']).item() if 'variance' in best_descriptor else 0.5
        density = best_descriptor['density'].item() if 'density' in best_descriptor else 1.0

        # 动态调整提示风格
        if density > 1.5:  # 高密度域
            style = "focused and specific"
        elif variance_mean > 0.6:  # 高方差域
            style = "diverse and varied"
        else:
            style = "balanced"

        if center_norm > 1.2:  # 强特征域
            intensity = "distinctive"
        else:
            intensity = "typical"

        # 评分描述
        rating_descriptions = {
            0: "terrible and disappointing",
            1: "poor quality and unsatisfactory",
            2: "average and okay",
            3: "good quality and recommended",
            4: "excellent and outstanding"
        }

        rating_desc = rating_descriptions[rating]

        # 生成动态提示
        dynamic_prompt = f"Generate a {style} {domain.lower()} review that is {rating_desc} with {intensity} characteristics:"

        return dynamic_prompt

    def _is_quality_text(self, text: str) -> bool:
        """检查生成文本的质量"""
        if not text or len(text.strip()) == 0:
            return False

        # 检查最小长度
        words = text.split()
        if len(words) < 5:
            return False

        # 检查是否包含过多重复词汇
        unique_words = set(words)
        if len(unique_words) < len(words) * 0.6:  # 至少60%的词汇是唯一的
            return False

        # 检查是否包含非英文字符（除了基本标点）
        import re
        if re.search(r'[^\w\s.,!?\'"-]', text):
            return False

        # 检查是否过度重复某个词
        word_counts = {}
        for word in words:
            word_lower = word.lower()
            word_counts[word_lower] = word_counts.get(word_lower, 0) + 1

        # 如果任何词出现超过总词数的30%，认为质量不佳
        max_word_freq = max(word_counts.values()) if word_counts else 0
        if max_word_freq > len(words) * 0.3:
            return False

        return True

    def fine_tune_generator(self, training_data: List[Dict[str, any]],
                           num_epochs: int = 3):
        """Fine-tune the T5 generator on domain-specific data"""
        logger.info("Fine-tuning T5 generator on domain data...")

        # Prepare training data
        prompts = []
        targets = []

        for sample in training_data:
            # Create prompt from domain and rating
            domain = sample['domain']
            rating = sample['label']
            prompt = self._create_generation_prompt(domain, rating)

            prompts.append(prompt)
            targets.append(sample['text'])

        # Tokenize
        prompt_encodings = self.tokenizer(
            prompts,
            truncation=True,
            padding=True,
            max_length=512,
            return_tensors='pt'
        )

        target_encodings = self.tokenizer(
            targets,
            truncation=True,
            padding=True,
            max_length=self.config.model.generator_max_length,
            return_tensors='pt'
        )

        # Training setup
        optimizer = torch.optim.AdamW(self.model.parameters(), lr=1e-4)
        self.model.train()

        for epoch in range(num_epochs):
            total_loss = 0

            # Simple batch processing (in practice, use DataLoader)
            batch_size = 8
            for i in range(0, len(prompts), batch_size):
                batch_prompts = prompt_encodings['input_ids'][i:i+batch_size].to(self.device)
                batch_targets = target_encodings['input_ids'][i:i+batch_size].to(self.device)

                # Forward pass
                outputs = self.model(
                    input_ids=batch_prompts,
                    labels=batch_targets
                )

                loss = outputs.loss
                total_loss += loss.item()

                # Backward pass
                optimizer.zero_grad()
                loss.backward()
                optimizer.step()

            avg_loss = total_loss / (len(prompts) // batch_size)
            logger.info(f"Epoch {epoch+1}/{num_epochs}, Average Loss: {avg_loss:.4f}")

        self.model.eval()
        logger.info("Generator fine-tuning completed")

    def save_generator(self, save_path: str):
        """Save the fine-tuned generator"""
        self.model.save_pretrained(save_path)
        self.tokenizer.save_pretrained(save_path)
        logger.info(f"Generator saved to {save_path}")

    def load_generator(self, load_path: str):
        """Load a fine-tuned generator"""
        self.model = T5ForConditionalGeneration.from_pretrained(load_path).to(self.device)
        self.tokenizer = T5Tokenizer.from_pretrained(load_path)
        logger.info(f"Generator loaded from {load_path}")
