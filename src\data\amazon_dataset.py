"""
Amazon Review Dataset Processing for D³AFD
"""
import os
import json
import random
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
from collections import defaultdict, Counter
import torch
from torch.utils.data import Dataset, DataLoader
from transformers import AutoTokenizer
from datasets import load_dataset, Dataset as HFDataset
import logging

logger = logging.getLogger(__name__)

class AmazonReviewDataset(Dataset):
    """Amazon Review Dataset for sentiment analysis"""

    def __init__(self, texts: List[str] = None, labels: List[int] = None, domains: List[str] = None,
                 tokenizer=None, max_length: int = 512, config=None):
        # Support both direct initialization and config-based initialization
        if config is not None:
            self.config = config
            self.tokenizer = AutoTokenizer.from_pretrained(config.model.model_name)
            self.max_length = getattr(config.data, 'max_length', 512)
            # Initialize with empty data, will be loaded later
            self.texts = []
            self.labels = []
            self.domains = []
            # 立即加载数据
            self._load_data()
        else:
            self.texts = texts or []
            self.labels = labels or []
            self.domains = domains or []
            self.tokenizer = tokenizer
            self.max_length = max_length
            self.config = None

        if self.texts and self.labels and self.domains:
            assert len(self.texts) == len(self.labels) == len(self.domains)
        
        logger.info(f"AmazonReviewDataset initialized with {len(self.texts)} samples")

    def _load_data(self):
        """Load Amazon Reviews data from config"""
        if self.config is None:
            return

        logger.info("Loading Amazon Reviews dataset...")

        # Create sample data for testing (replace with actual data loading)
        domains = getattr(self.config.data, 'domains', ['Electronics', 'Books'])
        samples_per_domain = 100  # Small sample for testing

        for domain in domains:
            for i in range(samples_per_domain):
                # Generate sample reviews for testing
                if domain == 'Electronics':
                    sample_texts = [
                        "This phone is amazing! Great battery life and camera quality.",
                        "The laptop is okay, but could be faster for the price.",
                        "Terrible headphones, very poor sound quality."
                    ]
                elif domain == 'Books':
                    sample_texts = [
                        "Excellent book! Couldn't put it down, highly recommended.",
                        "The story was decent but the ending was disappointing.",
                        "Boring book, waste of time and money."
                    ]
                else:
                    sample_texts = [
                        "Great product, exactly as described!",
                        "Average quality, nothing special.",
                        "Poor quality, would not recommend."
                    ]

                # Cycle through sample texts and assign labels
                text = sample_texts[i % len(sample_texts)]
                label = i % 3  # 0: negative, 1: neutral, 2: positive

                self.texts.append(text)
                self.labels.append(label)
                self.domains.append(domain)

        logger.info(f"Loaded {len(self.texts)} samples across {len(domains)} domains")

    def __len__(self):
        return len(self.texts)

    def __getitem__(self, idx):
        text = str(self.texts[idx])
        label = self.labels[idx]
        domain = self.domains[idx]

        # Tokenize text
        encoding = self.tokenizer(
            text,
            truncation=True,
            padding='max_length',
            max_length=self.max_length,
            return_tensors='pt'
        )

        return {
            'input_ids': encoding['input_ids'].flatten(),
            'attention_mask': encoding['attention_mask'].flatten(),
            'labels': torch.tensor(label, dtype=torch.long),
            'domain': domain,
            'text': text
        }

    def get_domain_data(self, domain: str) -> List[Dict]:
        """
        Get data for a specific domain

        Args:
            domain: Domain name

        Returns:
            List of data samples for the domain
        """
        logger.info(f"Getting domain data for '{domain}'")
        logger.info(f"Total samples in dataset: {len(self.texts)}")
        logger.info(f"Available domains: {list(set(self.domains))}")
        
        domain_data = []
        for i, d in enumerate(self.domains):
            if d == domain:
                domain_data.append({
                    'text': self.texts[i],
                    'label': self.labels[i],
                    'domain': d
                })

        logger.info(f"Retrieved {len(domain_data)} samples for domain {domain}")
        return domain_data

    def get_test_data(self) -> List[Dict]:
        """
        Get test data (for now, return a subset of all data)

        Returns:
            List of test data samples
        """
        # For testing, return last 20% of data as test set
        test_size = max(1, len(self.texts) // 5)
        test_indices = list(range(len(self.texts) - test_size, len(self.texts)))

        test_data = []
        for i in test_indices:
            test_data.append({
                'text': self.texts[i],
                'label': self.labels[i],
                'domain': self.domains[i]
            })

        logger.info(f"Retrieved {len(test_data)} test samples")
        return test_data

    def get_train_data(self) -> List[Dict]:
        """
        Get training data

        Returns:
            List of training data samples
        """
        # For testing, return first 80% of data as training set
        train_size = len(self.texts) - max(1, len(self.texts) // 5)

        train_data = []
        for i in range(train_size):
            train_data.append({
                'text': self.texts[i],
                'label': self.labels[i],
                'domain': self.domains[i]
            })

        logger.info(f"Retrieved {len(train_data)} training samples")
        return train_data

class AmazonDataProcessor:
    """Process Amazon Review data for federated learning"""
    
    def __init__(self, config):
        self.config = config
        self.tokenizer = AutoTokenizer.from_pretrained(config.model.teacher_model)
        self.domain_to_id = {domain: i for i, domain in enumerate(config.data.domains)}
        self.id_to_domain = {i: domain for domain, i in self.domain_to_id.items()}
        
    def load_amazon_reviews(self) -> pd.DataFrame:
        """Load Amazon Reviews dataset"""
        logger.info("Loading Amazon Reviews dataset...")
        
        try:
            # Try to load from local cache first
            cache_path = os.path.join(self.config.experiment.data_dir, "amazon_reviews.csv")
            if os.path.exists(cache_path):
                logger.info(f"Loading from cache: {cache_path}")
                return pd.read_csv(cache_path)
            
            # Load from HuggingFace datasets
            # Using a subset for demonstration - in practice you'd use the full dataset
            dataset = load_dataset("amazon_reviews_multi", "en", split="train[:50000]")
            
            # Convert to pandas DataFrame
            df = pd.DataFrame(dataset)
            
            # Map product categories to our domains
            category_mapping = self._create_category_mapping()
            df['domain'] = df['product_category'].map(category_mapping)
            
            # Filter out unmapped categories
            df = df.dropna(subset=['domain'])
            
            # Convert star rating to 0-4 (originally 1-5)
            df['label'] = df['stars'] - 1
            
            # Select relevant columns
            df = df[['review_body', 'label', 'domain', 'product_category']]
            df = df.rename(columns={'review_body': 'text'})
            
            # Save to cache
            os.makedirs(self.config.experiment.data_dir, exist_ok=True)
            df.to_csv(cache_path, index=False)
            logger.info(f"Saved to cache: {cache_path}")
            
            return df
            
        except Exception as e:
            logger.error(f"Error loading dataset: {e}")
            # Fallback: create synthetic data for testing
            return self._create_synthetic_data()
    
    def _create_category_mapping(self) -> Dict[str, str]:
        """Create mapping from product categories to domains"""
        # This is a simplified mapping - in practice you'd have more comprehensive mapping
        mapping = {
            'book': 'Books',
            'electronics': 'Electronics', 
            'home': 'Home_and_Kitchen',
            'sports': 'Sports_and_Outdoors',
            'toy': 'Toys_and_Games',
            'apparel': 'Clothing_Shoes_and_Jewelry',
            'health': 'Health_and_Personal_Care',
            'automotive': 'Automotive',
            'tools': 'Tools_and_Home_Improvement',
            'beauty': 'Beauty'
        }
        return mapping
    
    def _create_synthetic_data(self) -> pd.DataFrame:
        """Create synthetic Amazon review data for testing"""
        logger.warning("Creating synthetic data for testing...")
        
        synthetic_reviews = {
            'Books': [
                "This book was absolutely amazing! Great plot and characters.",
                "Couldn't put it down. Highly recommend this novel.",
                "The writing style was okay but the story was boring.",
                "Not my favorite book. Too slow paced for my taste.",
                "Terrible book. Waste of money and time."
            ],
            'Electronics': [
                "Great phone! Battery life is excellent and camera quality is superb.",
                "Good value for money. Works as expected.",
                "Average product. Nothing special but does the job.",
                "Poor quality. Stopped working after a week.",
                "Worst purchase ever. Complete waste of money."
            ],
            'Home_and_Kitchen': [
                "Love this kitchen appliance! Makes cooking so much easier.",
                "Good quality and works well. Recommended.",
                "It's okay. Does what it's supposed to do.",
                "Not very durable. Broke after a few months.",
                "Terrible quality. Don't buy this product."
            ]
        }
        
        data = []
        for domain, reviews in synthetic_reviews.items():
            for i, review in enumerate(reviews):
                # Create multiple copies with slight variations
                for _ in range(200):  # 200 samples per review template
                    data.append({
                        'text': review,
                        'label': i,  # 0-4 rating
                        'domain': domain,
                        'product_category': domain.lower()
                    })
        
        return pd.DataFrame(data)

    def create_federated_split(self, df: pd.DataFrame) -> Dict[int, Dict[str, List]]:
        """Create federated data split with non-IID distribution"""
        logger.info("Creating federated data split...")

        # Group data by domain
        domain_data = defaultdict(list)
        for _, row in df.iterrows():
            domain_data[row['domain']].append({
                'text': row['text'],
                'label': row['label'],
                'domain': row['domain']
            })

        # Create client assignments
        clients_data = defaultdict(lambda: {'texts': [], 'labels': [], 'domains': []})

        # Strategy 1: Each client gets 1-2 domains (non-IID)
        domains = list(domain_data.keys())
        random.shuffle(domains)

        for client_id in range(self.config.data.num_clients):
            # Assign 1-2 domains per client
            if client_id < len(domains):
                # First round: one domain per client
                assigned_domains = [domains[client_id]]
            else:
                # Second round: mix domains
                primary_domain = domains[client_id % len(domains)]
                secondary_domain = domains[(client_id + 1) % len(domains)]
                assigned_domains = [primary_domain, secondary_domain]

            # Sample data from assigned domains
            total_samples = 0
            for domain in assigned_domains:
                domain_samples = domain_data[domain]
                # Sample portion of domain data
                samples_from_domain = min(
                    len(domain_samples) // 2,  # Don't take all data from one domain
                    self.config.data.samples_per_client // len(assigned_domains)
                )

                sampled_data = random.sample(domain_samples, samples_from_domain)

                for sample in sampled_data:
                    clients_data[client_id]['texts'].append(sample['text'])
                    clients_data[client_id]['labels'].append(sample['label'])
                    clients_data[client_id]['domains'].append(sample['domain'])
                    total_samples += 1

            logger.info(f"Client {client_id}: {total_samples} samples from domains {assigned_domains}")

        return dict(clients_data)

    def create_dataloaders(self, clients_data: Dict[int, Dict[str, List]],
                          client_id: int, split_type: str = 'train') -> DataLoader:
        """Create DataLoader for specific client"""
        client_data = clients_data[client_id]

        # Split into train/val/test
        texts = client_data['texts']
        labels = client_data['labels']
        domains = client_data['domains']

        n_samples = len(texts)
        indices = list(range(n_samples))
        random.shuffle(indices)

        if split_type == 'train':
            # Use 70% for training
            end_idx = int(0.7 * n_samples)
            selected_indices = indices[:end_idx]
        elif split_type == 'val':
            # Use 15% for validation
            start_idx = int(0.7 * n_samples)
            end_idx = int(0.85 * n_samples)
            selected_indices = indices[start_idx:end_idx]
        else:  # test
            # Use 15% for testing
            start_idx = int(0.85 * n_samples)
            selected_indices = indices[start_idx:]

        selected_texts = [texts[i] for i in selected_indices]
        selected_labels = [labels[i] for i in selected_indices]
        selected_domains = [domains[i] for i in selected_indices]

        dataset = AmazonReviewDataset(
            texts=selected_texts,
            labels=selected_labels,
            domains=selected_domains,
            tokenizer=self.tokenizer,
            max_length=self.config.data.max_seq_length
        )

        batch_size = self.config.training.local_batch_size if split_type == 'train' else 32

        return DataLoader(
            dataset,
            batch_size=batch_size,
            shuffle=(split_type == 'train'),
            num_workers=2
        )

    def get_domain_statistics(self, clients_data: Dict[int, Dict[str, List]]) -> Dict:
        """Get statistics about domain distribution"""
        stats = {
            'total_clients': len(clients_data),
            'domain_distribution': defaultdict(int),
            'client_domain_counts': {},
            'samples_per_client': {}
        }

        for client_id, data in clients_data.items():
            client_domains = Counter(data['domains'])
            stats['client_domain_counts'][client_id] = dict(client_domains)
            stats['samples_per_client'][client_id] = len(data['texts'])

            for domain, count in client_domains.items():
                stats['domain_distribution'][domain] += count

        return stats

    def save_federated_data(self, clients_data: Dict[int, Dict[str, List]],
                           output_dir: str):
        """Save federated data split to disk"""
        os.makedirs(output_dir, exist_ok=True)

        # Save client data
        for client_id, data in clients_data.items():
            client_file = os.path.join(output_dir, f"client_{client_id}.json")
            with open(client_file, 'w') as f:
                json.dump(data, f, indent=2)

        # Save statistics
        stats = self.get_domain_statistics(clients_data)
        stats_file = os.path.join(output_dir, "federation_stats.json")
        with open(stats_file, 'w') as f:
            json.dump(stats, f, indent=2)

        logger.info(f"Federated data saved to {output_dir}")

    def load_federated_data(self, input_dir: str) -> Dict[int, Dict[str, List]]:
        """Load federated data split from disk"""
        clients_data = {}

        for client_file in os.listdir(input_dir):
            if client_file.startswith("client_") and client_file.endswith(".json"):
                client_id = int(client_file.split("_")[1].split(".")[0])

                with open(os.path.join(input_dir, client_file), 'r') as f:
                    clients_data[client_id] = json.load(f)

        logger.info(f"Loaded federated data for {len(clients_data)} clients")
        return clients_data
