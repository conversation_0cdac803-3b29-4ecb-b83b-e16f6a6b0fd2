"""
Domain Discriminator for D³AFD
Determines domain relevance weights for pseudo samples
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
from transformers import AutoModel, AutoTokenizer
from typing import List, Dict, Tuple
import logging

logger = logging.getLogger(__name__)

class DomainDiscriminator(nn.Module):
    """Domain discriminator network for determining sample-domain relevance"""
    
    def __init__(self, config, domain_name: str):
        super().__init__()
        self.config = config
        self.domain_name = domain_name
        
        # Text encoder (shared with teacher model)
        self.encoder = AutoModel.from_pretrained(config.model.teacher_model)
        
        # Domain-specific classifier layers
        self.classifier = nn.Sequential(
            nn.Linear(config.model.teacher_hidden_size, config.model.discriminator_hidden_size),
            nn.ReLU(),
            nn.Dropout(config.model.dropout_rate),
            nn.Linear(config.model.discriminator_hidden_size, config.model.discriminator_hidden_size // 2),
            nn.ReLU(),
            nn.Dropout(config.model.dropout_rate),
            nn.Linear(config.model.discriminator_hidden_size // 2, 1),  # Binary classification
            nn.Sigmoid()
        )
        
        # Freeze encoder initially (optional)
        if hasattr(config, 'freeze_discriminator_encoder') and config.freeze_discriminator_encoder:
            for param in self.encoder.parameters():
                param.requires_grad = False
    
    def forward(self, input_ids: torch.Tensor, attention_mask: torch.Tensor) -> torch.Tensor:
        """
        Forward pass
        Args:
            input_ids: Token IDs [batch_size, seq_len]
            attention_mask: Attention mask [batch_size, seq_len]
        Returns:
            Domain relevance scores [batch_size, 1]
        """
        # Encode text
        encoder_outputs = self.encoder(
            input_ids=input_ids,
            attention_mask=attention_mask
        )
        
        # Use [CLS] token representation
        # Handle both BERT (has pooler_output) and DistilBERT (no pooler_output)
        if hasattr(encoder_outputs, 'pooler_output') and encoder_outputs.pooler_output is not None:
            pooled_output = encoder_outputs.pooler_output
        else:
            # Use [CLS] token (first token) from last hidden state
            pooled_output = encoder_outputs.last_hidden_state[:, 0, :]
        
        # Classify domain relevance
        domain_score = self.classifier(pooled_output)
        
        return domain_score.squeeze(-1)  # [batch_size]

class DomainDiscriminatorTrainer:
    """Trainer for domain discriminators"""
    
    def __init__(self, config):
        self.config = config
        self.device = torch.device(config.experiment.device)
        self.tokenizer = AutoTokenizer.from_pretrained(config.model.teacher_model)
        
        # Create discriminators for each domain
        self.discriminators = {}
        for domain in config.data.domains:
            self.discriminators[domain] = DomainDiscriminator(config, domain).to(self.device)
    
    def train_discriminator(self, domain: str, positive_samples: List[str], 
                           negative_samples: List[str], num_epochs: int = 5):
        """Train a domain discriminator"""
        logger.info(f"Training domain discriminator for: {domain}")
        
        discriminator = self.discriminators[domain]
        optimizer = torch.optim.AdamW(discriminator.parameters(), lr=self.config.training.local_lr)
        criterion = nn.BCELoss()
        
        # Prepare training data
        texts = positive_samples + negative_samples
        labels = [1.0] * len(positive_samples) + [0.0] * len(negative_samples)
        
        # Create batches
        batch_size = self.config.training.local_batch_size
        
        discriminator.train()
        for epoch in range(num_epochs):
            total_loss = 0
            correct_predictions = 0
            total_predictions = 0
            
            # Shuffle data
            indices = torch.randperm(len(texts))
            
            for i in range(0, len(texts), batch_size):
                batch_indices = indices[i:i+batch_size]
                batch_texts = [texts[idx] for idx in batch_indices]
                batch_labels = torch.tensor([labels[idx] for idx in batch_indices], 
                                          dtype=torch.float).to(self.device)
                
                # Tokenize batch
                encoding = self.tokenizer(
                    batch_texts,
                    truncation=True,
                    padding=True,
                    max_length=self.config.data.max_seq_length,
                    return_tensors='pt'
                ).to(self.device)
                
                # Forward pass
                outputs = discriminator(
                    input_ids=encoding['input_ids'],
                    attention_mask=encoding['attention_mask']
                )
                
                loss = criterion(outputs, batch_labels)
                
                # Backward pass
                optimizer.zero_grad()
                loss.backward()
                optimizer.step()
                
                # Statistics
                total_loss += loss.item()
                predictions = (outputs > 0.5).float()
                correct_predictions += (predictions == batch_labels).sum().item()
                total_predictions += len(batch_labels)
            
            accuracy = correct_predictions / total_predictions
            avg_loss = total_loss / (len(texts) // batch_size + 1)
            
            logger.info(f"Domain {domain} - Epoch {epoch+1}/{num_epochs}: "
                       f"Loss: {avg_loss:.4f}, Accuracy: {accuracy:.4f}")
        
        discriminator.eval()
    
    def get_domain_weights(self, texts: List[str]) -> Dict[str, torch.Tensor]:
        """Get domain relevance weights for given texts"""
        weights = {}
        
        # Tokenize texts once
        encoding = self.tokenizer(
            texts,
            truncation=True,
            padding=True,
            max_length=self.config.data.max_seq_length,
            return_tensors='pt'
        ).to(self.device)
        
        # Get weights from each discriminator
        with torch.no_grad():
            for domain, discriminator in self.discriminators.items():
                discriminator.eval()
                domain_scores = discriminator(
                    input_ids=encoding['input_ids'],
                    attention_mask=encoding['attention_mask']
                )
                weights[domain] = domain_scores
        
        return weights
    
    def create_negative_samples(self, target_domain: str, all_domain_samples: Dict[str, List[str]], 
                               num_negative: int) -> List[str]:
        """Create negative samples for domain discriminator training"""
        negative_samples = []
        
        # Sample from other domains
        other_domains = [d for d in self.config.data.domains if d != target_domain]
        
        for domain in other_domains:
            if domain in all_domain_samples:
                domain_samples = all_domain_samples[domain]
                # Sample proportionally
                samples_from_domain = min(
                    len(domain_samples),
                    num_negative // len(other_domains)
                )
                negative_samples.extend(domain_samples[:samples_from_domain])
        
        # If we need more negative samples, add random noise or synthetic data
        while len(negative_samples) < num_negative:
            # Add some random/noisy text as negative samples
            noise_text = self._generate_noise_text()
            negative_samples.append(noise_text)
        
        return negative_samples[:num_negative]
    
    def _generate_noise_text(self) -> str:
        """Generate noise text for negative sampling"""
        import random
        import string
        
        # Generate random text
        words = ['random', 'noise', 'text', 'sample', 'data', 'test', 'example']
        noise_text = ' '.join(random.choices(words, k=random.randint(5, 20)))
        
        # Add some random characters
        noise_text += ' ' + ''.join(random.choices(string.ascii_lowercase, k=10))
        
        return noise_text
    
    def save_discriminators(self, save_dir: str):
        """Save all domain discriminators"""
        import os
        os.makedirs(save_dir, exist_ok=True)
        
        for domain, discriminator in self.discriminators.items():
            save_path = os.path.join(save_dir, f"discriminator_{domain}.pt")
            torch.save(discriminator.state_dict(), save_path)
        
        logger.info(f"Domain discriminators saved to {save_dir}")
    
    def load_discriminators(self, load_dir: str):
        """Load all domain discriminators"""
        import os
        
        for domain in self.config.data.domains:
            load_path = os.path.join(load_dir, f"discriminator_{domain}.pt")
            if os.path.exists(load_path):
                self.discriminators[domain].load_state_dict(torch.load(load_path))
                logger.info(f"Loaded discriminator for domain: {domain}")
            else:
                logger.warning(f"Discriminator not found for domain: {domain}")
    
    def evaluate_discriminators(self, test_data: Dict[str, List[str]]) -> Dict[str, float]:
        """Evaluate domain discriminators"""
        results = {}
        
        for domain, discriminator in self.discriminators.items():
            if domain not in test_data:
                continue
                
            discriminator.eval()
            
            # Positive samples (from target domain)
            positive_texts = test_data[domain][:100]  # Limit for evaluation
            
            # Negative samples (from other domains)
            negative_texts = []
            for other_domain, samples in test_data.items():
                if other_domain != domain:
                    negative_texts.extend(samples[:20])  # Sample from each other domain
            
            # Evaluate
            all_texts = positive_texts + negative_texts
            all_labels = [1.0] * len(positive_texts) + [0.0] * len(negative_texts)
            
            # Get predictions
            weights = self.get_domain_weights(all_texts)
            predictions = (weights[domain] > 0.5).float().cpu().numpy()
            
            # Calculate accuracy
            accuracy = (predictions == all_labels).mean()
            results[domain] = accuracy
            
            logger.info(f"Domain {domain} discriminator accuracy: {accuracy:.4f}")
        
        return results
