2025-08-03 10:42:05,552 - __main__ - INFO - SGD-fixed experiment logging initialized. Log file: logs/sgd_fixed_20250803_104205.log
2025-08-03 10:42:05,552 - __main__ - INFO - Starting SGD-Fixed D³AFD Experiment
2025-08-03 10:42:05,552 - __main__ - INFO - ======================================================================
2025-08-03 10:42:05,552 - __main__ - INFO - 🔧 FUNDAMENTAL FIXES APPLIED:
2025-08-03 10:42:05,552 - __main__ - INFO -    - MORE local training (5 epochs) to ensure learning
2025-08-03 10:42:05,552 - __main__ - INFO -    - Moderate global training (3 epochs) for stability
2025-08-03 10:42:05,552 - __main__ - INFO -    - Higher local LR (1e-3) to enable actual learning
2025-08-03 10:42:05,552 - __main__ - INFO -    - Moderate global LR (2e-3) for steady improvement
2025-08-03 10:42:05,552 - __main__ - INFO -    - MINIMAL regularization to allow learning
2025-08-03 10:42:05,552 - __main__ - INFO -    - Fewer but quality pseudo data (40/domain)
2025-08-03 10:42:05,552 - __main__ - INFO -    - Focus on LOCAL learning first, then knowledge transfer
2025-08-03 10:42:05,552 - __main__ - INFO -    - Relaxed early stopping to allow learning
2025-08-03 10:42:05,552 - __main__ - INFO -    - Conservative memory management
2025-08-03 10:42:06,994 - __main__ - INFO - GPU Memory Available: 8.00 GB
2025-08-03 10:42:07,122 - __main__ - INFO - Set GPU memory fraction to 60% (very conservative)
2025-08-03 10:42:07,401 - src.utils.memory_manager - INFO - GPU Memory initial: Allocated: 0.00GB, Reserved: 0.00GB, Free: 8.00GB
2025-08-03 10:42:07,401 - __main__ - INFO - SGD-Fixed Configuration:
2025-08-03 10:42:07,401 - __main__ - INFO -   🏢 Clients: 4
2025-08-03 10:42:07,401 - __main__ - INFO -   📊 Samples per client: 200
2025-08-03 10:42:07,406 - __main__ - INFO -   📏 Max sequence length: 128
2025-08-03 10:42:07,406 - __main__ - INFO -   🔄 Federated rounds: 5
2025-08-03 10:42:07,407 - __main__ - INFO -   🧪 Distillation rounds: 3
2025-08-03 10:42:07,407 - __main__ - INFO -   🎭 Pseudo samples per domain: 40
2025-08-03 10:42:07,407 - __main__ - INFO -   📦 Local batch size: 8
2025-08-03 10:42:07,407 - __main__ - INFO -   📦 Distillation batch size: 16
2025-08-03 10:42:07,407 - __main__ - INFO -   📈 Local LR: 5e-05 (FUNDAMENTAL: higher to enable learning)
2025-08-03 10:42:07,407 - __main__ - INFO -   📈 Distillation LR: 0.002 (MODERATE: steady global learning)
2025-08-03 10:42:07,407 - __main__ - INFO -   🛡️  Dropout: 0.1 (MINIMAL: allow learning)
2025-08-03 10:42:07,407 - __main__ - INFO -   🏷️  Label smoothing: 0.05 (MINIMAL: allow learning)
2025-08-03 10:42:07,407 - __main__ - INFO -   📚 Local epochs: 5 (MORE: ensure local learning)
2025-08-03 10:42:07,407 - __main__ - INFO -   🌍 Distillation epochs: 3 (MODERATE: balanced approach)
2025-08-03 10:42:07,407 - __main__ - INFO - Initializing D³AFD Framework...
2025-08-03 10:42:08,843 - huggingface_hub.file_download - WARNING - Xet Storage is enabled for this repo, but the 'hf_xet' package is not installed. Falling back to regular HTTP download. For better performance, install the package with: `pip install huggingface_hub[hf_xet]` or `pip install hf_xet`
2025-08-03 10:43:04,615 - huggingface_hub.file_download - WARNING - Xet Storage is enabled for this repo, but the 'hf_xet' package is not installed. Falling back to regular HTTP download. For better performance, install the package with: `pip install huggingface_hub[hf_xet]` or `pip install hf_xet`
2025-08-03 10:44:00,551 - src.federated.server - INFO - Federated server initialized
2025-08-03 10:44:03,094 - src.federated.d3afd_framework - INFO - D³AFD Framework initialized
2025-08-03 10:44:03,094 - src.utils.memory_manager - INFO - GPU Memory after_initialization: Allocated: 0.47GB, Reserved: 0.50GB, Free: 7.50GB
2025-08-03 10:44:03,378 - __main__ - INFO - Starting SGD-fixed training...
2025-08-03 10:44:03,378 - __main__ - INFO - 🎯 FUNDAMENTAL STRATEGY - Expected improvements:
2025-08-03 10:44:03,394 - __main__ - INFO -    - ENSURE local models learn basic knowledge first (>40% accuracy)
2025-08-03 10:44:03,394 - __main__ - INFO -    - THEN transfer meaningful knowledge to global model
2025-08-03 10:44:03,394 - __main__ - INFO -    - Target: Local accuracy >40%, Global accuracy >30%
2025-08-03 10:44:03,394 - __main__ - INFO -    - Meaningful DKD loss (>0.01, not near 0)
2025-08-03 10:44:03,394 - __main__ - INFO -    - Decreasing contrastive loss (better feature learning)
2025-08-03 10:44:03,394 - __main__ - INFO -    - Progressive improvement across federated rounds
2025-08-03 10:44:03,394 - __main__ - INFO -    - Stable memory usage without OOM
2025-08-03 10:44:03,394 - src.federated.d3afd_framework - INFO - Starting complete D³AFD training process...
2025-08-03 10:44:03,394 - src.federated.d3afd_framework - INFO - Following D³AFD four-stage design: Local Training → Pseudo Data → DKD Distillation → Personalization
2025-08-03 10:44:03,394 - src.federated.d3afd_framework - INFO - Setting up federated data distribution...
2025-08-03 10:44:03,394 - src.data.amazon_dataset - INFO - Loading Amazon Reviews dataset...
2025-08-03 10:44:05,467 - src.data.amazon_dataset - ERROR - Error loading dataset: Dataset scripts are no longer supported, but found amazon_reviews_multi.py
2025-08-03 10:44:05,467 - src.data.amazon_dataset - WARNING - Creating synthetic data for testing...
2025-08-03 10:44:05,490 - src.data.amazon_dataset - INFO - Creating federated data split...
2025-08-03 10:44:05,594 - src.data.amazon_dataset - INFO - Client 0: 200 samples from domains ['Books']
2025-08-03 10:44:05,594 - src.data.amazon_dataset - INFO - Client 1: 200 samples from domains ['Electronics']
2025-08-03 10:44:05,594 - src.data.amazon_dataset - INFO - Client 2: 200 samples from domains ['Home_and_Kitchen']
2025-08-03 10:44:05,594 - src.data.amazon_dataset - INFO - Client 3: 200 samples from domains ['Books', 'Electronics']
2025-08-03 10:44:05,603 - src.data.amazon_dataset - INFO - Federated data saved to sgd_fixed_data\federated_split
2025-08-03 10:44:05,603 - src.federated.d3afd_framework - INFO - Federated data setup complete:
2025-08-03 10:44:05,603 - src.federated.d3afd_framework - INFO -   - Total clients: 4
2025-08-03 10:44:05,603 - src.federated.d3afd_framework - INFO -   - Domain distribution: {'Books': 300, 'Electronics': 300, 'Home_and_Kitchen': 200}
2025-08-03 10:44:05,603 - src.federated.d3afd_framework - INFO - Initializing federated clients...
2025-08-03 10:44:13,574 - src.federated.client - INFO - Client 0 initialized with domains: ['Books']
2025-08-03 10:44:14,659 - src.federated.client - INFO - Client 0: Models initialized
2025-08-03 10:44:14,659 - src.federated.server - INFO - Registered client 0 with domains: ['Books']
2025-08-03 10:44:14,659 - src.federated.d3afd_framework - INFO - Client 0 initialized with 200 samples
2025-08-03 10:44:22,210 - src.federated.client - INFO - Client 1 initialized with domains: ['Electronics']
2025-08-03 10:44:23,295 - src.federated.client - INFO - Client 1: Models initialized
2025-08-03 10:44:23,301 - src.federated.server - INFO - Registered client 1 with domains: ['Electronics']
2025-08-03 10:44:23,301 - src.federated.d3afd_framework - INFO - Client 1 initialized with 200 samples
2025-08-03 10:44:30,960 - src.federated.client - INFO - Client 2 initialized with domains: ['Home_and_Kitchen']
2025-08-03 10:44:32,003 - src.federated.client - INFO - Client 2: Models initialized
2025-08-03 10:44:32,003 - src.federated.server - INFO - Registered client 2 with domains: ['Home_and_Kitchen']
2025-08-03 10:44:32,003 - src.federated.d3afd_framework - INFO - Client 2 initialized with 200 samples
2025-08-03 10:44:39,699 - src.federated.client - INFO - Client 3 initialized with domains: ['Electronics', 'Books']
2025-08-03 10:44:40,735 - src.federated.client - INFO - Client 3: Models initialized
2025-08-03 10:44:40,735 - src.federated.server - INFO - Registered client 3 with domains: ['Electronics', 'Books']
2025-08-03 10:44:40,735 - src.federated.d3afd_framework - INFO - Client 3 initialized with 200 samples
2025-08-03 10:44:40,735 - src.federated.d3afd_framework - INFO - Initialized 4 clients
2025-08-03 10:44:40,735 - src.federated.d3afd_framework - INFO - === Initial Setup Phase ===
2025-08-03 10:44:40,735 - src.federated.d3afd_framework - INFO - Training initial local teacher models...
2025-08-03 10:44:40,735 - src.federated.client - INFO - Client 0: Training local teacher for 5 epochs
2025-08-03 10:44:55,154 - src.federated.client - INFO - Client 0 - Epoch 1/5: Loss: 1.6781, Accuracy: 0.1571
2025-08-03 10:45:05,392 - src.federated.client - INFO - Client 0 - Epoch 2/5: Loss: 1.6548, Accuracy: 0.1714
2025-08-03 10:45:14,885 - src.federated.client - INFO - Client 0 - Epoch 3/5: Loss: 1.6104, Accuracy: 0.1929
2025-08-03 10:45:23,897 - src.federated.client - INFO - Client 0 - Epoch 4/5: Loss: 1.6329, Accuracy: 0.1857
2025-08-03 10:45:32,887 - src.federated.client - INFO - Client 0 - Epoch 5/5: Loss: 1.6235, Accuracy: 0.2071
2025-08-03 10:45:32,974 - src.federated.client - INFO - Client 0: Local teacher training completed. Final accuracy: 0.1829
2025-08-03 10:45:32,974 - src.federated.d3afd_framework - INFO - Client 0 teacher training: Accuracy 0.1829
2025-08-03 10:45:32,974 - src.federated.client - INFO - Client 1: Training local teacher for 5 epochs
2025-08-03 10:45:42,094 - src.federated.client - INFO - Client 1 - Epoch 1/5: Loss: 1.6849, Accuracy: 0.1857
