"""
Fast training D³AFD experiment runner
Optimized for speed while maintaining algorithm completeness
"""
import os
import sys
import logging
import torch
import gc
from datetime import datetime

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.config import get_default_config
from src.federated.d3afd_framework import D3AFDFramework
from src.utils.memory_manager import get_memory_manager

def setup_fast_training_config():
    """Create fast training configuration"""
    config = get_default_config()
    
    # Use efficient models
    config.model.teacher_model = "distilbert-base-uncased"
    config.model.student_model = "distilbert-base-uncased"
    config.model.generator_model = "t5-small"
    config.model.generator_max_length = 80  # Shorter generation for speed
    
    # Optimized data configuration for speed
    config.data.num_clients = 5  # Fewer clients for speed
    config.data.samples_per_client = 200  # Fewer samples for speed
    config.data.max_seq_length = 128  # Shorter sequences for speed
    
    # Fast training configuration
    config.training.federated_rounds = 5  # Fewer rounds for speed
    config.training.distillation_rounds = 3  # Fewer distillation rounds
    config.training.local_epochs = 2  # Fewer local epochs
    config.training.distillation_epochs = 2  # Fewer distillation epochs
    config.training.personalization_epochs = 2  # Fewer personalization epochs
    
    # Larger batch sizes for efficiency
    config.training.local_batch_size = 8  # Larger batches for speed
    config.training.distillation_batch_size = 16  # Larger batches for speed
    
    # Optimized learning rates for SGD (higher than Adam)
    config.training.local_lr = 1e-3  # Higher LR for SGD
    config.training.distillation_lr = 5e-4  # Higher LR for SGD
    config.training.personalization_lr = 2e-3  # Higher LR for SGD

    # Optimizer configuration
    config.training.optimizer_type = "sgd"  # Use SGD for speed and efficiency
    config.training.momentum = 0.9  # SGD momentum
    config.training.nesterov = True  # Nesterov momentum for better convergence

    # Regularization
    config.training.weight_decay = 1e-4  # Lower weight decay for SGD
    config.training.dropout_rate = 0.1
    
    # Balanced loss weights
    config.training.contrastive_weight = 0.3
    config.training.dkd_alpha = 0.5
    config.training.dkd_beta = 0.5
    
    # Reduced pseudo data for speed
    config.training.pseudo_samples_per_domain = 50  # Much fewer samples
    config.training.mixed_domain_samples = 20  # Much fewer samples
    
    # Output settings
    config.experiment.output_dir = "fast_training_outputs"
    config.experiment.data_dir = "fast_training_data"
    
    return config

def setup_speed_optimizations():
    """Setup environment optimizations for speed"""
    # Enable optimizations
    torch.backends.cudnn.benchmark = True  # Enable for consistent input sizes
    torch.backends.cudnn.deterministic = False  # Disable for speed
    
    # Set environment variables for speed
    os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'expandable_segments:True,max_split_size_mb:128'
    os.environ['TOKENIZERS_PARALLELISM'] = 'true'  # Enable for speed
    os.environ['OMP_NUM_THREADS'] = '4'  # Optimize CPU usage
    
    # Enable optimizations if available
    try:
        torch.backends.cuda.enable_flash_sdp(True)
        torch.backends.cuda.enable_mem_efficient_sdp(True)
        torch.backends.cuda.enable_math_sdp(True)
    except:
        pass

def setup_logging():
    """Setup logging"""
    os.makedirs('logs', exist_ok=True)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_filename = f"logs/fast_training_{timestamp}.log"
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_filename, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    logger = logging.getLogger(__name__)
    logger.info(f"Fast training experiment logging initialized. Log file: {log_filename}")
    return logger

def main():
    """Main function for fast training experiment"""
    logger = setup_logging()
    
    logger.info("Starting Fast Training D³AFD Experiment")
    logger.info("=" * 70)
    logger.info("🚀 GOAL: Complete training quickly while maintaining algorithm integrity")
    logger.info("⚡ Speed optimizations:")
    logger.info("   - Reduced rounds and epochs")
    logger.info("   - Smaller pseudo data generation")
    logger.info("   - Larger batch sizes for efficiency")
    logger.info("   - Shorter sequences and generation")
    logger.info("   - Pseudo data caching enabled")
    logger.info("   - Batch processing optimizations")
    
    # Setup speed optimizations
    setup_speed_optimizations()
    
    # Check CUDA availability
    if not torch.cuda.is_available():
        logger.warning("⚠️  CUDA not available, using CPU (will be slower)")
    else:
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        logger.info(f"GPU Memory Available: {gpu_memory:.2f} GB")
        
        # Set memory fraction for speed
        torch.cuda.set_per_process_memory_fraction(0.8)
    
    # Initialize memory manager
    memory_manager = get_memory_manager()
    memory_manager.log_memory_usage("initial")
    
    # Get fast training config
    config = setup_fast_training_config()
    
    logger.info("Fast Training Configuration:")
    logger.info(f"  🏢 Clients: {config.data.num_clients}")
    logger.info(f"  📊 Samples per client: {config.data.samples_per_client}")
    logger.info(f"  📏 Max sequence length: {config.data.max_seq_length}")
    logger.info(f"  🤖 Teacher model: {config.model.teacher_model}")
    logger.info(f"  📝 Generator model: {config.model.generator_model}")
    logger.info(f"  🔄 Federated rounds: {config.training.federated_rounds}")
    logger.info(f"  🧪 Distillation rounds: {config.training.distillation_rounds}")
    logger.info(f"  🎭 Pseudo samples per domain: {config.training.pseudo_samples_per_domain}")
    logger.info(f"  📦 Local batch size: {config.training.local_batch_size}")
    logger.info(f"  📦 Distillation batch size: {config.training.distillation_batch_size}")
    
    # Estimate training time
    estimated_time = (
        config.training.federated_rounds * 
        config.training.distillation_rounds * 
        (config.training.pseudo_samples_per_domain * len(config.data.domains) * 0.1 + 5)  # Rough estimate
    ) / 60  # Convert to minutes
    
    logger.info(f"⏱️  Estimated training time: {estimated_time:.1f} minutes")
    
    try:
        # Initialize framework
        logger.info("Initializing D³AFD Framework...")
        start_time = datetime.now()
        
        framework = D3AFDFramework(config)
        
        # Memory check after initialization
        memory_manager.log_memory_usage("after_initialization")
        
        # Run training
        logger.info("Starting fast training...")
        logger.info("💡 Speed strategy:")
        logger.info("   - Cache pseudo data every 3 rounds")
        logger.info("   - Batch process generation and predictions")
        logger.info("   - Larger batch sizes for GPU efficiency")
        logger.info("   - Aggressive memory management")
        
        results = framework.run_complete_training(force_reload_data=False)
        
        # Calculate actual training time
        end_time = datetime.now()
        actual_time = (end_time - start_time).total_seconds() / 60
        
        # Analyze results
        logger.info("Training completed successfully!")
        logger.info("=" * 70)
        logger.info("📊 FINAL RESULTS:")
        logger.info(f"⏱️  Actual training time: {actual_time:.1f} minutes")
        logger.info(f"🎯 Speed improvement: {estimated_time/actual_time:.1f}x faster than estimated")
        
        if 'global_model' in results:
            global_acc = results['global_model'].get('accuracy', 0)
            global_f1 = results['global_model'].get('f1_macro', 0)
            logger.info(f"🌍 Global Model Performance:")
            logger.info(f"   Accuracy: {global_acc:.4f} ({global_acc*100:.2f}%)")
            logger.info(f"   F1-Macro: {global_f1:.4f}")
            
            # Performance analysis
            if global_acc >= 0.4:
                logger.info("   🎉 EXCELLENT! Good accuracy achieved quickly!")
            elif global_acc >= 0.3:
                logger.info("   ✅ SUCCESS! Reasonable accuracy with fast training!")
            elif global_acc >= 0.2:
                logger.info("   📈 PROGRESS! Training completed efficiently!")
            else:
                logger.info("   ⚡ FAST! Training completed without errors!")
        
        if 'personalized_models' in results:
            personal_accs = [r.get('accuracy', 0) for r in results['personalized_models'].values()]
            if personal_accs:
                avg_personal_acc = sum(personal_accs) / len(personal_accs)
                max_personal_acc = max(personal_accs)
                
                logger.info(f"👤 Personalized Models Performance:")
                logger.info(f"   Average: {avg_personal_acc:.4f} ({avg_personal_acc*100:.2f}%)")
                logger.info(f"   Best: {max_personal_acc:.4f} ({max_personal_acc*100:.2f}%)")
        
        # Memory usage summary
        memory_summary = memory_manager.get_memory_summary()
        logger.info(f"💾 {memory_summary}")
        
        logger.info("=" * 70)
        logger.info(f"📁 Results saved to: {config.experiment.output_dir}")
        logger.info(f"⚡ Training completed in {actual_time:.1f} minutes!")
        
        return 0
        
    except Exception as e:
        logger.error(f"Fast training experiment failed: {e}")
        logger.error("Full traceback:")
        import traceback
        logger.error(traceback.format_exc())
        return 1
    
    finally:
        # Final cleanup
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        gc.collect()
        if 'memory_manager' in locals():
            memory_manager.log_memory_usage("final")
        logger.info("Fast training experiment completed.")

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
