model:
  base_learning_rate: 4.5e-06
  target: ldm.models.autoencoder.AutoencoderKL
  params:
    monitor: val/rec_loss
    embed_dim: 3
    lossconfig:
      target: ldm.modules.losses.LPIPSWithDiscriminator
      params:
        disc_start: 50001
        kl_weight: 1.0e-06
        disc_weight: 0.5
    ddconfig:
      double_z: true
      z_channels: 3
      resolution: 256
      in_channels: 3
      out_ch: 3
      ch: 128
      ch_mult:
      - 1
      - 2
      - 4
      num_res_blocks: 2
      attn_resolutions: []
      dropout: 0.0
data:
  target: main.DataModuleFromConfig
  params:
    batch_size: 10
    wrap: true
    train:
      target: ldm.data.openimages.FullOpenImagesTrain
      params:
        size: 384
        crop_size: 256
    validation:
      target: ldm.data.openimages.FullOpenImagesValidation
      params:
        size: 384
        crop_size: 256
