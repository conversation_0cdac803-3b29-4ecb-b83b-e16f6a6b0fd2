"""
Decoupled Knowledge Distillation (DKD) for D³AFD
Separates target class and non-target class distillation
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Tuple
import logging

logger = logging.getLogger(__name__)

class DKDLoss(nn.Module):
    """Decoupled Knowledge Distillation Loss"""
    
    def __init__(self, alpha: float = 0.7, beta: float = 0.3, temperature: float = 3.0):
        """
        Args:
            alpha: Weight for target class distillation (TCKD) - increased for better target knowledge
            beta: Weight for non-target class distillation (NCKD) - decreased to focus on targets
            temperature: Temperature for softmax - lowered for sharper distributions and better gradient signals
        """
        super().__init__()
        self.alpha = alpha
        self.beta = beta
        self.temperature = temperature
        
        assert abs(alpha + beta - 1.0) < 1e-6, "Alpha and beta should sum to 1.0"
    
    def forward(self, student_logits: torch.Tensor, teacher_logits: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        Compute DKD loss
        Args:
            student_logits: Student model logits [batch_size, num_classes]
            teacher_logits: Teacher model logits [batch_size, num_classes]
        Returns:
            Dictionary containing TCKD, NCKD, and total DKD losses
        """
        # Validate input shapes
        if student_logits.shape != teacher_logits.shape:
            logger.error(f"Shape mismatch: student {student_logits.shape} vs teacher {teacher_logits.shape}")
            return {
                'tckd_loss': torch.tensor(0.0, device=student_logits.device, requires_grad=True),
                'nckd_loss': torch.tensor(0.0, device=student_logits.device, requires_grad=True),
                'dkd_loss': torch.tensor(0.0, device=student_logits.device, requires_grad=True)
            }

        # Apply temperature scaling - reduced temperature for better gradient signals
        # Minimal epsilon to preserve meaningful probability differences
        epsilon = 1e-8
        student_probs = F.softmax(student_logits / self.temperature, dim=1)
        teacher_probs = F.softmax(teacher_logits / self.temperature, dim=1)

        # Minimal smoothing to preserve teacher knowledge while preventing overconfidence
        teacher_probs = teacher_probs * (1 - epsilon) + epsilon / teacher_probs.size(1)

        # Get target class (highest probability in teacher)
        target_class = torch.argmax(teacher_probs, dim=1)  # [batch_size]

        # Target Class Knowledge Distillation (TCKD)
        tckd_loss = self._compute_tckd_loss(student_probs, teacher_probs, target_class)

        # Non-Target Class Knowledge Distillation (NCKD)
        nckd_loss = self._compute_nckd_loss(student_probs, teacher_probs, target_class)

        # Total DKD loss
        total_loss = self.alpha * tckd_loss + self.beta * nckd_loss

        return {
            'tckd_loss': tckd_loss,
            'nckd_loss': nckd_loss,
            'dkd_loss': total_loss
        }
    
    def _compute_tckd_loss(self, student_probs: torch.Tensor, teacher_probs: torch.Tensor,
                          target_class: torch.Tensor) -> torch.Tensor:
        """Compute Target Class Knowledge Distillation loss"""
        batch_size = student_probs.size(0)

        # Extract target class probabilities
        student_target = student_probs.gather(1, target_class.unsqueeze(1)).squeeze(1)
        teacher_target = teacher_probs.gather(1, target_class.unsqueeze(1)).squeeze(1)

        # Clamp probabilities to avoid extreme values
        student_target = torch.clamp(student_target, min=1e-8, max=1-1e-8)
        teacher_target = torch.clamp(teacher_target, min=1e-8, max=1-1e-8)

        # KL divergence for target class (treating as binary classification)
        student_target_dist = torch.stack([1 - student_target, student_target], dim=1)
        teacher_target_dist = torch.stack([1 - teacher_target, teacher_target], dim=1)

        # Normalize distributions to ensure they sum to 1
        student_target_dist = student_target_dist / student_target_dist.sum(dim=1, keepdim=True)
        teacher_target_dist = teacher_target_dist / teacher_target_dist.sum(dim=1, keepdim=True)

        # Add small epsilon to avoid log(0) and ensure numerical stability
        eps = 1e-8
        teacher_target_dist = torch.clamp(teacher_target_dist, min=eps, max=1-eps)
        student_target_dist = torch.clamp(student_target_dist, min=eps, max=1-eps)

        tckd_loss = F.kl_div(
            torch.log(student_target_dist),
            teacher_target_dist,
            reduction='batchmean'
        )

        # Check for NaN and return 0 if found
        if torch.isnan(tckd_loss):
            logger.warning("NaN detected in TCKD loss, returning 0")
            return torch.tensor(0.0, device=student_probs.device, requires_grad=True)

        return tckd_loss
    
    def _compute_nckd_loss(self, student_probs: torch.Tensor, teacher_probs: torch.Tensor,
                          target_class: torch.Tensor) -> torch.Tensor:
        """Compute Non-Target Class Knowledge Distillation loss"""
        batch_size, num_classes = student_probs.shape

        # Create mask for non-target classes
        mask = torch.ones_like(student_probs, dtype=torch.bool)
        mask.scatter_(1, target_class.unsqueeze(1), False)

        # Extract non-target class probabilities using advanced indexing
        # This is safer than view() as it handles shape mismatches
        student_non_target_flat = student_probs[mask]
        teacher_non_target_flat = teacher_probs[mask]

        # Calculate expected size and verify
        expected_size = batch_size * (num_classes - 1)
        actual_size = student_non_target_flat.numel()

        if actual_size != expected_size:
            logger.warning(f"Size mismatch in NCKD: expected {expected_size}, got {actual_size}")
            # Return zero loss for safety
            return torch.tensor(0.0, device=student_probs.device, requires_grad=True)

        # Safely reshape
        try:
            student_non_target = student_non_target_flat.view(batch_size, num_classes - 1)
            teacher_non_target = teacher_non_target_flat.view(batch_size, num_classes - 1)
        except RuntimeError as e:
            logger.warning(f"Failed to reshape tensors in NCKD: {e}")
            return torch.tensor(0.0, device=student_probs.device, requires_grad=True)

        # Check for zero sums before normalization
        student_sums = student_non_target.sum(dim=1, keepdim=True)
        teacher_sums = teacher_non_target.sum(dim=1, keepdim=True)

        # Avoid division by zero
        eps = 1e-8
        student_sums = torch.clamp(student_sums, min=eps)
        teacher_sums = torch.clamp(teacher_sums, min=eps)

        # Renormalize non-target probabilities safely
        student_non_target = student_non_target / student_sums
        teacher_non_target = teacher_non_target / teacher_sums

        # Clamp to avoid extreme values
        student_non_target = torch.clamp(student_non_target, min=eps, max=1-eps)
        teacher_non_target = torch.clamp(teacher_non_target, min=eps, max=1-eps)

        nckd_loss = F.kl_div(
            torch.log(student_non_target),
            teacher_non_target,
            reduction='batchmean'
        )

        # Check for NaN and return 0 if found
        if torch.isnan(nckd_loss):
            logger.warning("NaN detected in NCKD loss, returning 0")
            return torch.tensor(0.0, device=student_probs.device, requires_grad=True)

        return nckd_loss

class WeightedEnsembleTeacher:
    """Weighted ensemble of multiple teacher models"""
    
    def __init__(self, config):
        self.config = config
        self.device = torch.device(config.experiment.device)
    
    def compute_ensemble_predictions(self, teacher_predictions: Dict[str, torch.Tensor],
                                   domain_weights: Dict[str, torch.Tensor]) -> torch.Tensor:
        """
        Compute weighted ensemble predictions from multiple teachers
        Args:
            teacher_predictions: Dict mapping domain -> teacher logits [batch_size, num_classes]
            domain_weights: Dict mapping domain -> relevance weights [batch_size]
        Returns:
            Ensemble teacher logits [batch_size, num_classes]
        """
        batch_size = next(iter(teacher_predictions.values())).size(0)
        num_classes = next(iter(teacher_predictions.values())).size(1)
        
        # Initialize ensemble logits
        ensemble_logits = torch.zeros(batch_size, num_classes).to(self.device)
        total_weights = torch.zeros(batch_size).to(self.device)
        
        # Weighted combination
        for domain, logits in teacher_predictions.items():
            if domain in domain_weights:
                weights = domain_weights[domain].unsqueeze(1)  # [batch_size, 1]
                ensemble_logits += weights * logits
                total_weights += domain_weights[domain]
        
        # Normalize by total weights
        total_weights = total_weights.unsqueeze(1)  # [batch_size, 1]
        total_weights = torch.clamp(total_weights, min=1e-8)  # Avoid division by zero
        ensemble_logits = ensemble_logits / total_weights
        
        return ensemble_logits

class DKDDistillationTrainer:
    """Trainer for DKD-based federated distillation"""
    
    def __init__(self, config):
        self.config = config
        self.device = torch.device(config.experiment.device)
        
        # Initialize DKD loss
        self.dkd_loss = DKDLoss(
            alpha=config.training.alpha,
            beta=config.training.beta,
            temperature=config.training.temperature
        )
        
        # Initialize ensemble teacher
        self.ensemble_teacher = WeightedEnsembleTeacher(config)
    
    def distill_step(self, student_model: nn.Module, teacher_predictions: Dict[str, torch.Tensor],
                    domain_weights: Dict[str, torch.Tensor], input_batch: Dict[str, torch.Tensor]) -> Dict[str, float]:
        """
        Perform one distillation step
        Args:
            student_model: Global student model
            teacher_predictions: Teacher predictions for each domain
            domain_weights: Domain relevance weights
            input_batch: Input batch data
        Returns:
            Dictionary of loss values
        """
        # Get student predictions
        student_outputs = student_model(
            input_ids=input_batch['input_ids'],
            attention_mask=input_batch['attention_mask']
        )
        student_logits = student_outputs.logits
        
        # Compute ensemble teacher predictions
        ensemble_logits = self.ensemble_teacher.compute_ensemble_predictions(
            teacher_predictions, domain_weights
        )
        
        # Compute DKD loss
        dkd_losses = self.dkd_loss(student_logits, ensemble_logits)
        
        return dkd_losses
    
    def compute_teacher_predictions(self, teacher_models: Dict[str, nn.Module],
                                  input_batch: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """
        Compute predictions from all teacher models
        Args:
            teacher_models: Dictionary mapping domain -> teacher model
            input_batch: Input batch data
        Returns:
            Dictionary mapping domain -> teacher logits
        """
        teacher_predictions = {}
        
        with torch.no_grad():
            for domain, teacher in teacher_models.items():
                teacher.eval()
                outputs = teacher(
                    input_ids=input_batch['input_ids'],
                    attention_mask=input_batch['attention_mask']
                )
                teacher_predictions[domain] = outputs.logits
        
        return teacher_predictions
    
    def train_global_student(self, student_model: nn.Module, teacher_models: Dict[str, nn.Module],
                           domain_discriminators, pseudo_dataloader, num_epochs: int = 3):
        """
        Train global student model using DKD distillation
        Args:
            student_model: Global student model to train
            teacher_models: Dictionary of teacher models for each domain
            domain_discriminators: Domain discriminator trainer
            pseudo_dataloader: DataLoader for pseudo samples
            num_epochs: Number of training epochs
        """
        logger.info("Starting DKD distillation training...")
        
        optimizer = torch.optim.AdamW(
            student_model.parameters(),
            lr=self.config.training.distillation_lr
        )
        
        student_model.train()
        
        for epoch in range(num_epochs):
            total_dkd_loss = 0
            total_tckd_loss = 0
            total_nckd_loss = 0
            num_batches = 0
            
            for batch in pseudo_dataloader:
                # Move batch to device
                input_batch = {
                    'input_ids': batch['input_ids'].to(self.device),
                    'attention_mask': batch['attention_mask'].to(self.device)
                }
                
                # Get domain weights for pseudo samples
                texts = batch['text']
                domain_weights = domain_discriminators.get_domain_weights(texts)
                
                # Get teacher predictions
                teacher_predictions = self.compute_teacher_predictions(teacher_models, input_batch)
                
                # Perform distillation step
                losses = self.distill_step(
                    student_model, teacher_predictions, domain_weights, input_batch
                )
                
                # Backward pass
                optimizer.zero_grad()
                losses['dkd_loss'].backward()
                optimizer.step()
                
                # Accumulate losses
                total_dkd_loss += losses['dkd_loss'].item()
                total_tckd_loss += losses['tckd_loss'].item()
                total_nckd_loss += losses['nckd_loss'].item()
                num_batches += 1
            
            # Log epoch statistics
            avg_dkd_loss = total_dkd_loss / num_batches
            avg_tckd_loss = total_tckd_loss / num_batches
            avg_nckd_loss = total_nckd_loss / num_batches
            
            logger.info(f"Epoch {epoch+1}/{num_epochs}: "
                       f"DKD Loss: {avg_dkd_loss:.4f}, "
                       f"TCKD Loss: {avg_tckd_loss:.4f}, "
                       f"NCKD Loss: {avg_nckd_loss:.4f}")
        
        logger.info("DKD distillation training completed")
