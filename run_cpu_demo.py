"""
CPU-only D³AFD demo
For environments without sufficient GPU memory
"""
import os
import sys
import logging
import torch

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.config import get_default_config
from src.federated.d3afd_framework import D3AFDFramework

def setup_cpu_config():
    """Create CPU-optimized configuration"""
    config = get_default_config()
    
    # Force CPU usage
    config.experiment.device = "cpu"
    
    # Minimal configuration for CPU
    config.data.num_clients = 2  # Very few clients
    config.data.samples_per_client = 100  # Small datasets
    config.data.max_seq_length = 64  # Very short sequences
    
    # Use smallest models
    config.model.teacher_model = "distilbert-base-uncased"
    config.model.student_model = "distilbert-base-uncased"
    config.model.generator_model = "t5-small"
    config.model.generator_max_length = 32
    
    # Minimal batch sizes
    config.training.local_batch_size = 2
    config.training.distillation_batch_size = 4
    
    # Minimal training
    config.training.federated_rounds = 1
    config.training.distillation_rounds = 1
    config.training.local_epochs = 1
    config.training.distillation_epochs = 1
    config.training.personalization_epochs = 1
    
    # Minimal pseudo data
    config.training.pseudo_samples_per_domain = 10
    config.training.mixed_domain_samples = 5
    
    # Output settings
    config.experiment.output_dir = "cpu_demo_outputs"
    config.experiment.data_dir = "cpu_demo_data"
    
    return config

def setup_logging():
    """Setup logging"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )
    return logging.getLogger(__name__)

def main():
    """Main function for CPU demo"""
    logger = setup_logging()
    
    logger.info("Starting CPU-Only D³AFD Demo")
    logger.info("=" * 50)
    logger.info("This demo uses minimal configuration to run on CPU")
    logger.info("Performance will be slower but should work without GPU")
    
    # Get CPU config
    config = setup_cpu_config()
    
    logger.info("CPU Demo Configuration:")
    logger.info(f"  Device: {config.experiment.device}")
    logger.info(f"  Clients: {config.data.num_clients}")
    logger.info(f"  Samples per client: {config.data.samples_per_client}")
    logger.info(f"  Federated rounds: {config.training.federated_rounds}")
    logger.info(f"  Distillation rounds: {config.training.distillation_rounds}")
    
    try:
        # Initialize framework
        logger.info("Initializing D³AFD Framework...")
        framework = D3AFDFramework(config)
        
        # Run training
        logger.info("Starting training (this may take a while on CPU)...")
        results = framework.run_complete_training(force_reload_data=False)
        
        # Print results
        logger.info("Training completed successfully!")
        
        if 'global_model' in results:
            global_acc = results['global_model'].get('accuracy', 0)
            logger.info(f"Global model accuracy: {global_acc:.4f}")
        
        if 'personalized_models' in results:
            personal_accs = [r.get('accuracy', 0) for r in results['personalized_models'].values()]
            if personal_accs:
                avg_personal_acc = sum(personal_accs) / len(personal_accs)
                logger.info(f"Average personalized accuracy: {avg_personal_acc:.4f}")
        
        logger.info(f"Results saved to: {config.experiment.output_dir}")
        logger.info("Demo completed successfully!")
        
    except Exception as e:
        logger.error(f"Demo failed: {e}")
        logger.info("This might be due to missing dependencies or other issues")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
