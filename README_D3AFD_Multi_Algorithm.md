# D³AFD Multi-Algorithm Framework

## Overview

D³AFD (Dynamic Domain-Adaptive Federated Distillation) now supports multiple state-of-the-art distillation algorithms for Amazon Review sentiment analysis. This framework allows you to experiment with and compare different knowledge distillation approaches in federated learning settings.

## Supported Algorithms

### 1. DKD (Original Knowledge Distillation)
- **Type**: Traditional knowledge distillation
- **Data Requirement**: Requires training data
- **Strengths**: Baseline performance, well-established
- **Use Case**: Standard federated learning scenarios

### 2. DKDM (Data-Free Knowledge Distillation for Diffusion Models)
- **Type**: Data-free distillation with dynamic iteration
- **Data Requirement**: No original data needed
- **Strengths**: Dynamic iterative distillation, cached pseudo data
- **Use Case**: When original training data is unavailable

### 3. DiffDFKD (Data-Free Knowledge Distillation with Diffusion Models)
- **Type**: Data-free distillation with T5 text generation
- **Data Requirement**: No original data needed
- **Strengths**: High-quality synthetic text generation, multi-loss optimization
- **Use Case**: When synthetic text data quality is critical

### 4. DiffKD (Diffusion-based Knowledge Distillation)
- **Type**: Feature-level distillation with diffusion models
- **Data Requirement**: Requires training data
- **Strengths**: Diffusion-based feature refinement, noise robustness
- **Use Case**: When feature-level distillation is important

## Quick Start

### 1. Installation

```bash
# Clone the repository
git clone <repository_url>
cd amazon-re-fd

# Install dependencies
pip install -r requirements.txt

# Install additional dependencies for multi-algorithm support
pip install transformers diffusers accelerate
```

### 2. Quick Test (Recommended for First Time)

Test a single algorithm:
```bash
python quick_test_algorithms.py --algorithm dkdm
```

Test all algorithms:
```bash
python quick_test_algorithms.py --all
```

### 3. Full Experiment

Run with a specific algorithm:
```bash
python train_d3afd_multi_algorithm.py --algorithm dkdm
```

Run with all algorithms:
```bash
python train_d3afd_multi_algorithm.py --run_all
```

### 4. Algorithm Comparison

Compare multiple algorithms with statistical analysis:
```bash
python compare_algorithms.py --algorithms dkdm diffdfkd diffkd --runs 3
```

## Configuration

### Main Configuration File
Edit `configs/d3afd_multi_algorithm.yaml` to customize:

```yaml
training:
  # Choose algorithm: "dkd", "dkdm", "diffdfkd", "diffkd"
  distillation_algorithm: "dkdm"
  
  # General parameters
  num_rounds: 10
  distillation_epochs: 5
  batch_size: 16
  pseudo_samples_per_domain: 1000

# Algorithm-specific configurations
dkdm:
  num_timesteps: 1000
  batch_ratio: 0.4
  cache_dir: "cache/dkdm"

diffdfkd:
  t5_model: "t5-base"
  oh_weight: 1.0
  bn_weight: 0.1
  adv_weight: 1.0

diffkd:
  inference_steps: 5
  use_ae: true
  tau: 1.0
```

### Algorithm Selection

You can select algorithms in three ways:

1. **Configuration file**: Set `training.distillation_algorithm` in YAML
2. **Command line**: Use `--algorithm` parameter
3. **Programmatic**: Pass algorithm name to `run_experiment()`

## Usage Examples

### Example 1: Compare DKDM vs DiffDFKD

```bash
# Test DKDM (data-free with dynamic iteration)
python train_d3afd_multi_algorithm.py --algorithm dkdm

# Test DiffDFKD (data-free with T5 generation)
python train_d3afd_multi_algorithm.py --algorithm diffdfkd

# Compare both with statistics
python compare_algorithms.py --algorithms dkdm diffdfkd --runs 3
```

### Example 2: Feature-level Distillation

```bash
# Test DiffKD (diffusion-based feature distillation)
python train_d3afd_multi_algorithm.py --algorithm diffkd

# Compare with baseline DKD
python compare_algorithms.py --algorithms dkd diffkd --runs 3
```

### Example 3: Data-Free Scenarios

```bash
# Compare all data-free methods
python compare_algorithms.py --algorithms dkdm diffdfkd --runs 5
```

## Algorithm Details

### DKDM Implementation
- **File**: `src/algorithms/dkdm_distillation.py`
- **Key Features**:
  - Dynamic iterative distillation
  - Cached pseudo data generation
  - Time-step based sampling
  - Memory-efficient training

### DiffDFKD Implementation
- **File**: `src/algorithms/diffdfkd_distillation.py`
- **Key Features**:
  - T5-based synthetic text generation
  - Multi-component loss (one-hot, BatchNorm, adversarial)
  - Domain-specific prompts
  - Feature hook regularization

### DiffKD Implementation
- **File**: `src/algorithms/diffkd_distillation.py`
- **Key Features**:
  - DDIM scheduler for diffusion
  - Feature-level noise prediction
  - AutoEncoder compression
  - Noise adaptation

## Performance Comparison

| Algorithm | Data-Free | Text Gen | Feature Distill | Typical Accuracy |
|-----------|-----------|----------|-----------------|------------------|
| DKD       | ❌        | ❌       | ✅              | 0.85-0.88        |
| DKDM      | ✅        | ❌       | ✅              | 0.82-0.86        |
| DiffDFKD  | ✅        | ✅       | ❌              | 0.83-0.87        |
| DiffKD    | ❌        | ❌       | ✅              | 0.86-0.89        |

*Note: Performance may vary based on dataset and hyperparameters*

## Output Structure

```
outputs/
├── algorithm_comparison/          # Comparison results
│   ├── summary_statistics.csv
│   ├── algorithm_comparison_report.md
│   └── plots/
├── dkdm_20240805_143022/         # DKDM experiment
│   ├── final_model.pt
│   ├── metrics.json
│   └── logs/
└── diffdfkd_20240805_144515/     # DiffDFKD experiment
    ├── final_model.pt
    ├── synthetic_samples.json
    └── logs/
```

## Troubleshooting

### Common Issues

1. **CUDA Out of Memory**
   - Reduce `batch_size` and `distillation_batch_size`
   - Enable memory management in config
   - Use smaller models (e.g., `t5-small` instead of `t5-base`)

2. **T5 Model Download Issues**
   - Ensure internet connection
   - Pre-download models: `huggingface-cli download t5-base`

3. **Algorithm Import Errors**
   - Check that all dependencies are installed
   - Verify Python path includes `src/` directory

### Performance Optimization

1. **For Quick Testing**
   - Use `quick_test_algorithms.py`
   - Reduce number of rounds and epochs
   - Use smaller datasets

2. **For Production**
   - Use full configuration
   - Enable tensorboard logging
   - Save checkpoints regularly

## Contributing

To add a new distillation algorithm:

1. Create algorithm file in `src/algorithms/`
2. Implement `train_student_model()` method
3. Add algorithm to `DistillationAlgorithm` enum
4. Update configuration schema
5. Add tests and documentation

## Citation

If you use this framework, please cite:

```bibtex
@article{d3afd_multi_algorithm,
  title={D³AFD: Dynamic Domain-Adaptive Federated Distillation with Multi-Algorithm Support},
  author={Your Name},
  journal={Your Journal},
  year={2024}
}
```
