2025-08-03 10:50:30,173 - __main__ - INFO - SGD-fixed experiment logging initialized. Log file: logs/sgd_fixed_20250803_105030.log
2025-08-03 10:50:30,173 - __main__ - INFO - Starting SGD-Fixed D³AFD Experiment
2025-08-03 10:50:30,173 - __main__ - INFO - ======================================================================
2025-08-03 10:50:30,173 - __main__ - INFO - 🔧 FUNDAMENTAL FIXES APPLIED:
2025-08-03 10:50:30,173 - __main__ - INFO -    - MORE local training (5 epochs) to ensure learning
2025-08-03 10:50:30,173 - __main__ - INFO -    - Moderate global training (3 epochs) for stability
2025-08-03 10:50:30,173 - __main__ - INFO -    - Higher local LR (1e-3) to enable actual learning
2025-08-03 10:50:30,173 - __main__ - INFO -    - Moderate global LR (2e-3) for steady improvement
2025-08-03 10:50:30,173 - __main__ - INFO -    - MINIMAL regularization to allow learning
2025-08-03 10:50:30,173 - __main__ - INFO -    - Fewer but quality pseudo data (40/domain)
2025-08-03 10:50:30,173 - __main__ - INFO -    - Focus on LOCAL learning first, then knowledge transfer
2025-08-03 10:50:30,173 - __main__ - INFO -    - Relaxed early stopping to allow learning
2025-08-03 10:50:30,173 - __main__ - INFO -    - Conservative memory management
2025-08-03 10:50:30,208 - __main__ - INFO - GPU Memory Available: 8.00 GB
2025-08-03 10:50:30,329 - __main__ - INFO - Set GPU memory fraction to 60% (very conservative)
2025-08-03 10:50:30,553 - src.utils.memory_manager - INFO - GPU Memory initial: Allocated: 0.00GB, Reserved: 0.00GB, Free: 8.00GB
2025-08-03 10:50:30,553 - __main__ - INFO - SGD-Fixed Configuration:
2025-08-03 10:50:30,554 - __main__ - INFO -   🏢 Clients: 4
2025-08-03 10:50:30,554 - __main__ - INFO -   📊 Samples per client: 200
2025-08-03 10:50:30,554 - __main__ - INFO -   📏 Max sequence length: 128
2025-08-03 10:50:30,554 - __main__ - INFO -   🔄 Federated rounds: 5
2025-08-03 10:50:30,554 - __main__ - INFO -   🧪 Distillation rounds: 3
2025-08-03 10:50:30,554 - __main__ - INFO -   🎭 Pseudo samples per domain: 40
2025-08-03 10:50:30,554 - __main__ - INFO -   📦 Local batch size: 8
2025-08-03 10:50:30,555 - __main__ - INFO -   📦 Distillation batch size: 16
2025-08-03 10:50:30,555 - __main__ - INFO -   📈 Local LR: 0.001 (FUNDAMENTAL: higher to enable learning)
2025-08-03 10:50:30,555 - __main__ - INFO -   📈 Distillation LR: 0.002 (MODERATE: steady global learning)
2025-08-03 10:50:30,555 - __main__ - INFO -   🛡️  Dropout: 0.1 (MINIMAL: allow learning)
2025-08-03 10:50:30,556 - __main__ - INFO -   🏷️  Label smoothing: 0.05 (MINIMAL: allow learning)
2025-08-03 10:50:30,556 - __main__ - INFO -   📚 Local epochs: 5 (MORE: ensure local learning)
2025-08-03 10:50:30,556 - __main__ - INFO -   🌍 Distillation epochs: 3 (MODERATE: balanced approach)
2025-08-03 10:50:30,556 - __main__ - INFO - Initializing D³AFD Framework...
2025-08-03 10:50:33,028 - src.federated.server - INFO - Federated server initialized
2025-08-03 10:50:33,322 - src.federated.d3afd_framework - INFO - D³AFD Framework initialized
2025-08-03 10:50:33,323 - src.utils.memory_manager - INFO - GPU Memory after_initialization: Allocated: 0.47GB, Reserved: 0.50GB, Free: 7.50GB
2025-08-03 10:50:33,545 - __main__ - INFO - Starting SGD-fixed training...
2025-08-03 10:50:33,545 - __main__ - INFO - 🎯 FUNDAMENTAL STRATEGY - Expected improvements:
2025-08-03 10:50:33,545 - __main__ - INFO -    - ENSURE local models learn basic knowledge first (>40% accuracy)
2025-08-03 10:50:33,545 - __main__ - INFO -    - THEN transfer meaningful knowledge to global model
2025-08-03 10:50:33,545 - __main__ - INFO -    - Target: Local accuracy >40%, Global accuracy >30%
2025-08-03 10:50:33,545 - __main__ - INFO -    - Meaningful DKD loss (>0.01, not near 0)
2025-08-03 10:50:33,545 - __main__ - INFO -    - Decreasing contrastive loss (better feature learning)
2025-08-03 10:50:33,545 - __main__ - INFO -    - Progressive improvement across federated rounds
2025-08-03 10:50:33,545 - __main__ - INFO -    - Stable memory usage without OOM
2025-08-03 10:50:33,545 - src.federated.d3afd_framework - INFO - Starting complete D³AFD training process...
2025-08-03 10:50:33,545 - src.federated.d3afd_framework - INFO - Following D³AFD four-stage design: Local Training → Pseudo Data → DKD Distillation → Personalization
2025-08-03 10:50:33,545 - src.federated.d3afd_framework - INFO - Setting up federated data distribution...
2025-08-03 10:50:33,545 - src.federated.d3afd_framework - INFO - Loading existing federated data split...
2025-08-03 10:50:33,561 - src.data.amazon_dataset - INFO - Loaded federated data for 4 clients
2025-08-03 10:50:33,561 - src.federated.d3afd_framework - INFO - Federated data setup complete:
2025-08-03 10:50:33,561 - src.federated.d3afd_framework - INFO -   - Total clients: 4
2025-08-03 10:50:33,561 - src.federated.d3afd_framework - INFO -   - Domain distribution: {'Books': 300, 'Electronics': 300, 'Home_and_Kitchen': 200}
2025-08-03 10:50:33,561 - src.federated.d3afd_framework - INFO - Initializing federated clients...
2025-08-03 10:50:40,451 - src.federated.client - INFO - Client 0 initialized with domains: ['Books']
2025-08-03 10:50:41,463 - src.federated.client - INFO - Client 0: Models initialized
2025-08-03 10:50:41,463 - src.federated.server - INFO - Registered client 0 with domains: ['Books']
2025-08-03 10:50:41,463 - src.federated.d3afd_framework - INFO - Client 0 initialized with 200 samples
2025-08-03 10:50:48,324 - src.federated.client - INFO - Client 1 initialized with domains: ['Electronics']
2025-08-03 10:50:49,331 - src.federated.client - INFO - Client 1: Models initialized
2025-08-03 10:50:49,331 - src.federated.server - INFO - Registered client 1 with domains: ['Electronics']
2025-08-03 10:50:49,331 - src.federated.d3afd_framework - INFO - Client 1 initialized with 200 samples
2025-08-03 10:50:56,184 - src.federated.client - INFO - Client 2 initialized with domains: ['Home_and_Kitchen']
2025-08-03 10:50:57,198 - src.federated.client - INFO - Client 2: Models initialized
2025-08-03 10:50:57,198 - src.federated.server - INFO - Registered client 2 with domains: ['Home_and_Kitchen']
2025-08-03 10:50:57,198 - src.federated.d3afd_framework - INFO - Client 2 initialized with 200 samples
2025-08-03 10:51:04,143 - src.federated.client - INFO - Client 3 initialized with domains: ['Books', 'Electronics']
2025-08-03 10:51:05,193 - src.federated.client - INFO - Client 3: Models initialized
2025-08-03 10:51:05,193 - src.federated.server - INFO - Registered client 3 with domains: ['Books', 'Electronics']
2025-08-03 10:51:05,193 - src.federated.d3afd_framework - INFO - Client 3 initialized with 200 samples
2025-08-03 10:51:05,193 - src.federated.d3afd_framework - INFO - Initialized 4 clients
2025-08-03 10:51:05,193 - src.federated.d3afd_framework - INFO - === Initial Setup Phase ===
2025-08-03 10:51:05,193 - src.federated.d3afd_framework - INFO - Training initial local teacher models...
2025-08-03 10:51:05,193 - src.federated.client - INFO - Client 0: Training local teacher for 5 epochs
2025-08-03 10:51:15,642 - src.federated.client - INFO - Client 0 - Epoch 1/5: Loss: 1.6225, Accuracy: 0.2429
2025-08-03 10:51:23,996 - src.federated.client - INFO - Client 0 - Epoch 2/5: Loss: 1.3698, Accuracy: 0.6571
2025-08-03 10:51:32,181 - src.federated.client - INFO - Client 0 - Epoch 3/5: Loss: 0.8781, Accuracy: 0.9286
2025-08-03 10:51:40,543 - src.federated.client - INFO - Client 0 - Epoch 4/5: Loss: 0.5224, Accuracy: 1.0000
2025-08-03 10:51:40,543 - src.federated.client - WARNING - Client 0: Early stopping due to potential overfitting (accuracy: 1.0000)
2025-08-03 10:51:40,543 - src.federated.client - INFO - Client 0: Local teacher training completed. Final accuracy: 0.7071
2025-08-03 10:51:40,543 - src.federated.d3afd_framework - INFO - Client 0 teacher training: Accuracy 0.7071
2025-08-03 10:51:40,543 - src.federated.client - INFO - Client 1: Training local teacher for 5 epochs
2025-08-03 10:51:48,130 - src.utils.memory_manager - INFO - GPU Memory final: Allocated: 3.00GB, Reserved: 3.24GB, Free: 4.76GB
2025-08-03 10:51:48,130 - __main__ - INFO - SGD-fixed experiment completed.
