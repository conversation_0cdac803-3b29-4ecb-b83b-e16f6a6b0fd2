"""
Metrics tracking utilities for D³AFD framework
"""

import json
import os
import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime

from .logger import get_logger

logger = get_logger(__name__)


class MetricsTracker:
    """
    Track and save metrics during training and evaluation
    """
    
    def __init__(self, config, output_dir: str):
        """
        Initialize metrics tracker
        
        Args:
            config: Configuration object
            output_dir: Directory to save metrics
        """
        self.config = config
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Metrics storage
        self.round_metrics = []
        self.eval_metrics = []
        self.final_metrics = {}
        
        # Metadata
        self.start_time = datetime.now()
        self.algorithm = getattr(config.training, 'distillation_algorithm', 'unknown')
        
        logger.info(f"Metrics tracker initialized for {self.algorithm} algorithm")
    
    def log_round_metrics(self, round_num: int, metrics: Dict[str, Any]) -> None:
        """
        Log metrics for a training round
        
        Args:
            round_num: Round number
            metrics: Dictionary of metrics
        """
        round_data = {
            'round': round_num,
            'timestamp': datetime.now().isoformat(),
            **metrics
        }
        
        self.round_metrics.append(round_data)
        
        # Save incrementally
        self._save_round_metrics()
        
        logger.info(f"Round {round_num} metrics logged: {metrics}")
    
    def log_eval_metrics(self, round_num: int, metrics: Dict[str, float]) -> None:
        """
        Log evaluation metrics
        
        Args:
            round_num: Round number
            metrics: Dictionary of evaluation metrics
        """
        eval_data = {
            'round': round_num,
            'timestamp': datetime.now().isoformat(),
            **metrics
        }
        
        self.eval_metrics.append(eval_data)
        
        # Save incrementally
        self._save_eval_metrics()
        
        logger.info(f"Round {round_num} evaluation metrics logged: {metrics}")
    
    def log_final_metrics(self, metrics: Dict[str, float]) -> None:
        """
        Log final experiment metrics
        
        Args:
            metrics: Dictionary of final metrics
        """
        self.final_metrics = {
            'algorithm': self.algorithm,
            'start_time': self.start_time.isoformat(),
            'end_time': datetime.now().isoformat(),
            'duration_minutes': (datetime.now() - self.start_time).total_seconds() / 60,
            **metrics
        }
        
        # Save final metrics
        self._save_final_metrics()
        
        logger.info(f"Final metrics logged: {metrics}")
    
    def get_best_metrics(self, metric_name: str = 'accuracy') -> Optional[Dict[str, Any]]:
        """
        Get best evaluation metrics based on specified metric
        
        Args:
            metric_name: Metric to use for finding best performance
            
        Returns:
            Best metrics dictionary or None
        """
        if not self.eval_metrics:
            return None
        
        # Find best round
        best_round = max(self.eval_metrics, key=lambda x: x.get(metric_name, 0))
        return best_round
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """
        Get summary of all metrics
        
        Returns:
            Summary dictionary
        """
        summary = {
            'algorithm': self.algorithm,
            'total_rounds': len(self.round_metrics),
            'total_evaluations': len(self.eval_metrics)
        }
        
        # Add final metrics if available
        if self.final_metrics:
            summary.update(self.final_metrics)
        
        # Add best metrics if available
        best_metrics = self.get_best_metrics()
        if best_metrics:
            summary['best_round'] = best_metrics['round']
            summary['best_accuracy'] = best_metrics.get('accuracy', 0)
            summary['best_f1_macro'] = best_metrics.get('f1_macro', 0)
        
        return summary
    
    def save_metrics(self) -> None:
        """Save all metrics to files"""
        self._save_round_metrics()
        self._save_eval_metrics()
        self._save_final_metrics()
        self._save_summary()
        
        logger.info(f"All metrics saved to {self.output_dir}")
    
    def _save_round_metrics(self) -> None:
        """Save round metrics to JSON and CSV"""
        if not self.round_metrics:
            return
        
        # JSON format
        json_path = self.output_dir / 'round_metrics.json'
        with open(json_path, 'w') as f:
            json.dump(self.round_metrics, f, indent=2)
        
        # CSV format
        try:
            df = pd.DataFrame(self.round_metrics)
            csv_path = self.output_dir / 'round_metrics.csv'
            df.to_csv(csv_path, index=False)
        except Exception as e:
            logger.warning(f"Failed to save round metrics CSV: {e}")
    
    def _save_eval_metrics(self) -> None:
        """Save evaluation metrics to JSON and CSV"""
        if not self.eval_metrics:
            return
        
        # JSON format
        json_path = self.output_dir / 'eval_metrics.json'
        with open(json_path, 'w') as f:
            json.dump(self.eval_metrics, f, indent=2)
        
        # CSV format
        try:
            df = pd.DataFrame(self.eval_metrics)
            csv_path = self.output_dir / 'eval_metrics.csv'
            df.to_csv(csv_path, index=False)
        except Exception as e:
            logger.warning(f"Failed to save eval metrics CSV: {e}")
    
    def _save_final_metrics(self) -> None:
        """Save final metrics to JSON"""
        if not self.final_metrics:
            return
        
        json_path = self.output_dir / 'final_metrics.json'
        with open(json_path, 'w') as f:
            json.dump(self.final_metrics, f, indent=2)
    
    def _save_summary(self) -> None:
        """Save metrics summary"""
        summary = self.get_metrics_summary()
        
        json_path = self.output_dir / 'metrics_summary.json'
        with open(json_path, 'w') as f:
            json.dump(summary, f, indent=2)


def calculate_metrics(y_true: np.ndarray, y_pred: np.ndarray, 
                     average: str = 'macro') -> Dict[str, float]:
    """
    Calculate common classification metrics
    
    Args:
        y_true: True labels
        y_pred: Predicted labels
        average: Averaging method for multi-class metrics
        
    Returns:
        Dictionary of metrics
    """
    try:
        from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
        
        metrics = {
            'accuracy': accuracy_score(y_true, y_pred),
            'precision': precision_score(y_true, y_pred, average=average, zero_division=0),
            'recall': recall_score(y_true, y_pred, average=average, zero_division=0),
            'f1_macro': f1_score(y_true, y_pred, average='macro', zero_division=0),
            'f1_weighted': f1_score(y_true, y_pred, average='weighted', zero_division=0)
        }
        
        return metrics
        
    except ImportError:
        logger.warning("scikit-learn not available, using basic accuracy only")
        accuracy = (y_true == y_pred).mean()
        return {'accuracy': accuracy}


def format_metrics(metrics: Dict[str, float], precision: int = 4) -> str:
    """
    Format metrics dictionary for display
    
    Args:
        metrics: Dictionary of metrics
        precision: Number of decimal places
        
    Returns:
        Formatted string
    """
    formatted = []
    for key, value in metrics.items():
        if isinstance(value, (int, float)):
            formatted.append(f"{key}: {value:.{precision}f}")
        else:
            formatted.append(f"{key}: {value}")
    
    return ", ".join(formatted)
