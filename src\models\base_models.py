"""
Base models for D³AFD implementation
"""
import torch
import torch.nn as nn
from transformers import AutoModel, AutoConfig
from typing import Optional
import logging

logger = logging.getLogger(__name__)

class SentimentClassifier(nn.Module):
    """Base sentiment classifier for Amazon reviews"""
    
    def __init__(self, config, model_name: str):
        super().__init__()
        self.config = config
        
        # Load pre-trained transformer
        self.encoder = AutoModel.from_pretrained(model_name)
        
        # Classification head
        self.classifier = nn.Sequential(
            nn.Dropout(config.model.dropout_rate),
            nn.Linear(config.model.teacher_hidden_size, config.model.num_classes)
        )
        
        # Initialize weights
        self._init_weights()
    
    def _init_weights(self):
        """Initialize classifier weights"""
        for module in self.classifier.modules():
            if isinstance(module, nn.Linear):
                nn.init.normal_(module.weight, std=0.02)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
    
    def forward(self, input_ids: torch.Tensor, attention_mask: torch.Tensor,
                output_hidden_states: bool = False):
        """Forward pass"""
        # Encode text
        encoder_outputs = self.encoder(
            input_ids=input_ids,
            attention_mask=attention_mask,
            output_hidden_states=output_hidden_states
        )
        
        # Get pooled representation
        # DistilBERT doesn't have pooler_output, so we always use [CLS] token
        if hasattr(encoder_outputs, 'pooler_output') and encoder_outputs.pooler_output is not None:
            pooled_output = encoder_outputs.pooler_output
        else:
            # Use [CLS] token (first token) from last hidden state
            pooled_output = encoder_outputs.last_hidden_state[:, 0, :]
        
        # Classify
        logits = self.classifier(pooled_output)
        
        # Return outputs similar to transformers models
        outputs = type('ModelOutput', (), {})()
        outputs.logits = logits
        outputs.hidden_states = encoder_outputs.hidden_states if output_hidden_states else None
        outputs.pooler_output = pooled_output
        
        return outputs

class TeacherModel(SentimentClassifier):
    """Teacher model for local training"""
    
    def __init__(self, config):
        super().__init__(config, config.model.teacher_model)

class StudentModel(SentimentClassifier):
    """Student model for global distillation"""
    
    def __init__(self, config):
        super().__init__(config, config.model.student_model)
        
        # Adjust hidden size for student model
        student_config = AutoConfig.from_pretrained(config.model.student_model)
        student_hidden_size = student_config.hidden_size
        
        # Update classifier for student hidden size
        self.classifier = nn.Sequential(
            nn.Dropout(config.model.dropout_rate),
            nn.Linear(student_hidden_size, config.model.num_classes)
        )
        self._init_weights()

class PersonalizedHead(nn.Module):
    """Personalized classification head for clients"""
    
    def __init__(self, config, input_size: int):
        super().__init__()
        self.config = config
        
        self.head = nn.Sequential(
            nn.Dropout(config.model.dropout_rate),
            nn.Linear(input_size, config.model.num_classes)
        )
        
        self._init_weights()
    
    def _init_weights(self):
        """Initialize weights"""
        for module in self.head.modules():
            if isinstance(module, nn.Linear):
                nn.init.normal_(module.weight, std=0.02)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
    
    def forward(self, features: torch.Tensor) -> torch.Tensor:
        """Forward pass"""
        return self.head(features)

class PersonalizedModel(nn.Module):
    """Personalized model with shared backbone and private head"""
    
    def __init__(self, global_backbone: nn.Module, personalized_head: PersonalizedHead):
        super().__init__()
        self.backbone = global_backbone
        self.head = personalized_head
        
        # Freeze backbone by default
        self.freeze_backbone()
    
    def freeze_backbone(self):
        """Freeze backbone parameters"""
        for param in self.backbone.parameters():
            param.requires_grad = False
    
    def unfreeze_backbone(self):
        """Unfreeze backbone parameters"""
        for param in self.backbone.parameters():
            param.requires_grad = True
    
    def forward(self, input_ids: torch.Tensor, attention_mask: torch.Tensor,
                output_hidden_states: bool = False):
        """Forward pass"""
        # Get features from frozen backbone
        with torch.no_grad() if self.backbone.training == False else torch.enable_grad():
            backbone_outputs = self.backbone(
                input_ids=input_ids,
                attention_mask=attention_mask,
                output_hidden_states=output_hidden_states
            )
        
        # Get features for personalized head
        if hasattr(backbone_outputs, 'pooler_output') and backbone_outputs.pooler_output is not None:
            features = backbone_outputs.pooler_output
        else:
            features = backbone_outputs.hidden_states[-1][:, 0, :] if output_hidden_states else backbone_outputs.last_hidden_state[:, 0, :]
        
        # Apply personalized head
        logits = self.head(features)
        
        # Return outputs
        outputs = type('ModelOutput', (), {})()
        outputs.logits = logits
        outputs.hidden_states = backbone_outputs.hidden_states if output_hidden_states else None
        outputs.pooler_output = features
        
        return outputs
