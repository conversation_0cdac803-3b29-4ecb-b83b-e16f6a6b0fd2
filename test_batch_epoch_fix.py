"""
Test script to verify batch indexing across epochs fix
"""
import torch

def test_batch_indexing_across_epochs():
    """Test batch indexing logic across multiple epochs"""
    print("🧪 Testing batch indexing across multiple epochs...")
    
    # Simulate teacher predictions
    teacher_predictions = {
        'Books': torch.randn(1240, 5),  # 1240 samples, 5 classes
        'Electronics': torch.randn(1240, 5),
        'Home_and_Kitchen': torch.randn(1240, 5)
    }
    
    # Test parameters
    distillation_batch_size = 16
    distillation_epochs = 2
    total_samples = 1240
    expected_batches_per_epoch = (total_samples + distillation_batch_size - 1) // distillation_batch_size  # Ceiling division
    
    print(f"   Teacher predictions: {len(teacher_predictions)} domains, {total_samples} samples each")
    print(f"   Batch size: {distillation_batch_size}")
    print(f"   Epochs: {distillation_epochs}")
    print(f"   Expected batches per epoch: {expected_batches_per_epoch}")
    
    # Simulate the fixed logic
    total_processed_batches = 0
    
    for epoch in range(distillation_epochs):
        print(f"\n   📊 Epoch {epoch + 1}:")
        batch_idx = 0  # Reset for each epoch (this is the fix)
        epoch_processed_batches = 0
        
        # Simulate dataloader batches
        for dataloader_batch_idx in range(expected_batches_per_epoch):
            # Simulate current batch size (last batch might be smaller)
            start_sample = dataloader_batch_idx * distillation_batch_size
            end_sample = min(start_sample + distillation_batch_size, total_samples)
            current_batch_size = end_sample - start_sample
            
            if current_batch_size <= 0:
                break
            
            # Test the indexing logic
            batch_teacher_preds = {}
            
            for domain in teacher_predictions:
                start_idx = batch_idx * distillation_batch_size
                end_idx = start_idx + current_batch_size
                total_predictions = teacher_predictions[domain].size(0)
                
                # Check if we have enough predictions
                if start_idx < total_predictions:
                    actual_end_idx = min(end_idx, total_predictions)
                    actual_batch_size = actual_end_idx - start_idx
                    
                    if actual_batch_size > 0:
                        batch_teacher_preds[domain] = teacher_predictions[domain][start_idx:actual_end_idx]
                
            # Check results
            if batch_teacher_preds:
                epoch_processed_batches += 1
                total_processed_batches += 1
                
                # Verify all domains have same batch size
                batch_sizes = [preds.shape[0] for preds in batch_teacher_preds.values()]
                if len(set(batch_sizes)) == 1:
                    batch_size = batch_sizes[0]
                    if dataloader_batch_idx < 5 or dataloader_batch_idx >= expected_batches_per_epoch - 2:  # Show first few and last few
                        print(f"      Batch {batch_idx}: {batch_size} samples ✅")
                else:
                    print(f"      Batch {batch_idx}: Inconsistent batch sizes {batch_sizes} ❌")
                    return False
            else:
                print(f"      Batch {batch_idx}: No valid predictions ⚠️")
            
            batch_idx += 1
        
        print(f"      Epoch {epoch + 1} processed: {epoch_processed_batches} batches")
    
    print(f"\n   📈 Summary:")
    print(f"      Total processed batches: {total_processed_batches}")
    print(f"      Expected total batches: {expected_batches_per_epoch * distillation_epochs}")
    
    # The key test: we should process the same number of batches in each epoch
    expected_total = expected_batches_per_epoch * distillation_epochs
    if total_processed_batches == expected_total:
        print(f"      ✅ Correct: Processed all expected batches")
        return True
    else:
        print(f"      ❌ Error: Expected {expected_total}, got {total_processed_batches}")
        return False

def test_edge_cases():
    """Test edge cases"""
    print("\n🔬 Testing edge cases...")
    
    # Test case 1: Exact multiple of batch size
    print("   Test 1: Exact multiple of batch size")
    total_samples = 160  # 10 batches of 16
    batch_size = 16
    expected_batches = total_samples // batch_size
    
    teacher_preds = {'domain1': torch.randn(total_samples, 5)}
    
    processed_batches = 0
    for epoch in range(2):
        batch_idx = 0
        for batch_num in range(expected_batches):
            start_idx = batch_idx * batch_size
            end_idx = start_idx + batch_size
            
            if start_idx < total_samples:
                processed_batches += 1
            
            batch_idx += 1
    
    if processed_batches == expected_batches * 2:
        print(f"      ✅ Exact multiple: {processed_batches} batches processed")
    else:
        print(f"      ❌ Exact multiple: Expected {expected_batches * 2}, got {processed_batches}")
        return False
    
    # Test case 2: Non-exact multiple
    print("   Test 2: Non-exact multiple of batch size")
    total_samples = 165  # 10 full batches + 1 partial batch of 5
    expected_batches = (total_samples + batch_size - 1) // batch_size  # 11 batches
    
    teacher_preds = {'domain1': torch.randn(total_samples, 5)}
    
    processed_batches = 0
    for epoch in range(2):
        batch_idx = 0
        for batch_num in range(expected_batches):
            start_idx = batch_idx * batch_size
            current_batch_size = min(batch_size, total_samples - start_idx)
            
            if current_batch_size > 0 and start_idx < total_samples:
                processed_batches += 1
            
            batch_idx += 1
    
    if processed_batches == expected_batches * 2:
        print(f"      ✅ Non-exact multiple: {processed_batches} batches processed")
    else:
        print(f"      ❌ Non-exact multiple: Expected {expected_batches * 2}, got {processed_batches}")
        return False
    
    return True

def main():
    """Main test function"""
    print("=" * 60)
    print("🔧 Testing Batch Indexing Across Epochs Fix")
    print("=" * 60)
    
    # Run tests
    indexing_test = test_batch_indexing_across_epochs()
    edge_test = test_edge_cases()
    
    print("\n" + "=" * 60)
    print("📊 Test Results:")
    print(f"   Multi-Epoch Indexing: {'✅ PASS' if indexing_test else '❌ FAIL'}")
    print(f"   Edge Cases: {'✅ PASS' if edge_test else '❌ FAIL'}")
    
    all_passed = indexing_test and edge_test
    
    if all_passed:
        print("\n🎉 All tests passed! Batch indexing fix is working correctly.")
        print("The epoch reset issue should be resolved.")
        return 0
    else:
        print("\n⚠️  Some tests failed. Please check the fixes.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    exit(exit_code)
