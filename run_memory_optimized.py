"""
Memory-optimized D³AFD experiment runner
Reduces memory usage for GPU-constrained environments
"""
import os
import sys
import logging
import torch
import gc

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.config import get_default_config
from src.federated.d3afd_framework import D3AFDFramework
from src.utils.memory_manager import get_memory_manager

def setup_memory_optimized_config():
    """Create memory-optimized configuration"""
    config = get_default_config()

    # Reduce model sizes and batch sizes - more aggressive
    config.data.num_clients = 2  # Even fewer clients
    config.data.samples_per_client = 100  # Smaller datasets
    config.data.max_seq_length = 64  # Much shorter sequences

    # Use smaller models
    config.model.teacher_model = "distilbert-base-uncased"  # Smaller teacher
    config.model.student_model = "distilbert-base-uncased"  # Same as teacher for simplicity
    config.model.generator_model = "t5-small"  # Smaller T5
    config.model.generator_max_length = 32  # Much shorter generation

    # Reduce batch sizes significantly
    config.training.local_batch_size = 2  # Very small batch
    config.training.distillation_batch_size = 4  # Very small batch

    # Reduce training iterations
    config.training.federated_rounds = 2
    config.training.distillation_rounds = 2
    config.training.local_epochs = 1  # Reduce epochs
    config.training.distillation_epochs = 1
    config.training.personalization_epochs = 1

    # Reduce pseudo data generation significantly
    config.training.pseudo_samples_per_domain = 20  # Much fewer samples
    config.training.mixed_domain_samples = 10  # Much fewer samples

    # Output settings
    config.experiment.output_dir = "memory_optimized_outputs"
    config.experiment.data_dir = "memory_optimized_data"

    return config

def clear_gpu_memory():
    """Clear GPU memory"""
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        gc.collect()

def setup_logging():
    """Setup logging"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )
    return logging.getLogger(__name__)

def main():
    """Main function for memory-optimized experiment"""
    logger = setup_logging()

    logger.info("Starting Memory-Optimized D³AFD Experiment")
    logger.info("=" * 60)

    # Set CUDA memory allocation environment variable
    os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'expandable_segments:True'

    # Check GPU memory
    if torch.cuda.is_available():
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        logger.info(f"GPU Memory Available: {gpu_memory:.2f} GB")

        # Clear any existing GPU memory
        clear_gpu_memory()

        # Set memory fraction to prevent OOM - reduce to 70% for safety
        torch.cuda.set_per_process_memory_fraction(0.7)  # Use 70% of GPU memory
        logger.info("Set GPU memory fraction to 70% to prevent OOM")
    else:
        logger.info("CUDA not available, using CPU")
    
    # Get memory-optimized config
    config = setup_memory_optimized_config()
    
    logger.info("Memory-Optimized Configuration:")
    logger.info(f"  Clients: {config.data.num_clients}")
    logger.info(f"  Samples per client: {config.data.samples_per_client}")
    logger.info(f"  Max sequence length: {config.data.max_seq_length}")
    logger.info(f"  Teacher model: {config.model.teacher_model}")
    logger.info(f"  Generator model: {config.model.generator_model}")
    logger.info(f"  Local batch size: {config.training.local_batch_size}")
    logger.info(f"  Federated rounds: {config.training.federated_rounds}")
    
    try:
        # Initialize framework
        logger.info("Initializing D³AFD Framework...")
        framework = D3AFDFramework(config)
        
        # Clear memory after initialization
        clear_gpu_memory()
        
        # Run training
        logger.info("Starting training...")
        results = framework.run_complete_training(force_reload_data=False)
        
        # Print results
        logger.info("Training completed successfully!")
        
        if 'global_model' in results:
            global_acc = results['global_model'].get('accuracy', 0)
            logger.info(f"Global model accuracy: {global_acc:.4f}")
        
        if 'personalized_models' in results:
            personal_accs = [r.get('accuracy', 0) for r in results['personalized_models'].values()]
            if personal_accs:
                avg_personal_acc = sum(personal_accs) / len(personal_accs)
                logger.info(f"Average personalized accuracy: {avg_personal_acc:.4f}")
        
        logger.info(f"Results saved to: {config.experiment.output_dir}")
        
    except torch.cuda.OutOfMemoryError as e:
        logger.error("CUDA Out of Memory Error!")
        logger.error("Try reducing batch sizes or model sizes further")
        logger.error("You can also try running on CPU by setting device='cpu'")
        logger.error(f"Error details: {e}")
        
    except Exception as e:
        logger.error(f"Experiment failed: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Clean up GPU memory
        clear_gpu_memory()

if __name__ == "__main__":
    main()
