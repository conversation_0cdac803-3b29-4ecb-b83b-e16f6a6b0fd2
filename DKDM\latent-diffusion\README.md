# Dynamic Iterative Distillation in Latent Space

## Installation

```bash
conda env create -f environment.yaml
conda activate ldm

pip install -e git+https://github.com/CompVis/taming-transformers.git@master#egg=taming-transformers
pip install -e git+https://github.com/openai/CLIP.git@main#egg=clip
pip install -e .
```

## Teachers

- **celeba_hq_256**: Download `celeba.zip` from [original latent-diffusion repo](https://ommer-lab.com/files/latent-diffusion/celeba.zip) and unzip it to `models/ldm/celeba256/model.ckpt`. Then, run `cd models/ldm/celeba256; python extract_ae.py` to extract the autoencoder so that the checkpoint of autoencoder `models/ldm/celeba256/ae.ckpt` is generated.
- **ffhq_256**: Download `ffhq.zip` from [original latent-diffusion repo](https://ommer-lab.com/files/latent-diffusion/ffhq.zip) and unzip it to `models/ldm/ffhq256/model.ckpt`. Then, run `cd models/ldm/ffhq256; python extract_ae.py` to extract the autoencoder so that the checkpoint of autoencoder `models/ldm/ffhq256/ae.ckpt` is generated.

## Dynamic Iterative Distillation

**① Preparation**: Prepare dynamic batch for DKDM

```bash
# celeba_hq_256
CUDA_VISIBLE_DEVICES=x torchrun --nproc_per_node=1 --nnodes=1 scripts/dynamic_prepare.py -r models/ldm/celeba256/model.ckpt --batch_size 256 --stu_batch_size 48 --save_to models/ldm/celeba256/cache --BATCH 4
# ffhq_256
CUDA_VISIBLE_DEVICES=x torchrun --nproc_per_node=1 --nnodes=1 scripts/dynamic_prepare.py -r models/ldm/ffhq256/model.ckpt --batch_size 128 --stu_batch_size 42 --save_to models/ldm/ffhq256/cache --BATCH 4
```

Note: running `scripts/dynamic_prepare.py` with multiple GPUs is not recommended. You can specify smaller `--BATCH`, run it multiple times, and merge the cache files. You can also download the dynamic batches generated by the authors from [OneDrive](https://1drv.ms/f/s!ApmL4Zp8fxOMgudEmEVoNDDs9JYHSA?e=xDmduW), and rename them to `models/ldm/celeba256/cache`, `models/ldm/ffhq256/cache`, respectively.

**② Training**:

```bash
# celeba_hq_256
python main_dkdm.py --base configs/distill/celeba/dkdm-celebahq-ldm-vq-4.yaml -t --gpus 0, --max_steps 15000000 --scale_lr False
# ffhq_256
python main_dkdm.py --base configs/distill/ffhq/dkdm-ffhq-ldm-vq-4.yaml -t --gpus 0, --max_steps 15000000 --scale_lr False
```

## Baselines

### Data-Free Training

**① Preparation**: Prepare the synthetic dataset for baselines:

- `celeba_hq_256`: generate 25000 images. Refer to the script `data/prepare_celebahq.py` and make sure the file name of these images are like: `data/celebahq_baseline/00000.npy`, `data/celebahq_baseline/00001.npy`, ..., `data/celebahq_baseline/24999.npy`

```bash
CUDA_VISIBLE_DEVICES=0 python scripts/sample_diffusion.py -r models/ldm/celeba256/model.ckpt -l ./log -n 25000 --batch_size 64 --custom_steps 100 --eta 0.
```

- `ffhq_256`: generate 60000 images. Refer to the script `data/prepare_ffhq.py` and make sure the file name of these images are like: `data/ffhq_baseline/00000.png`, `data/ffhq_baseline/00001.png`, ..., `data/ffhq_baseline/59999.png`

```bash
CUDA_VISIBLE_DEVICES=0 python scripts/sample_diffusion.py -r models/ldm/ffhq256/model.ckpt -l ./log -n 60000 --batch_size 64 --custom_steps 100 --eta 0.0
```

**② Train**

```bash
# celeba_hq_256
python main.py --base configs/distill/celeba/baseline-celebahq-ldm-vq-4.yaml -t --gpus 0, --max_steps 15000000 --scale_lr False
# ffhq_256
python main.py --base configs/distill/ffhq/baseline-ffhq-ldm-vq-4.yaml -t --gpus 0, --max_steps 15000000 --scale_lr False
```

### Data-Limited and Data-Based Training

**① Preparation**: Download CelebA-HQ-256 and follow `data/celebahq_npy` to generate npy file in `data/celebahq`. Download FFHQ-256 datasets and move it to `data/ffhq`. The final files should be like: `data/celebahq/imgHQ00000.npy`\~`data/celebahq/imgHQ29999.npy` and `data/ffhq/00000.png`\~`data/ffhq/69999.png`. The index of dataset for data-limited training was previous generated in `*_baseline_*.txt`. You could refer to `data/celebahq_mix.py` and `data/ffhq_mix.py` to understand how to generate them.

**② Train**

```bash
# celeba_hq_256
## Data-Limited Training
### 5%
python main.py --base configs/distill/celeba/baseline-0.05-celebahq-ldm-vq-4.yaml -t --gpus 0, --max_steps 15000000 --scale_lr False
### 10%
python main.py --base configs/distill/celeba/baseline-0.1-celebahq-ldm-vq-4.yaml -t --gpus 0, --max_steps 15000000 --scale_lr False
### 15%
python main.py --base configs/distill/celeba/baseline-0.15-celebahq-ldm-vq-4.yaml -t --gpus 0, --max_steps 15000000 --scale_lr False
### 20%
python main.py --base configs/distill/celeba/baseline-0.1-celebahq-ldm-vq-4.yaml -t --gpus 0, --max_steps 15000000 --scale_lr False
## Data-Based Training
python main.py --base configs/distill/celeba/baseline-1-celebahq-ldm-vq-4.yaml -t --gpus 0, --max_steps 15000000 --scale_lr False

# ffhq_256
## Data-Limited Training
### 5%
python main.py --base configs/distill/ffhq/baseline-0.05-ffhq-ldm-vq-4.yaml -t --gpus 0, --max_steps 15000000 --scale_lr False
### 10%
python main.py --base configs/distill/ffhq/baseline-0.1-ffhq-ldm-vq-4.yaml -t --gpus 0, --max_steps 15000000 --scale_lr False
### 15%
python main.py --base configs/distill/ffhq/baseline-0.15-ffhq-ldm-vq-4.yaml -t --gpus 0, --max_steps 15000000 --scale_lr False
### 20%
python main.py --base configs/distill/ffhq//baseline-0.2-ffhq-ldm-vq-4.yaml -t --gpus 0, --max_steps 15000000 --scale_lr False
## Data-Based Training
python main.py --base configs/distill/ffhq/baseline-1-ffhq-ldm-vq-4.yaml -t --gpus 0, --max_steps 15000000 --scale_lr False
```

## Evaluation

**① Prepare reference images**: Refer to `data/celebahq_npz.py` and `data/ffhq_npz.py` to generate `data/celebahq_train.npz` and `data/ffhq_train.npz`.

**② Sample images from the trained models**:

```bash
CUDA_VISIBLE_DEVICES=0 torchrun --nproc_per_node=1 --nnodes=1 --master_port 12344 scripts/sample_diffusion_ddp.py -r ./logs/path/to/checkpoints/step=639999.ckpt --base ./logs/path/to/configs/project.yaml -l ./log -n 50000 --batch_size 128 --custom_steps 200 --eta 0.0
```

**③ Evaluation**: Use ADM TensorFlow evaluation suite, which is also available in [our pixel space code](../guided-diffusion/README.md#evaluation).

## Released Models

You could download the models trained by our Dynamic Iterative Distillation from [OneDrive](https://1drv.ms/f/s!ApmL4Zp8fxOMgudOKnyyPMkSQgIGIQ?e=LeLyfW).
