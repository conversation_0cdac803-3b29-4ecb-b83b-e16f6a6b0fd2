"""
Test script to verify the DistilBERT pooler_output fix
"""
import torch
import sys
import os

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.config import get_memory_optimized_config
from src.models.base_models import TeacherModel, StudentModel
from src.models.domain_discriminator import DomainDiscriminator

def test_models():
    """Test that models work with DistilBERT"""
    print("Testing DistilBERT compatibility...")
    
    # Get memory optimized config (uses DistilBERT)
    config = get_memory_optimized_config()
    config.experiment.device = "cpu"  # Use CPU for testing
    
    print(f"Teacher model: {config.model.teacher_model}")
    print(f"Student model: {config.model.student_model}")
    
    try:
        # Test TeacherModel
        print("\n1. Testing TeacherModel...")
        teacher = TeacherModel(config)
        
        # Create dummy input
        batch_size = 2
        seq_length = 32
        input_ids = torch.randint(0, 1000, (batch_size, seq_length))
        attention_mask = torch.ones(batch_size, seq_length)
        
        # Forward pass
        outputs = teacher(input_ids=input_ids, attention_mask=attention_mask)
        print(f"   ✓ Teacher output shape: {outputs.logits.shape}")
        print(f"   ✓ Has pooler_output: {hasattr(outputs, 'pooler_output')}")
        
        # Test StudentModel
        print("\n2. Testing StudentModel...")
        student = StudentModel(config)
        outputs = student(input_ids=input_ids, attention_mask=attention_mask)
        print(f"   ✓ Student output shape: {outputs.logits.shape}")
        print(f"   ✓ Has pooler_output: {hasattr(outputs, 'pooler_output')}")
        
        # Test DomainDiscriminator
        print("\n3. Testing DomainDiscriminator...")
        discriminator = DomainDiscriminator(config, "Books")
        outputs = discriminator(input_ids=input_ids, attention_mask=attention_mask)
        print(f"   ✓ Discriminator output shape: {outputs.shape}")
        
        print("\n🎉 All tests passed! DistilBERT compatibility fixed.")
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_memory_optimized_config():
    """Test memory optimized configuration"""
    print("\n" + "="*50)
    print("TESTING MEMORY OPTIMIZED CONFIGURATION")
    print("="*50)
    
    config = get_memory_optimized_config()
    
    print(f"Data configuration:")
    print(f"  - Clients: {config.data.num_clients}")
    print(f"  - Samples per client: {config.data.samples_per_client}")
    print(f"  - Max sequence length: {config.data.max_seq_length}")
    
    print(f"\nModel configuration:")
    print(f"  - Teacher model: {config.model.teacher_model}")
    print(f"  - Student model: {config.model.student_model}")
    print(f"  - Generator model: {config.model.generator_model}")
    
    print(f"\nTraining configuration:")
    print(f"  - Local batch size: {config.training.local_batch_size}")
    print(f"  - Distillation batch size: {config.training.distillation_batch_size}")
    print(f"  - Federated rounds: {config.training.federated_rounds}")
    print(f"  - Distillation rounds: {config.training.distillation_rounds}")
    
    return True

if __name__ == "__main__":
    print("D³AFD Model Compatibility Test")
    print("="*50)
    
    # Test memory optimized config
    test_memory_optimized_config()
    
    # Test model compatibility
    success = test_models()
    
    if success:
        print("\n✅ All tests passed!")
        print("\nYou can now run:")
        print("  python main.py --memory_optimized")
        print("  python run_memory_optimized.py")
    else:
        print("\n❌ Tests failed. Please check the error messages above.")
