2025-08-03 12:17:00,879 - __main__ - INFO - T5质量测试日志初始化完成. 日志文件: logs/t5_quality_test_20250803_121700.log
2025-08-03 12:17:00,879 - __main__ - INFO - 开始T5文本生成器质量测试
2025-08-03 12:17:00,879 - __main__ - INFO - ============================================================
2025-08-03 12:17:02,309 - __main__ - INFO - 使用设备: cuda
2025-08-03 12:17:02,309 - __main__ - INFO - 生成器模型: t5-small
2025-08-03 12:17:02,309 - __main__ - INFO - 步骤 1: 加载原始数据
2025-08-03 12:17:02,309 - __main__ - INFO - 加载原始Amazon评论数据...
2025-08-03 12:17:12,354 - __main__ - ERROR - 测试过程中发生错误: (MaxRetryError('HTTPSConnectionPool(host=\'hf-mirror.com\', port=443): Max retries exceeded with url: /bert-base-uncased/resolve/main/tokenizer_config.json (Caused by ProxyError(\'Unable to connect to proxy\', ReadTimeoutError("HTTPSConnectionPool(host=\'hf-mirror.com\', port=443): Read timed out. (read timeout=10)")))'), '(Request ID: c2f8d192-ff1a-455c-b49b-b42befbc3fe1)')
2025-08-03 12:17:12,463 - __main__ - ERROR - Traceback (most recent call last):
  File "D:\anaconda\envs\pytorch\lib\site-packages\urllib3\connectionpool.py", line 779, in urlopen
    self._prepare_proxy(conn)
  File "D:\anaconda\envs\pytorch\lib\site-packages\urllib3\connectionpool.py", line 1048, in _prepare_proxy
    conn.connect()
  File "D:\anaconda\envs\pytorch\lib\site-packages\urllib3\connection.py", line 625, in connect
    self.sock = sock = self._connect_tls_proxy(self.host, sock)
  File "D:\anaconda\envs\pytorch\lib\site-packages\urllib3\connection.py", line 699, in _connect_tls_proxy
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "D:\anaconda\envs\pytorch\lib\site-packages\urllib3\connection.py", line 806, in _ssl_wrap_socket_and_match_hostname
    ssl_sock = ssl_wrap_socket(
  File "D:\anaconda\envs\pytorch\lib\site-packages\urllib3\util\ssl_.py", line 465, in ssl_wrap_socket
    ssl_sock = _ssl_wrap_socket_impl(sock, context, tls_in_tls, server_hostname)
  File "D:\anaconda\envs\pytorch\lib\site-packages\urllib3\util\ssl_.py", line 509, in _ssl_wrap_socket_impl
    return ssl_context.wrap_socket(sock, server_hostname=server_hostname)
  File "D:\anaconda\envs\pytorch\lib\ssl.py", line 500, in wrap_socket
    return self.sslsocket_class._create(
  File "D:\anaconda\envs\pytorch\lib\ssl.py", line 1040, in _create
    self.do_handshake()
  File "D:\anaconda\envs\pytorch\lib\ssl.py", line 1309, in do_handshake
    self._sslobj.do_handshake()
socket.timeout: _ssl.c:1112: The handshake operation timed out

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\anaconda\envs\pytorch\lib\site-packages\urllib3\connectionpool.py", line 781, in urlopen
    self._raise_timeout(
  File "D:\anaconda\envs\pytorch\lib\site-packages\urllib3\connectionpool.py", line 370, in _raise_timeout
    raise ReadTimeoutError(
urllib3.exceptions.ReadTimeoutError: HTTPSConnectionPool(host='hf-mirror.com', port=443): Read timed out. (read timeout=10)

The above exception was the direct cause of the following exception:

urllib3.exceptions.ProxyError: ('Unable to connect to proxy', ReadTimeoutError("HTTPSConnectionPool(host='hf-mirror.com', port=443): Read timed out. (read timeout=10)"))

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\anaconda\envs\pytorch\lib\site-packages\requests\adapters.py", line 667, in send
    resp = conn.urlopen(
  File "D:\anaconda\envs\pytorch\lib\site-packages\urllib3\connectionpool.py", line 847, in urlopen
    retries = retries.increment(
  File "D:\anaconda\envs\pytorch\lib\site-packages\urllib3\util\retry.py", line 515, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='hf-mirror.com', port=443): Max retries exceeded with url: /bert-base-uncased/resolve/main/tokenizer_config.json (Caused by ProxyError('Unable to connect to proxy', ReadTimeoutError("HTTPSConnectionPool(host='hf-mirror.com', port=443): Read timed out. (read timeout=10)")))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "e:\amazon-re-fd\test_t5_generator_quality.py", line 360, in main
    original_data = load_original_data(config)
  File "e:\amazon-re-fd\test_t5_generator_quality.py", line 76, in load_original_data
    data_processor = AmazonDataProcessor(config)
  File "e:\amazon-re-fd\src\data\amazon_dataset.py", line 62, in __init__
    self.tokenizer = AutoTokenizer.from_pretrained(config.model.teacher_model)
  File "D:\anaconda\envs\pytorch\lib\site-packages\transformers\models\auto\tokenization_auto.py", line 817, in from_pretrained
    tokenizer_config = get_tokenizer_config(pretrained_model_name_or_path, **kwargs)
  File "D:\anaconda\envs\pytorch\lib\site-packages\transformers\models\auto\tokenization_auto.py", line 649, in get_tokenizer_config
    resolved_config_file = cached_file(
  File "D:\anaconda\envs\pytorch\lib\site-packages\transformers\utils\hub.py", line 399, in cached_file
    resolved_file = hf_hub_download(
  File "D:\anaconda\envs\pytorch\lib\site-packages\huggingface_hub\utils\_validators.py", line 114, in _inner_fn
    return fn(*args, **kwargs)
  File "D:\anaconda\envs\pytorch\lib\site-packages\huggingface_hub\file_download.py", line 1008, in hf_hub_download
    return _hf_hub_download_to_cache_dir(
  File "D:\anaconda\envs\pytorch\lib\site-packages\huggingface_hub\file_download.py", line 1071, in _hf_hub_download_to_cache_dir
    (url_to_download, etag, commit_hash, expected_size, xet_file_data, head_call_error) = _get_metadata_or_catch_error(
  File "D:\anaconda\envs\pytorch\lib\site-packages\huggingface_hub\file_download.py", line 1533, in _get_metadata_or_catch_error
    metadata = get_hf_file_metadata(
  File "D:\anaconda\envs\pytorch\lib\site-packages\huggingface_hub\utils\_validators.py", line 114, in _inner_fn
    return fn(*args, **kwargs)
  File "D:\anaconda\envs\pytorch\lib\site-packages\huggingface_hub\file_download.py", line 1450, in get_hf_file_metadata
    r = _request_wrapper(
  File "D:\anaconda\envs\pytorch\lib\site-packages\huggingface_hub\file_download.py", line 286, in _request_wrapper
    response = _request_wrapper(
  File "D:\anaconda\envs\pytorch\lib\site-packages\huggingface_hub\file_download.py", line 309, in _request_wrapper
    response = http_backoff(method=method, url=url, **params, retry_on_exceptions=(), retry_on_status_codes=(429,))
  File "D:\anaconda\envs\pytorch\lib\site-packages\huggingface_hub\utils\_http.py", line 310, in http_backoff
    response = session.request(method=method, url=url, **kwargs)
  File "D:\anaconda\envs\pytorch\lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "D:\anaconda\envs\pytorch\lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "D:\anaconda\envs\pytorch\lib\site-packages\huggingface_hub\utils\_http.py", line 96, in send
    return super().send(request, *args, **kwargs)
  File "D:\anaconda\envs\pytorch\lib\site-packages\requests\adapters.py", line 694, in send
    raise ProxyError(e, request=request)
requests.exceptions.ProxyError: (MaxRetryError('HTTPSConnectionPool(host=\'hf-mirror.com\', port=443): Max retries exceeded with url: /bert-base-uncased/resolve/main/tokenizer_config.json (Caused by ProxyError(\'Unable to connect to proxy\', ReadTimeoutError("HTTPSConnectionPool(host=\'hf-mirror.com\', port=443): Read timed out. (read timeout=10)")))'), '(Request ID: c2f8d192-ff1a-455c-b49b-b42befbc3fe1)')

