"""
Ultra-low memory D³AFD experiment runner
For extremely GPU-constrained environments (< 8GB GPU memory)
"""
import os
import sys
import logging
import torch
import gc

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.config import get_default_config
from src.federated.d3afd_framework import D3AFDFramework

def setup_ultra_low_memory_config():
    """Create ultra-low memory configuration"""
    config = get_default_config()
    
    # Minimal configuration
    config.data.num_clients = 2  # Only 2 clients
    config.data.samples_per_client = 50  # Very small datasets
    config.data.max_seq_length = 32  # Very short sequences
    
    # Use smallest possible models
    config.model.teacher_model = "distilbert-base-uncased"
    config.model.student_model = "distilbert-base-uncased"
    config.model.generator_model = "t5-small"
    config.model.generator_max_length = 16  # Very short generation
    
    # Minimal batch sizes
    config.training.local_batch_size = 1  # Batch size of 1
    config.training.distillation_batch_size = 2  # Batch size of 2
    
    # Minimal training iterations
    config.training.federated_rounds = 1  # Only 1 round for testing
    config.training.distillation_rounds = 1
    config.training.local_epochs = 1
    config.training.distillation_epochs = 1
    config.training.personalization_epochs = 1
    
    # Minimal pseudo data generation
    config.training.pseudo_samples_per_domain = 10  # Very few samples
    config.training.mixed_domain_samples = 5
    
    # Output settings
    config.experiment.output_dir = "ultra_low_memory_outputs"
    config.experiment.data_dir = "ultra_low_memory_data"
    
    return config

def clear_gpu_memory():
    """Clear GPU memory aggressively"""
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        torch.cuda.ipc_collect()
        gc.collect()

def setup_logging():
    """Setup logging"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )
    return logging.getLogger(__name__)

def main():
    """Main function for ultra-low memory experiment"""
    logger = setup_logging()
    
    logger.info("Starting Ultra-Low Memory D³AFD Experiment")
    logger.info("=" * 60)
    logger.info("WARNING: This is a minimal configuration for testing only!")
    logger.info("Results may not be representative of full algorithm performance.")
    
    # Set CUDA memory allocation environment variables
    os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'expandable_segments:True,max_split_size_mb:128'
    
    # Check GPU memory
    if torch.cuda.is_available():
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        logger.info(f"GPU Memory Available: {gpu_memory:.2f} GB")
        
        # Clear any existing GPU memory
        clear_gpu_memory()
        
        # Set very conservative memory fraction
        torch.cuda.set_per_process_memory_fraction(0.6)  # Use only 60% of GPU memory
        logger.info("Set GPU memory fraction to 60% for ultra-conservative usage")
    else:
        logger.info("CUDA not available, using CPU")
    
    # Get ultra-low memory config
    config = setup_ultra_low_memory_config()
    
    logger.info("Ultra-Low Memory Configuration:")
    logger.info(f"  Clients: {config.data.num_clients}")
    logger.info(f"  Samples per client: {config.data.samples_per_client}")
    logger.info(f"  Max sequence length: {config.data.max_seq_length}")
    logger.info(f"  Teacher model: {config.model.teacher_model}")
    logger.info(f"  Generator model: {config.model.generator_model}")
    logger.info(f"  Local batch size: {config.training.local_batch_size}")
    logger.info(f"  Federated rounds: {config.training.federated_rounds}")
    
    try:
        # Initialize framework
        logger.info("Initializing D³AFD Framework...")
        framework = D3AFDFramework(config)
        
        # Clear memory after initialization
        clear_gpu_memory()
        
        # Run training
        logger.info("Starting training...")
        results = framework.run_complete_training(force_reload_data=False)
        
        # Print results
        logger.info("Training completed successfully!")
        
        if 'global_model' in results:
            global_acc = results['global_model'].get('accuracy', 0)
            logger.info(f"Global model accuracy: {global_acc:.4f}")
        
        if 'personalized_models' in results:
            personal_accs = [r.get('accuracy', 0) for r in results['personalized_models'].values()]
            if personal_accs:
                avg_personal_acc = sum(personal_accs) / len(personal_accs)
                logger.info(f"Average personalized accuracy: {avg_personal_acc:.4f}")
        
        logger.info(f"Results saved to: {config.experiment.output_dir}")
        
    except torch.cuda.OutOfMemoryError as e:
        logger.error("CUDA Out of Memory Error!")
        logger.error("Even ultra-low memory configuration failed.")
        logger.error("Please try CPU-only mode: python run_cpu_demo.py")
        logger.error(f"Error details: {e}")
        
    except Exception as e:
        logger.error(f"Experiment failed: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Clean up GPU memory
        clear_gpu_memory()

if __name__ == "__main__":
    main()
