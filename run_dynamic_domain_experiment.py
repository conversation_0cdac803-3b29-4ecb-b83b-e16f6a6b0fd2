"""
动态域描述符联邦学习实验
实现真正的动态联邦学习：
教师模型训练 → 动态域描述符 → 动态伪数据 → 持续知识蒸馏
"""
import os
import sys
import torch
import logging
from datetime import datetime

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.config import get_default_config
from src.federated.d3afd_framework import D3AFDFramework

def setup_logging():
    """设置日志"""
    os.makedirs('logs', exist_ok=True)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_filename = f"logs/dynamic_domain_{timestamp}.log"
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_filename, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    logger = logging.getLogger(__name__)
    logger.info(f"动态域描述符实验日志初始化完成. 日志文件: {log_filename}")
    return logger

def get_dynamic_domain_config():
    """获取动态域描述符配置"""
    config = get_default_config()
    
    # 实验设置
    config.experiment.name = "dynamic_domain_descriptors"
    config.experiment.device = "cuda" if torch.cuda.is_available() else "cpu"
    config.experiment.seed = 42
    
    # Output settings
    config.experiment.output_dir = "dynamic_domain_outputs"
    config.experiment.data_dir = "dynamic_domain_data"
    
    # 数据配置 - 进一步减少内存使用
    config.data.domains = ['Books']  # 单域测试
    config.data.samples_per_domain = 200  # 大幅减少数据量
    config.data.max_seq_length = 64  # 极短序列
    config.data.train_split = 0.7
    config.data.val_split = 0.15
    config.data.test_split = 0.15

    # 联邦学习配置 - 最小化测试
    config.training.federated_rounds = 2  # 最少轮次
    config.training.distillation_rounds = 1  # 单轮蒸馏

    # 本地训练配置 - 最小化
    config.training.local_epochs = 1  # 单轮本地训练
    config.training.local_lr = 1e-3
    config.training.local_batch_size = 4  # 极小批次
    config.training.label_smoothing = 0.0  # 禁用
    config.training.weight_decay = 0.0  # 禁用
    
    # 知识蒸馏配置 - 优化参数
    config.training.temperature = 6.0  # 适中温度
    config.training.alpha = 0.4
    config.training.beta = 0.6
    
    # 全局训练配置 - 最小化
    config.training.distillation_epochs = 1  # 单轮全局训练
    config.training.distillation_lr = 1e-3
    config.training.distillation_batch_size = 4  # 极小批次

    # 伪数据配置 - 最小化
    config.training.pseudo_samples_per_domain = 8  # 极少伪数据
    config.training.mixed_domain_samples = 0  # 禁用混合域

    # 对比学习配置 - 禁用以节省内存
    config.training.contrastive_weight = 0.0  # 完全禁用
    config.training.contrastive_temperature = 0.1
    
    # 个性化配置 - 禁用
    config.training.personalization_epochs = 0  # 完全禁用个性化
    config.training.personalization_lr = 1e-4

    # 模型配置 - 最小化
    config.model.teacher_model = "distilbert-base-uncased"
    config.model.student_model = "distilbert-base-uncased"
    config.model.generator_model = "t5-small"
    config.model.generator_max_length = 32  # 极短生成
    
    return config

def main():
    """主函数"""
    logger = setup_logging()
    
    logger.info("🌟 动态域描述符联邦学习实验开始")
    logger.info("=" * 80)
    logger.info("🔄 核心创新 - 动态域描述符机制:")
    logger.info("   1. 📊 基于教师模型状态提取域特征")
    logger.info("   2. 🎯 生成动态域描述符 (中心、方差、密度、范围)")
    logger.info("   3. 🔄 每轮更新域描述符反映学习进展")
    logger.info("   4. 📝 基于域描述符生成自适应伪数据提示")
    logger.info("   5. 🌊 实现真正的动态知识蒸馏")
    
    logger.info("\n🎯 预期动态效果:")
    logger.info("   - 域描述符随教师模型训练而演化")
    logger.info("   - 伪数据提示根据域特征自适应调整")
    logger.info("   - DKD损失反映真实知识传递 (>0.01)")
    logger.info("   - 全局精度呈现学习曲线而非平线")
    logger.info("   - 每轮都有新的知识注入全局模型")
    
    logger.info("\n🔍 关键观察指标:")
    logger.info("   - 域描述符的演化趋势")
    logger.info("   - 域中心的移动轨迹")
    logger.info("   - 域密度和方差的变化")
    logger.info("   - 伪数据质量的动态提升")
    logger.info("   - 全局模型的持续学习")

    # 设置环境变量优化内存
    os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'expandable_segments:True'
    os.environ['TOKENIZERS_PARALLELISM'] = 'false'

    try:
        # 获取配置
        config = get_dynamic_domain_config()
        
        logger.info(f"\n📋 实验配置:")
        logger.info(f"   设备: {config.experiment.device}")
        logger.info(f"   联邦轮次: {config.training.federated_rounds}")
        logger.info(f"   蒸馏轮次: {config.training.distillation_rounds}")
        logger.info(f"   本地轮次: {config.training.local_epochs}")
        logger.info(f"   蒸馏温度: {config.training.temperature}")
        logger.info(f"   批次大小: {config.training.local_batch_size}")
        logger.info(f"   伪数据量: {config.training.pseudo_samples_per_domain}")

        # 预清理内存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            logger.info("预清理GPU内存完成")

        # 初始化框架
        logger.info("\n🏗️ 初始化动态域描述符D³AFD框架...")
        framework = D3AFDFramework(config)
        
        # 运行完整训练
        logger.info("\n🚀 开始动态域描述符联邦学习...")
        framework.run_complete_training()
        
        logger.info("\n🎉 动态域描述符实验完成!")
        logger.info("=" * 80)
        logger.info("📊 请检查以下动态指标:")
        logger.info("   1. 是否出现'生成动态域描述符'日志")
        logger.info("   2. 域描述符是否在每轮发生变化")
        logger.info("   3. DKD损失是否反映真实知识传递")
        logger.info("   4. 全局精度是否呈现学习曲线")
        logger.info("   5. 伪数据提示是否根据域特征调整")
        logger.info("   6. 域演化分析结果")
        
        logger.info("\n🔬 实验意义:")
        logger.info("   这是首个真正动态的联邦学习实现")
        logger.info("   解决了静态域描述符导致的学习停滞问题")
        logger.info("   为联邦学习的持续知识更新奠定基础")
        
        return 0

    except torch.cuda.OutOfMemoryError as e:
        logger.error(f"GPU内存不足: {e}")
        logger.error("建议:")
        logger.error("   - 进一步减少batch_size到2")
        logger.error("   - 减少序列长度到32")
        logger.error("   - 使用CPU模式运行")
        logger.error("   - 减少样本数量到100")
        return 1

    except Exception as e:
        logger.error(f"实验过程中发生错误: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
