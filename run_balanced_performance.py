"""
Balanced performance D³AFD experiment runner
Optimized for better accuracy while maintaining reasonable memory usage
"""
import os
import sys
import logging
import torch
import gc

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.config import get_balanced_performance_config
from src.federated.d3afd_framework import D3AFDFramework

def clear_gpu_memory():
    """Clear GPU memory"""
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        torch.cuda.ipc_collect()
        gc.collect()

def setup_logging():
    """Setup logging"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )
    return logging.getLogger(__name__)

def main():
    """Main function for balanced performance experiment"""
    logger = setup_logging()
    
    logger.info("Starting Balanced Performance D³AFD Experiment")
    logger.info("=" * 60)
    logger.info("🎯 Optimized for better accuracy with reasonable memory usage")
    
    # Set environment variables for memory optimization
    os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'expandable_segments:True,max_split_size_mb:256'
    os.environ['TOKENIZERS_PARALLELISM'] = 'false'  # Disable tokenizer warnings
    
    # Check GPU memory
    if torch.cuda.is_available():
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        logger.info(f"GPU Memory Available: {gpu_memory:.2f} GB")
        
        # Clear any existing GPU memory
        clear_gpu_memory()
        
        # Set memory fraction based on available memory
        if gpu_memory > 12:
            memory_fraction = 0.85
        elif gpu_memory > 8:
            memory_fraction = 0.80
        else:
            memory_fraction = 0.75
            
        torch.cuda.set_per_process_memory_fraction(memory_fraction)
        logger.info(f"Set GPU memory fraction to {memory_fraction*100:.0f}%")
    else:
        logger.info("CUDA not available, using CPU")
    
    # Get balanced performance config
    config = get_balanced_performance_config()
    
    logger.info("Balanced Performance Configuration:")
    logger.info(f"  Clients: {config.data.num_clients}")
    logger.info(f"  Samples per client: {config.data.samples_per_client}")
    logger.info(f"  Max sequence length: {config.data.max_seq_length}")
    logger.info(f"  Teacher model: {config.model.teacher_model}")
    logger.info(f"  Generator model: {config.model.generator_model}")
    logger.info(f"  Local batch size: {config.training.local_batch_size}")
    logger.info(f"  Federated rounds: {config.training.federated_rounds}")
    logger.info(f"  Distillation rounds: {config.training.distillation_rounds}")
    logger.info(f"  Pseudo samples per domain: {config.training.pseudo_samples_per_domain}")
    
    # Set output directory
    config.experiment.output_dir = "balanced_performance_outputs"
    config.experiment.data_dir = "balanced_performance_data"
    
    try:
        # Initialize framework
        logger.info("Initializing D³AFD Framework...")
        framework = D3AFDFramework(config)
        
        # Clear memory after initialization
        clear_gpu_memory()
        
        # Run training
        logger.info("Starting training...")
        results = framework.run_complete_training(force_reload_data=False)
        
        # Print results
        logger.info("Training completed successfully!")
        logger.info("=" * 60)
        logger.info("📊 FINAL RESULTS:")
        
        if 'global_model' in results:
            global_acc = results['global_model'].get('accuracy', 0)
            global_f1 = results['global_model'].get('f1_macro', 0)
            logger.info(f"🌍 Global Model:")
            logger.info(f"   Accuracy: {global_acc:.4f} ({global_acc*100:.2f}%)")
            logger.info(f"   F1-Macro: {global_f1:.4f}")
        
        if 'personalized_models' in results:
            personal_accs = [r.get('accuracy', 0) for r in results['personalized_models'].values()]
            if personal_accs:
                avg_personal_acc = sum(personal_accs) / len(personal_accs)
                max_personal_acc = max(personal_accs)
                min_personal_acc = min(personal_accs)
                logger.info(f"👤 Personalized Models:")
                logger.info(f"   Average Accuracy: {avg_personal_acc:.4f} ({avg_personal_acc*100:.2f}%)")
                logger.info(f"   Best Client: {max_personal_acc:.4f} ({max_personal_acc*100:.2f}%)")
                logger.info(f"   Worst Client: {min_personal_acc:.4f} ({min_personal_acc*100:.2f}%)")
        
        logger.info("=" * 60)
        logger.info(f"📁 Results saved to: {config.experiment.output_dir}")
        
        # Performance analysis
        if 'global_model' in results:
            global_acc = results['global_model'].get('accuracy', 0)
            if global_acc < 0.5:
                logger.warning("⚠️  Global accuracy is still low. Consider:")
                logger.warning("   - Increasing federated rounds (current: {})".format(config.training.federated_rounds))
                logger.warning("   - Increasing samples per client (current: {})".format(config.data.samples_per_client))
                logger.warning("   - Using real dataset instead of synthetic data")
                logger.warning("   - Increasing model size if memory allows")
            else:
                logger.info("✅ Good performance achieved!")
        
    except torch.cuda.OutOfMemoryError as e:
        logger.error("CUDA Out of Memory Error!")
        logger.error("Try using the memory-optimized configuration:")
        logger.error("python run_memory_optimized.py")
        logger.error(f"Error details: {e}")
        
    except Exception as e:
        logger.error(f"Experiment failed: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Clean up GPU memory
        clear_gpu_memory()

if __name__ == "__main__":
    main()
