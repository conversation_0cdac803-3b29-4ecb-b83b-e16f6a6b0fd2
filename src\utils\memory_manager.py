"""
GPU Memory Management Utilities
"""
import torch
import gc
import logging
from typing import Optional, Dict, Any
import psutil
import time

logger = logging.getLogger(__name__)

class GPUMemoryManager:
    """GPU Memory Manager for efficient memory usage"""
    
    def __init__(self, device: str = 'cuda'):
        self.device = device
        self.peak_memory = 0
        self.memory_history = []
        
    def clear_cache(self, force: bool = False):
        """Clear GPU cache"""
        if torch.cuda.is_available():
            if force:
                # Force garbage collection multiple times
                for _ in range(3):
                    gc.collect()
                    torch.cuda.empty_cache()
                    torch.cuda.ipc_collect()
                time.sleep(0.1)  # Small delay to ensure cleanup
            else:
                gc.collect()
                torch.cuda.empty_cache()
    
    def get_memory_info(self) -> Dict[str, float]:
        """Get current memory information"""
        if not torch.cuda.is_available():
            return {"allocated": 0, "reserved": 0, "free": 0, "total": 0}
        
        allocated = torch.cuda.memory_allocated(0) / 1024**3
        reserved = torch.cuda.memory_reserved(0) / 1024**3
        total = torch.cuda.get_device_properties(0).total_memory / 1024**3
        free = total - reserved
        
        # Update peak memory
        self.peak_memory = max(self.peak_memory, reserved)
        
        return {
            "allocated": allocated,
            "reserved": reserved,
            "free": free,
            "total": total,
            "peak": self.peak_memory
        }
    
    def log_memory_usage(self, context: str = ""):
        """Log current memory usage"""
        info = self.get_memory_info()
        logger.info(f"GPU Memory {context}: "
                   f"Allocated: {info['allocated']:.2f}GB, "
                   f"Reserved: {info['reserved']:.2f}GB, "
                   f"Free: {info['free']:.2f}GB")
        
        # Store in history
        self.memory_history.append({
            "context": context,
            "timestamp": time.time(),
            **info
        })
    
    def check_memory_threshold(self, threshold: float = 0.9) -> bool:
        """Check if memory usage exceeds threshold"""
        info = self.get_memory_info()
        usage_ratio = info['reserved'] / info['total']
        
        if usage_ratio > threshold:
            logger.warning(f"High memory usage: {usage_ratio:.2%} > {threshold:.2%}")
            return True
        return False
    
    def force_cleanup_if_needed(self, threshold: float = 0.85):
        """Force cleanup if memory usage is high"""
        if self.check_memory_threshold(threshold):
            logger.info("Forcing memory cleanup due to high usage")
            self.clear_cache(force=True)
            self.log_memory_usage("after_force_cleanup")
    
    def get_memory_summary(self) -> str:
        """Get memory usage summary"""
        info = self.get_memory_info()
        return (f"Memory Summary - "
                f"Used: {info['reserved']:.2f}GB/{info['total']:.2f}GB "
                f"({info['reserved']/info['total']:.1%}), "
                f"Peak: {info['peak']:.2f}GB")

class ModelMemoryManager:
    """Memory manager for model operations"""
    
    def __init__(self, gpu_manager: GPUMemoryManager):
        self.gpu_manager = gpu_manager
        self.active_models = {}
    
    def load_model_safely(self, model_name: str, model_loader_func, *args, **kwargs):
        """Load model with memory management"""
        self.gpu_manager.log_memory_usage(f"before_loading_{model_name}")
        
        # Check if we have enough memory
        if self.gpu_manager.check_memory_threshold(0.8):
            logger.warning(f"High memory usage before loading {model_name}, cleaning up")
            self.cleanup_inactive_models()
            self.gpu_manager.clear_cache(force=True)
        
        # Load model
        model = model_loader_func(*args, **kwargs)
        self.active_models[model_name] = model
        
        self.gpu_manager.log_memory_usage(f"after_loading_{model_name}")
        return model
    
    def unload_model(self, model_name: str):
        """Unload model and free memory"""
        if model_name in self.active_models:
            del self.active_models[model_name]
            self.gpu_manager.clear_cache()
            logger.info(f"Unloaded model: {model_name}")
    
    def cleanup_inactive_models(self):
        """Clean up models that are no longer needed"""
        for model_name in list(self.active_models.keys()):
            self.unload_model(model_name)
        self.gpu_manager.clear_cache(force=True)
        logger.info("Cleaned up all inactive models")

class BatchMemoryManager:
    """Memory manager for batch processing"""
    
    def __init__(self, gpu_manager: GPUMemoryManager):
        self.gpu_manager = gpu_manager
    
    def process_batch_safely(self, batch_func, batch_data, max_retries: int = 3):
        """Process batch with memory management and retry logic"""
        for attempt in range(max_retries):
            try:
                # Check memory before processing
                self.gpu_manager.force_cleanup_if_needed(0.85)
                
                # Process batch
                result = batch_func(batch_data)
                
                # Clean up after processing
                self.gpu_manager.clear_cache()
                
                return result
                
            except torch.cuda.OutOfMemoryError as e:
                logger.warning(f"OOM on attempt {attempt + 1}/{max_retries}: {e}")
                
                if attempt < max_retries - 1:
                    # Force cleanup and retry
                    self.gpu_manager.clear_cache(force=True)
                    time.sleep(1)  # Wait a bit before retry
                else:
                    # Final attempt failed
                    raise e
    
    def split_large_batch(self, batch_data, max_batch_size: int):
        """Split large batch into smaller chunks"""
        if isinstance(batch_data, dict):
            batch_size = len(next(iter(batch_data.values())))
        else:
            batch_size = len(batch_data)
        
        if batch_size <= max_batch_size:
            return [batch_data]
        
        chunks = []
        for i in range(0, batch_size, max_batch_size):
            if isinstance(batch_data, dict):
                chunk = {k: v[i:i+max_batch_size] for k, v in batch_data.items()}
            else:
                chunk = batch_data[i:i+max_batch_size]
            chunks.append(chunk)
        
        logger.info(f"Split batch of size {batch_size} into {len(chunks)} chunks")
        return chunks

# Global memory manager instance
_global_memory_manager = None

def get_memory_manager() -> GPUMemoryManager:
    """Get global memory manager instance"""
    global _global_memory_manager
    if _global_memory_manager is None:
        _global_memory_manager = GPUMemoryManager()
    return _global_memory_manager

def clear_gpu_memory():
    """Convenience function to clear GPU memory"""
    manager = get_memory_manager()
    manager.clear_cache(force=True)

def log_memory_usage(context: str = ""):
    """Convenience function to log memory usage"""
    manager = get_memory_manager()
    manager.log_memory_usage(context)

def memory_cleanup_decorator(func):
    """Decorator to automatically clean up memory after function execution"""
    def wrapper(*args, **kwargs):
        manager = get_memory_manager()
        manager.log_memory_usage(f"before_{func.__name__}")
        
        try:
            result = func(*args, **kwargs)
            return result
        finally:
            manager.clear_cache()
            manager.log_memory_usage(f"after_{func.__name__}")
    
    return wrapper
