# D³AFD 内存优化指南

由于D³AFD算法涉及多个大型Transformer模型（T5、BERT等），在GPU内存有限的环境中可能遇到内存不足的问题。本指南提供了几种解决方案。

## 🚨 内存不足问题

如果遇到以下错误：
```
CUDA out of memory. Tried to allocate 24.00 MiB. GPU 0 has a total capacity of 14.58 GiB...
```

## 💡 解决方案

### 方案1：使用内存优化配置

```bash
# 使用内存优化配置（推荐用于8-16GB GPU）
python main.py --memory_optimized

# 或者使用专门的内存优化脚本
python run_memory_optimized.py
```

**内存优化配置特点：**
- 使用较小的模型（DistilBERT + T5-small）
- 减少客户端数量（5个）
- 减少批次大小（8/16）
- 减少序列长度（256）
- 减少训练轮次

### 方案2：使用CPU配置

```bash
# 使用CPU配置（适用于GPU内存不足的情况）
python main.py --cpu_only

# 或者使用专门的CPU演示脚本
python run_cpu_demo.py
```

**CPU配置特点：**
- 强制使用CPU
- 最小化配置（2-3个客户端）
- 极小的批次大小（4/8）
- 最短的序列长度（128）
- 最少的训练轮次

### 方案3：手动调整配置

编辑 `src/config.py` 或创建自定义配置：

```python
from src.config import get_default_config

config = get_default_config()

# 减少模型大小
config.model.teacher_model = "distilbert-base-uncased"
config.model.student_model = "distilbert-base-uncased"
config.model.generator_model = "t5-small"

# 减少批次大小
config.training.local_batch_size = 4
config.training.distillation_batch_size = 8

# 减少客户端数量
config.data.num_clients = 3

# 减少序列长度
config.data.max_seq_length = 128
```

## 📊 配置对比

| 配置类型 | GPU内存需求 | 客户端数 | 模型大小 | 训练时间 | 性能 |
|---------|------------|---------|----------|----------|------|
| 默认配置 | >16GB | 10 | BERT+T5-base | 长 | 最佳 |
| 内存优化 | 8-16GB | 5 | DistilBERT+T5-small | 中等 | 良好 |
| CPU配置 | 无GPU要求 | 2-3 | DistilBERT+T5-small | 很长 | 基本 |

## 🔧 其他优化技巧

### 1. 设置GPU内存分配
```bash
export PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True
```

### 2. 使用梯度累积
在代码中可以实现梯度累积来模拟更大的批次：

```python
# 在训练循环中
accumulation_steps = 4
for i, batch in enumerate(dataloader):
    loss = model(batch)
    loss = loss / accumulation_steps
    loss.backward()
    
    if (i + 1) % accumulation_steps == 0:
        optimizer.step()
        optimizer.zero_grad()
```

### 3. 使用混合精度训练
```python
from torch.cuda.amp import autocast, GradScaler

scaler = GradScaler()

with autocast():
    outputs = model(inputs)
    loss = criterion(outputs, targets)

scaler.scale(loss).backward()
scaler.step(optimizer)
scaler.update()
```

## 🚀 推荐使用方式

1. **首次尝试**：使用内存优化配置
   ```bash
   python main.py --memory_optimized --federated_rounds 3
   ```

2. **如果仍然内存不足**：使用CPU配置
   ```bash
   python run_cpu_demo.py
   ```

3. **有充足GPU内存**：使用默认配置
   ```bash
   python main.py --federated_rounds 10
   ```

## 📝 注意事项

- CPU训练会显著增加训练时间，但可以验证算法正确性
- 内存优化配置在保持算法完整性的同时减少资源需求
- 可以根据具体硬件环境进一步调整参数
- 建议先用小规模配置验证代码正确性，再扩展到大规模实验

## 🔍 监控内存使用

```python
import torch

# 检查GPU内存使用
if torch.cuda.is_available():
    print(f"GPU Memory: {torch.cuda.memory_allocated()/1024**3:.2f} GB")
    print(f"GPU Memory Cached: {torch.cuda.memory_reserved()/1024**3:.2f} GB")
```

通过这些优化方案，您应该能够在各种硬件环境中成功运行D³AFD算法！
