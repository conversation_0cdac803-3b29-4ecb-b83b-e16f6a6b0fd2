2025-08-03 10:52:10,451 - __main__ - INFO - SGD-fixed experiment logging initialized. Log file: logs/sgd_fixed_20250803_105210.log
2025-08-03 10:52:10,451 - __main__ - INFO - Starting SGD-Fixed D³AFD Experiment
2025-08-03 10:52:10,451 - __main__ - INFO - ======================================================================
2025-08-03 10:52:10,451 - __main__ - INFO - 🔧 FUNDAMENTAL FIXES APPLIED:
2025-08-03 10:52:10,451 - __main__ - INFO -    - MORE local training (5 epochs) to ensure learning
2025-08-03 10:52:10,451 - __main__ - INFO -    - Moderate global training (3 epochs) for stability
2025-08-03 10:52:10,451 - __main__ - INFO -    - Higher local LR (1e-3) to enable actual learning
2025-08-03 10:52:10,451 - __main__ - INFO -    - Moderate global LR (2e-3) for steady improvement
2025-08-03 10:52:10,451 - __main__ - INFO -    - MINIMAL regularization to allow learning
2025-08-03 10:52:10,451 - __main__ - INFO -    - Fewer but quality pseudo data (40/domain)
2025-08-03 10:52:10,451 - __main__ - INFO -    - Focus on LOCAL learning first, then knowledge transfer
2025-08-03 10:52:10,451 - __main__ - INFO -    - Relaxed early stopping to allow learning
2025-08-03 10:52:10,451 - __main__ - INFO -    - Conservative memory management
2025-08-03 10:52:11,844 - __main__ - INFO - GPU Memory Available: 8.00 GB
2025-08-03 10:52:11,923 - __main__ - INFO - Set GPU memory fraction to 60% (very conservative)
2025-08-03 10:52:12,184 - src.utils.memory_manager - INFO - GPU Memory initial: Allocated: 0.00GB, Reserved: 0.00GB, Free: 8.00GB
2025-08-03 10:52:12,184 - __main__ - INFO - SGD-Fixed Configuration:
2025-08-03 10:52:12,184 - __main__ - INFO -   🏢 Clients: 4
2025-08-03 10:52:12,184 - __main__ - INFO -   📊 Samples per client: 200
2025-08-03 10:52:12,184 - __main__ - INFO -   📏 Max sequence length: 128
2025-08-03 10:52:12,184 - __main__ - INFO -   🔄 Federated rounds: 5
2025-08-03 10:52:12,189 - __main__ - INFO -   🧪 Distillation rounds: 3
2025-08-03 10:52:12,189 - __main__ - INFO -   🎭 Pseudo samples per domain: 40
2025-08-03 10:52:12,189 - __main__ - INFO -   📦 Local batch size: 8
2025-08-03 10:52:12,189 - __main__ - INFO -   📦 Distillation batch size: 16
2025-08-03 10:52:12,189 - __main__ - INFO -   📈 Local LR: 0.0006 (FUNDAMENTAL: higher to enable learning)
2025-08-03 10:52:12,189 - __main__ - INFO -   📈 Distillation LR: 0.002 (MODERATE: steady global learning)
2025-08-03 10:52:12,189 - __main__ - INFO -   🛡️  Dropout: 0.1 (MINIMAL: allow learning)
2025-08-03 10:52:12,189 - __main__ - INFO -   🏷️  Label smoothing: 0.05 (MINIMAL: allow learning)
2025-08-03 10:52:12,189 - __main__ - INFO -   📚 Local epochs: 5 (MORE: ensure local learning)
2025-08-03 10:52:12,189 - __main__ - INFO -   🌍 Distillation epochs: 3 (MODERATE: balanced approach)
2025-08-03 10:52:12,189 - __main__ - INFO - Initializing D³AFD Framework...
2025-08-03 10:52:14,723 - src.federated.server - INFO - Federated server initialized
2025-08-03 10:52:15,010 - src.federated.d3afd_framework - INFO - D³AFD Framework initialized
2025-08-03 10:52:15,010 - src.utils.memory_manager - INFO - GPU Memory after_initialization: Allocated: 0.47GB, Reserved: 0.50GB, Free: 7.50GB
2025-08-03 10:52:15,234 - __main__ - INFO - Starting SGD-fixed training...
2025-08-03 10:52:15,234 - __main__ - INFO - 🎯 FUNDAMENTAL STRATEGY - Expected improvements:
2025-08-03 10:52:15,234 - __main__ - INFO -    - ENSURE local models learn basic knowledge first (>40% accuracy)
2025-08-03 10:52:15,234 - __main__ - INFO -    - THEN transfer meaningful knowledge to global model
2025-08-03 10:52:15,234 - __main__ - INFO -    - Target: Local accuracy >40%, Global accuracy >30%
2025-08-03 10:52:15,234 - __main__ - INFO -    - Meaningful DKD loss (>0.01, not near 0)
2025-08-03 10:52:15,234 - __main__ - INFO -    - Decreasing contrastive loss (better feature learning)
2025-08-03 10:52:15,234 - __main__ - INFO -    - Progressive improvement across federated rounds
2025-08-03 10:52:15,234 - __main__ - INFO -    - Stable memory usage without OOM
2025-08-03 10:52:15,234 - src.federated.d3afd_framework - INFO - Starting complete D³AFD training process...
2025-08-03 10:52:15,234 - src.federated.d3afd_framework - INFO - Following D³AFD four-stage design: Local Training → Pseudo Data → DKD Distillation → Personalization
2025-08-03 10:52:15,234 - src.federated.d3afd_framework - INFO - Setting up federated data distribution...
2025-08-03 10:52:15,234 - src.federated.d3afd_framework - INFO - Loading existing federated data split...
2025-08-03 10:52:15,249 - src.data.amazon_dataset - INFO - Loaded federated data for 4 clients
2025-08-03 10:52:15,249 - src.federated.d3afd_framework - INFO - Federated data setup complete:
2025-08-03 10:52:15,249 - src.federated.d3afd_framework - INFO -   - Total clients: 4
2025-08-03 10:52:15,249 - src.federated.d3afd_framework - INFO -   - Domain distribution: {'Books': 300, 'Electronics': 300, 'Home_and_Kitchen': 200}
2025-08-03 10:52:15,249 - src.federated.d3afd_framework - INFO - Initializing federated clients...
2025-08-03 10:52:22,056 - src.federated.client - INFO - Client 0 initialized with domains: ['Books']
2025-08-03 10:52:22,973 - src.federated.client - INFO - Client 0: Models initialized
2025-08-03 10:52:22,973 - src.federated.server - INFO - Registered client 0 with domains: ['Books']
2025-08-03 10:52:22,973 - src.federated.d3afd_framework - INFO - Client 0 initialized with 200 samples
2025-08-03 10:52:29,707 - src.federated.client - INFO - Client 1 initialized with domains: ['Electronics']
2025-08-03 10:52:30,703 - src.federated.client - INFO - Client 1: Models initialized
2025-08-03 10:52:30,703 - src.federated.server - INFO - Registered client 1 with domains: ['Electronics']
2025-08-03 10:52:30,703 - src.federated.d3afd_framework - INFO - Client 1 initialized with 200 samples
2025-08-03 10:52:37,441 - src.federated.client - INFO - Client 2 initialized with domains: ['Home_and_Kitchen']
2025-08-03 10:52:38,495 - src.federated.client - INFO - Client 2: Models initialized
2025-08-03 10:52:38,495 - src.federated.server - INFO - Registered client 2 with domains: ['Home_and_Kitchen']
2025-08-03 10:52:38,495 - src.federated.d3afd_framework - INFO - Client 2 initialized with 200 samples
2025-08-03 10:52:45,666 - src.federated.client - INFO - Client 3 initialized with domains: ['Electronics', 'Books']
2025-08-03 10:52:46,674 - src.federated.client - INFO - Client 3: Models initialized
2025-08-03 10:52:46,674 - src.federated.server - INFO - Registered client 3 with domains: ['Electronics', 'Books']
2025-08-03 10:52:46,674 - src.federated.d3afd_framework - INFO - Client 3 initialized with 200 samples
2025-08-03 10:52:46,674 - src.federated.d3afd_framework - INFO - Initialized 4 clients
2025-08-03 10:52:46,674 - src.federated.d3afd_framework - INFO - === Initial Setup Phase ===
2025-08-03 10:52:46,674 - src.federated.d3afd_framework - INFO - Training initial local teacher models...
2025-08-03 10:52:46,674 - src.federated.client - INFO - Client 0: Training local teacher for 5 epochs
2025-08-03 10:52:57,290 - src.federated.client - INFO - Client 0 - Epoch 1/5: Loss: 1.5635, Accuracy: 0.3071
2025-08-03 10:53:05,672 - src.federated.client - INFO - Client 0 - Epoch 2/5: Loss: 1.2971, Accuracy: 0.7643
2025-08-03 10:53:14,053 - src.federated.client - INFO - Client 0 - Epoch 3/5: Loss: 0.9680, Accuracy: 1.0000
2025-08-03 10:53:22,448 - src.federated.client - INFO - Client 0 - Epoch 4/5: Loss: 0.7259, Accuracy: 1.0000
2025-08-03 10:53:22,448 - src.federated.client - WARNING - Client 0: Early stopping due to potential overfitting (accuracy: 1.0000)
2025-08-03 10:53:22,448 - src.federated.client - INFO - Client 0: Local teacher training completed. Final accuracy: 0.7679
2025-08-03 10:53:22,448 - src.federated.d3afd_framework - INFO - Client 0 teacher training: Accuracy 0.7679
2025-08-03 10:53:22,448 - src.federated.client - INFO - Client 1: Training local teacher for 5 epochs
2025-08-03 10:53:30,652 - src.federated.client - INFO - Client 1 - Epoch 1/5: Loss: 1.5296, Accuracy: 0.3929
2025-08-03 10:53:38,870 - src.federated.client - INFO - Client 1 - Epoch 2/5: Loss: 1.2668, Accuracy: 0.8643
