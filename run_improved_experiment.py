"""
Improved D³AFD experiment runner
Addresses low accuracy issues by using better configuration
"""
import os
import sys
import logging
import torch
import gc

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.config import get_default_config
from src.federated.d3afd_framework import D3AFDFramework

def setup_improved_config():
    """Create improved configuration for better accuracy"""
    config = get_default_config()

    # Improve data configuration (memory-conscious)
    config.data.num_clients = 6  # Good balance for federated learning
    config.data.samples_per_client = 400  # More samples but manageable
    config.data.max_seq_length = 256  # Reasonable length for memory efficiency

    # Use efficient but capable models
    config.model.teacher_model = "distilbert-base-uncased"  # Memory-efficient
    config.model.student_model = "distilbert-base-uncased"  # Same as teacher
    config.model.generator_model = "t5-small"  # Memory-efficient generator
    config.model.generator_max_length = 128  # Reasonable generation length

    # Optimize training parameters for better convergence
    config.training.local_batch_size = 2  # Very small batch for memory efficiency
    config.training.distillation_batch_size = 4  # Small batch for stability
    config.training.federated_rounds = 10  # More federated rounds
    config.training.distillation_rounds = 6  # More distillation rounds
    config.training.local_epochs = 4  # More local training
    config.training.distillation_epochs = 3  # More distillation epochs
    config.training.personalization_epochs = 4  # More personalization

    # Increase pseudo data moderately
    config.training.pseudo_samples_per_domain = 150  # More pseudo data but manageable
    config.training.mixed_domain_samples = 60  # More mixed samples

    # Optimize learning rates for better convergence
    config.training.local_lr = 2e-5  # Lower for stability
    config.training.distillation_lr = 5e-5  # Lower for better knowledge transfer
    config.training.personalization_lr = 1e-4  # Moderate for personalization

    # Add regularization
    config.training.weight_decay = 0.01  # L2 regularization
    config.training.dropout_rate = 0.1  # Dropout for generalization

    # Improve loss weights
    config.training.contrastive_weight = 0.5  # Increase contrastive learning
    config.training.dkd_alpha = 0.5  # Balance TCKD and NCKD
    config.training.dkd_beta = 0.5

    # Output settings
    config.experiment.output_dir = "high_accuracy_outputs"
    config.experiment.data_dir = "high_accuracy_data"

    return config

def clear_gpu_memory():
    """Clear GPU memory"""
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        torch.cuda.ipc_collect()
        gc.collect()

def setup_logging():
    """Setup logging with both console and file output"""
    # Create logs directory if it doesn't exist
    os.makedirs('logs', exist_ok=True)

    # Generate log filename with timestamp
    from datetime import datetime
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_filename = f"logs/improved_experiment_{timestamp}.log"

    # Setup logging with both file and console handlers
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_filename, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )

    logger = logging.getLogger(__name__)
    logger.info(f"Logging initialized. Log file: {log_filename}")
    return logger

def main():
    """Main function for improved experiment"""
    logger = setup_logging()

    logger.info("Starting Improved D³AFD Experiment")
    logger.info("=" * 60)
    logger.info("🎯 Addressing low accuracy issues with optimized configuration")
    logger.info("🔧 Applied fixes: DKD tensor shape issues, NaN handling, memory optimization")
    
    # Set environment variables
    os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'expandable_segments:True,max_split_size_mb:256'
    os.environ['TOKENIZERS_PARALLELISM'] = 'false'
    
    # Check GPU memory
    if torch.cuda.is_available():
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        logger.info(f"GPU Memory Available: {gpu_memory:.2f} GB")
        
        clear_gpu_memory()
        
        # Conservative memory usage
        torch.cuda.set_per_process_memory_fraction(0.80)
        logger.info("Set GPU memory fraction to 80%")
    else:
        logger.info("CUDA not available, using CPU")
    
    # Get improved config
    config = setup_improved_config()
    
    logger.info("Improved Configuration:")
    logger.info(f"  🏢 Clients: {config.data.num_clients}")
    logger.info(f"  📊 Samples per client: {config.data.samples_per_client}")
    logger.info(f"  📏 Max sequence length: {config.data.max_seq_length}")
    logger.info(f"  🤖 Teacher model: {config.model.teacher_model}")
    logger.info(f"  📝 Generator model: {config.model.generator_model}")
    logger.info(f"  🔄 Federated rounds: {config.training.federated_rounds}")
    logger.info(f"  🧪 Distillation rounds: {config.training.distillation_rounds}")
    logger.info(f"  🎭 Pseudo samples per domain: {config.training.pseudo_samples_per_domain}")
    logger.info(f"  📚 Local epochs: {config.training.local_epochs}")
    logger.info(f"  🎯 Personalization epochs: {config.training.personalization_epochs}")
    
    try:
        # Initialize framework
        logger.info("Initializing D³AFD Framework...")
        framework = D3AFDFramework(config)
        
        clear_gpu_memory()
        
        # Run training
        logger.info("Starting training...")
        logger.info("💡 Key improvements:")
        logger.info("   - More clients and samples for better learning")
        logger.info("   - More federated rounds for convergence")
        logger.info("   - More pseudo data for knowledge transfer")
        logger.info("   - Optimized learning rates")
        
        results = framework.run_complete_training(force_reload_data=False)
        
        # Print detailed results
        logger.info("Training completed successfully!")
        logger.info("=" * 60)
        logger.info("📊 DETAILED RESULTS:")
        
        if 'global_model' in results:
            global_acc = results['global_model'].get('accuracy', 0)
            global_f1 = results['global_model'].get('f1_macro', 0)
            logger.info(f"🌍 Global Model Performance:")
            logger.info(f"   Accuracy: {global_acc:.4f} ({global_acc*100:.2f}%)")
            logger.info(f"   F1-Macro: {global_f1:.4f}")
            
            # Performance assessment
            if global_acc > 0.7:
                logger.info("   ✅ Excellent performance!")
            elif global_acc > 0.5:
                logger.info("   ✅ Good performance!")
            elif global_acc > 0.3:
                logger.info("   ⚠️  Moderate performance - consider more training")
            else:
                logger.info("   ❌ Low performance - needs investigation")
        
        if 'personalized_models' in results:
            personal_accs = [r.get('accuracy', 0) for r in results['personalized_models'].values()]
            if personal_accs:
                avg_personal_acc = sum(personal_accs) / len(personal_accs)
                max_personal_acc = max(personal_accs)
                min_personal_acc = min(personal_accs)
                std_personal_acc = (sum([(x - avg_personal_acc)**2 for x in personal_accs]) / len(personal_accs))**0.5
                
                logger.info(f"👤 Personalized Models Performance:")
                logger.info(f"   Average: {avg_personal_acc:.4f} ({avg_personal_acc*100:.2f}%)")
                logger.info(f"   Best: {max_personal_acc:.4f} ({max_personal_acc*100:.2f}%)")
                logger.info(f"   Worst: {min_personal_acc:.4f} ({min_personal_acc*100:.2f}%)")
                logger.info(f"   Std Dev: {std_personal_acc:.4f}")
                
                # Personalization effectiveness
                if 'global_model' in results:
                    global_acc = results['global_model'].get('accuracy', 0)
                    improvement = avg_personal_acc - global_acc
                    logger.info(f"   Personalization Gain: {improvement:.4f} ({improvement*100:.2f}%)")
        
        logger.info("=" * 60)
        logger.info(f"📁 Results saved to: {config.experiment.output_dir}")
        
        # Recommendations
        if 'global_model' in results:
            global_acc = results['global_model'].get('accuracy', 0)
            if global_acc < 0.4:
                logger.info("💡 Recommendations for further improvement:")
                logger.info("   - Try using real Amazon Reviews dataset")
                logger.info("   - Increase federated rounds to 15-20")
                logger.info("   - Use larger models if memory allows")
                logger.info("   - Increase samples per client to 500+")
        
    except torch.cuda.OutOfMemoryError as e:
        logger.error("CUDA Out of Memory Error!")
        logger.error("Try the memory-optimized version: python run_memory_optimized.py")
        logger.error(f"Error details: {e}")
        return 1

    except Exception as e:
        logger.error(f"Experiment failed: {e}")
        logger.error("Full traceback:")
        import traceback
        logger.error(traceback.format_exc())
        return 1

    finally:
        clear_gpu_memory()
        logger.info("Experiment completed. Check the log file for detailed results.")
        return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
