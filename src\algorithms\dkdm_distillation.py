"""
DKDM (Data-Free Knowledge Distillation for Diffusion Models) Implementation for D³AFD
Based on dynamic iterative distillation with cached pseudo data generation
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import DataLoader
from typing import Dict, List, Optional, Tuple, Any
import logging
import pickle
import os
import random
import numpy as np
from pathlib import Path

from ..models.base_models import StudentModel
from ..utils.memory_manager import get_memory_manager, memory_cleanup_decorator
from ..utils.logger import get_logger

logger = get_logger(__name__)


class DKDMDistillationTrainer:
    """
    DKDM-based distillation trainer for D³AFD framework
    Implements dynamic iterative distillation without requiring original data
    """
    
    def __init__(self, config):
        self.config = config
        self.device = torch.device(config.experiment.device)
        
        # DKDM specific parameters
        self.num_timesteps = config.dkdm.num_timesteps if hasattr(config, 'dkdm') else 1000
        self.batch_ratio = config.dkdm.batch_ratio if hasattr(config, 'dkdm') else 0.4
        self.cache_dir = config.dkdm.cache_dir if hasattr(config, 'dkdm') else "cache/dkdm"
        self.distill_weight = config.dkdm.distill_weight if hasattr(config, 'dkdm') else 1.0
        
        # Create cache directory
        Path(self.cache_dir).mkdir(parents=True, exist_ok=True)
        
        # Dynamic iteration state
        self.total_t = None
        self.total_x_t_1 = None
        self.iteration_state = {}
        
        logger.info("DKDM distillation trainer initialized")
    
    def generate_pseudo_data_cache(self, teacher_models: Dict[str, nn.Module], 
                                 domains: List[str], cache_path: str) -> None:
        """
        Generate and cache pseudo data for dynamic iterative distillation
        
        Args:
            teacher_models: Dictionary of teacher models for each domain
            domains: List of domain names
            cache_path: Path to save the cache
        """
        logger.info("Generating DKDM pseudo data cache...")
        
        # Calculate total batch size
        total_batch_size = round(self.batch_ratio * self.num_timesteps) * self.config.training.batch_size
        
        # Generate time steps and noise states
        indices = list(range(self.num_timesteps))[::-1]
        
        # Initialize arrays for caching
        total_t = []
        total_x_t_1 = []
        
        # Generate pseudo data for each domain
        for domain in domains:
            if domain not in teacher_models:
                logger.warning(f"No teacher model found for domain {domain}, skipping")
                continue
                
            teacher_model = teacher_models[domain]
            teacher_model.eval()
            
            # Generate time steps
            domain_batch_size = total_batch_size // len(domains)
            t_values = torch.randint(0, self.num_timesteps, (domain_batch_size,))
            
            # Generate initial noise states (text embeddings for Amazon reviews)
            # For text data, we use embedding dimension instead of image dimensions
            embedding_dim = self.config.model.hidden_size
            x_t_1_values = torch.randn(domain_batch_size, embedding_dim)
            
            total_t.append(t_values)
            total_x_t_1.append(x_t_1_values)
        
        # Concatenate all domains
        total_t = torch.cat(total_t, dim=0)
        total_x_t_1 = torch.cat(total_x_t_1, dim=0)
        
        # Save cache
        cache_data = {
            'total_t': total_t.numpy(),
            'total_x_t_1': total_x_t_1.numpy(),
            'num_timesteps': self.num_timesteps,
            'domains': domains
        }
        
        with open(cache_path, 'wb') as f:
            pickle.dump(cache_data, f)
        
        logger.info(f"DKDM cache saved to {cache_path} with {len(total_t)} samples")
    
    def load_pseudo_data_cache(self, cache_path: str) -> bool:
        """
        Load cached pseudo data for dynamic iterative distillation
        
        Args:
            cache_path: Path to the cache file
            
        Returns:
            bool: True if cache loaded successfully, False otherwise
        """
        if not os.path.exists(cache_path):
            logger.warning(f"Cache file {cache_path} not found")
            return False
        
        try:
            with open(cache_path, 'rb') as f:
                cache_data = pickle.load(f)
            
            self.total_t = torch.from_numpy(cache_data['total_t']).to(self.device)
            self.total_x_t_1 = torch.from_numpy(cache_data['total_x_t_1']).to(self.device)
            
            logger.info(f"DKDM cache loaded from {cache_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to load cache: {e}")
            return False
    
    def dynamic_iterative_distillation(self, batch_size: int):
        """
        Generator for dynamic iterative distillation samples
        
        Args:
            batch_size: Batch size for sampling
            
        Yields:
            Tuple of (timesteps, embeddings, teacher_outputs)
        """
        if self.total_t is None or self.total_x_t_1 is None:
            raise RuntimeError("Pseudo data cache not loaded. Call load_pseudo_data_cache first.")
        
        total_batch_size = len(self.total_t)
        valid_indices = list(range(total_batch_size))
        
        while True:
            # Sample random indices
            random_index = random.sample(valid_indices, min(batch_size, len(valid_indices)))
            
            t = self.total_t[random_index].clone()
            x_t_1 = self.total_x_t_1[random_index].clone()
            
            # Check for t == 0 and reinitialize
            zero_index = torch.where(t == 0)[0]
            if len(zero_index) > 0:
                t[zero_index] = torch.tensor([self.num_timesteps-1] * len(zero_index), device=self.device)
                embedding_dim = x_t_1.shape[1]
                x_t_1[zero_index] = torch.randn(len(zero_index), embedding_dim, device=self.device)
            
            # Decrement time step
            t = t - 1
            x_t = x_t_1.contiguous()
            
            # Update cache
            self.total_t[random_index] = t
            self.total_x_t_1[random_index] = x_t_1
            
            yield t, x_t, x_t_1
    
    @memory_cleanup_decorator
    def train_student_model(self, student_model: nn.Module, teacher_models: Dict[str, nn.Module],
                          pseudo_dataloader: DataLoader, num_epochs: int = 3) -> Dict[str, float]:
        """
        Train student model using DKDM dynamic iterative distillation
        
        Args:
            student_model: Student model to train
            teacher_models: Dictionary of teacher models
            pseudo_dataloader: DataLoader for pseudo samples
            num_epochs: Number of training epochs
            
        Returns:
            Dictionary of training metrics
        """
        logger.info("Starting DKDM distillation training...")
        
        # Setup cache
        cache_path = os.path.join(self.cache_dir, f"dkdm_cache_{len(teacher_models)}_domains.pkl")
        
        if not self.load_pseudo_data_cache(cache_path):
            # Generate cache if not exists
            domains = list(teacher_models.keys())
            self.generate_pseudo_data_cache(teacher_models, domains, cache_path)
            self.load_pseudo_data_cache(cache_path)
        
        # Setup optimizer
        optimizer = torch.optim.AdamW(
            student_model.parameters(),
            lr=self.config.training.distillation_lr
        )
        
        student_model.train()
        total_loss = 0
        num_batches = 0
        
        # Dynamic iterative distillation
        distillation_generator = self.dynamic_iterative_distillation(self.config.training.batch_size)
        
        for epoch in range(num_epochs):
            epoch_loss = 0
            epoch_batches = 0
            
            # Process batches for this epoch
            for batch_idx in range(len(pseudo_dataloader)):
                try:
                    # Get dynamic samples
                    t, x_t, x_t_1 = next(distillation_generator)
                    
                    # Get student predictions
                    student_outputs = student_model(x_t)
                    
                    # Compute ensemble teacher predictions
                    teacher_outputs = []
                    for domain, teacher_model in teacher_models.items():
                        teacher_model.eval()
                        with torch.no_grad():
                            teacher_out = teacher_model(x_t)
                            teacher_outputs.append(teacher_out)
                    
                    # Average teacher outputs
                    ensemble_output = torch.stack(teacher_outputs).mean(dim=0)
                    
                    # Compute distillation loss
                    distill_loss = F.mse_loss(student_outputs, ensemble_output.detach())
                    
                    # Backward pass
                    optimizer.zero_grad()
                    distill_loss.backward()
                    optimizer.step()
                    
                    epoch_loss += distill_loss.item()
                    epoch_batches += 1
                    
                except StopIteration:
                    break
                except Exception as e:
                    logger.error(f"Error in DKDM training batch {batch_idx}: {e}")
                    continue
            
            avg_epoch_loss = epoch_loss / max(epoch_batches, 1)
            total_loss += avg_epoch_loss
            num_batches += 1
            
            logger.info(f"DKDM Epoch {epoch+1}/{num_epochs}, Loss: {avg_epoch_loss:.4f}")
        
        avg_loss = total_loss / max(num_batches, 1)
        
        return {
            'dkdm_loss': avg_loss,
            'algorithm': 'dkdm',
            'epochs': num_epochs,
            'total_batches': num_batches
        }
    
    def get_algorithm_info(self) -> Dict[str, Any]:
        """Get information about DKDM algorithm"""
        return {
            "name": "DKDM",
            "description": "Data-Free Knowledge Distillation for Diffusion Models",
            "supports_data_free": True,
            "supports_dynamic_iteration": True,
            "requires_cache": True,
            "num_timesteps": self.num_timesteps,
            "batch_ratio": self.batch_ratio
        }
