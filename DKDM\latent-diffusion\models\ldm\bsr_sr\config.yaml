model:
  base_learning_rate: 1.0e-06
  target: ldm.models.diffusion.ddpm.LatentDiffusion
  params:
    linear_start: 0.0015
    linear_end: 0.0155
    log_every_t: 100
    timesteps: 1000
    loss_type: l2
    first_stage_key: image
    cond_stage_key: LR_image
    image_size: 64
    channels: 3
    concat_mode: true
    cond_stage_trainable: false
    unet_config:
      target: ldm.modules.diffusionmodules.openaimodel.UNetModel
      params:
        image_size: 64
        in_channels: 6
        out_channels: 3
        model_channels: 160
        attention_resolutions:
        - 16
        - 8
        num_res_blocks: 2
        channel_mult:
        - 1
        - 2
        - 2
        - 4
        num_head_channels: 32
    first_stage_config:
      target: ldm.models.autoencoder.VQModelInterface
      params:
        embed_dim: 3
        n_embed: 8192
        monitor: val/rec_loss
        ddconfig:
          double_z: false
          z_channels: 3
          resolution: 256
          in_channels: 3
          out_ch: 3
          ch: 128
          ch_mult:
          - 1
          - 2
          - 4
          num_res_blocks: 2
          attn_resolutions: []
          dropout: 0.0
        lossconfig:
          target: torch.nn.Identity
    cond_stage_config:
      target: torch.nn.Identity
data:
  target: main.DataModuleFromConfig
  params:
    batch_size: 64
    wrap: false
    num_workers: 12
    train:
      target: ldm.data.openimages.SuperresOpenImagesAdvancedTrain
      params:
        size: 256
        degradation: bsrgan_light
        downscale_f: 4
        min_crop_f: 0.5
        max_crop_f: 1.0
        random_crop: true
    validation:
      target: ldm.data.openimages.SuperresOpenImagesAdvancedValidation
      params:
        size: 256
        degradation: bsrgan_light
        downscale_f: 4
        min_crop_f: 0.5
        max_crop_f: 1.0
        random_crop: true
