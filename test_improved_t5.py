"""
测试改进后的T5生成器质量
验证新的提示工程和后处理效果
"""
import os
import sys
import torch
import logging
import json
from datetime import datetime

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.config import get_default_config
from src.models.text_generator import T5TextGenerator

def setup_logging():
    """设置日志"""
    os.makedirs('logs', exist_ok=True)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_filename = f"logs/test_improved_t5_{timestamp}.log"
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_filename, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    logger = logging.getLogger(__name__)
    logger.info(f"改进T5测试日志初始化完成. 日志文件: {log_filename}")
    return logger

def test_generation_samples():
    """测试生成样本"""
    logger = logging.getLogger(__name__)
    logger.info("测试改进后的T5生成器...")
    
    # 获取配置
    config = get_default_config()
    config.model.generator_model = "t5-small"
    config.model.generator_max_length = 200
    config.experiment.device = "cuda" if torch.cuda.is_available() else "cpu"
    
    logger.info(f"使用设备: {config.experiment.device}")
    
    # 初始化生成器
    generator = T5TextGenerator(config)
    
    # 测试不同域和评分
    test_cases = [
        ('Books', 0, "负面书籍评论"),
        ('Books', 4, "正面书籍评论"),
        ('Electronics', 0, "负面电子产品评论"),
        ('Electronics', 4, "正面电子产品评论"),
        ('Home_and_Kitchen', 2, "中性厨房用品评论"),
    ]
    
    results = []
    
    for domain, rating, description in test_cases:
        logger.info(f"\n测试: {description}")
        logger.info(f"域: {domain}, 评分: {rating}")
        
        # 生成多个样本
        samples = generator.generate_pseudo_samples(
            domain=domain,
            num_samples=5,
            target_rating=rating
        )
        
        logger.info(f"生成了 {len(samples)} 个样本:")
        for i, sample in enumerate(samples, 1):
            logger.info(f"  样本 {i}: {sample['text']}")
            logger.info(f"    提示: {sample['prompt']}")
            logger.info(f"    长度: {len(sample['text'])} 字符, {len(sample['text'].split())} 词")
        
        results.append({
            'domain': domain,
            'rating': rating,
            'description': description,
            'samples': samples
        })
    
    return results

def analyze_quality(results):
    """分析生成质量"""
    logger = logging.getLogger(__name__)
    logger.info("\n分析生成质量...")
    
    total_samples = 0
    quality_metrics = {
        'avg_length': 0,
        'avg_words': 0,
        'empty_count': 0,
        'short_count': 0,
        'quality_issues': 0
    }
    
    all_texts = []
    
    for result in results:
        for sample in result['samples']:
            text = sample['text']
            all_texts.append(text)
            total_samples += 1
            
            # 统计指标
            quality_metrics['avg_length'] += len(text)
            words = text.split()
            quality_metrics['avg_words'] += len(words)
            
            # 质量检查
            if not text or len(text.strip()) == 0:
                quality_metrics['empty_count'] += 1
            elif len(words) < 5:
                quality_metrics['short_count'] += 1
            
            # 检查质量问题
            if any(ord(char) > 127 for char in text if char.isalpha()):
                quality_metrics['quality_issues'] += 1
    
    # 计算平均值
    if total_samples > 0:
        quality_metrics['avg_length'] /= total_samples
        quality_metrics['avg_words'] /= total_samples
    
    # 计算质量分数
    quality_score = 1.0
    quality_score -= quality_metrics['empty_count'] / total_samples * 0.5
    quality_score -= quality_metrics['short_count'] / total_samples * 0.3
    quality_score -= quality_metrics['quality_issues'] / total_samples * 0.4
    quality_score = max(0, quality_score)
    
    logger.info(f"质量分析结果:")
    logger.info(f"  总样本数: {total_samples}")
    logger.info(f"  平均长度: {quality_metrics['avg_length']:.1f} 字符")
    logger.info(f"  平均单词数: {quality_metrics['avg_words']:.1f}")
    logger.info(f"  空文本数: {quality_metrics['empty_count']}")
    logger.info(f"  过短文本数: {quality_metrics['short_count']}")
    logger.info(f"  质量问题数: {quality_metrics['quality_issues']}")
    logger.info(f"  质量分数: {quality_score:.3f} (0-1)")
    
    # 质量评估
    if quality_score >= 0.8:
        logger.info("  🎉 质量优秀!")
    elif quality_score >= 0.6:
        logger.info("  ✅ 质量良好!")
    elif quality_score >= 0.4:
        logger.info("  📈 质量一般")
    else:
        logger.info("  ⚠️ 质量需要改进")
    
    return {
        'quality_metrics': quality_metrics,
        'quality_score': quality_score,
        'total_samples': total_samples
    }

def save_results(results, quality_analysis, output_dir="improved_t5_test_results"):
    """保存测试结果"""
    logger = logging.getLogger(__name__)
    
    os.makedirs(output_dir, exist_ok=True)
    
    # 保存完整结果
    full_results = {
        'timestamp': datetime.now().isoformat(),
        'test_results': results,
        'quality_analysis': quality_analysis,
        'improvements': [
            "使用更精确的提示工程",
            "添加强化后处理逻辑",
            "实现质量检查和重试机制",
            "优化生成参数",
            "添加备用评论机制"
        ]
    }
    
    with open(os.path.join(output_dir, 'test_results.json'), 'w', encoding='utf-8') as f:
        json.dump(full_results, f, indent=2, ensure_ascii=False)
    
    # 保存样本示例
    with open(os.path.join(output_dir, 'sample_examples.txt'), 'w', encoding='utf-8') as f:
        f.write("改进后T5生成器样本示例\n")
        f.write("=" * 50 + "\n\n")
        
        for result in results:
            f.write(f"测试案例: {result['description']}\n")
            f.write(f"域: {result['domain']}, 评分: {result['rating']}\n")
            f.write("-" * 30 + "\n")
            
            for i, sample in enumerate(result['samples'], 1):
                f.write(f"样本 {i}:\n")
                f.write(f"  提示: {sample['prompt']}\n")
                f.write(f"  生成: {sample['text']}\n")
                f.write(f"  统计: {len(sample['text'])} 字符, {len(sample['text'].split())} 词\n\n")
            
            f.write("\n")
    
    logger.info(f"测试结果已保存到: {output_dir}")

def main():
    """主函数"""
    logger = setup_logging()
    
    logger.info("开始测试改进后的T5生成器")
    logger.info("=" * 60)
    logger.info("🔧 改进措施:")
    logger.info("   - 更精确的提示工程")
    logger.info("   - 强化后处理逻辑")
    logger.info("   - 质量检查和重试机制")
    logger.info("   - 优化生成参数")
    logger.info("   - 备用评论机制")
    
    try:
        # 1. 测试生成样本
        logger.info("\n步骤 1: 测试生成样本")
        results = test_generation_samples()
        
        # 2. 分析质量
        logger.info("\n步骤 2: 分析生成质量")
        quality_analysis = analyze_quality(results)
        
        # 3. 保存结果
        logger.info("\n步骤 3: 保存测试结果")
        save_results(results, quality_analysis)
        
        logger.info("\n改进后T5生成器测试完成!")
        logger.info("=" * 60)
        logger.info("🎯 测试总结:")
        logger.info(f"   质量分数: {quality_analysis['quality_score']:.3f}")
        logger.info(f"   总样本数: {quality_analysis['total_samples']}")
        logger.info(f"   平均长度: {quality_analysis['quality_metrics']['avg_length']:.1f} 字符")
        logger.info(f"   平均单词数: {quality_analysis['quality_metrics']['avg_words']:.1f}")
        
        # 与之前的结果对比
        previous_quality = 0.014  # 之前测试的相似度
        if quality_analysis['quality_score'] > 0.5:
            logger.info("   🎉 相比之前有显著改善!")
        elif quality_analysis['quality_score'] > 0.3:
            logger.info("   ✅ 质量有明显提升!")
        else:
            logger.info("   📈 仍有改进空间")
        
        return 0
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
