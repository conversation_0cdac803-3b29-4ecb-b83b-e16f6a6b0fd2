# D³AFD Multi-Algorithm Framework Requirements

# Core PyTorch and ML libraries
torch>=1.12.0
torchvision>=0.13.0
torchaudio>=0.12.0

# Transformers and NLP
transformers>=4.20.0
tokenizers>=0.12.0
datasets>=2.0.0
sentencepiece>=0.1.96

# Diffusion models support
diffusers>=0.15.0
accelerate>=0.18.0

# Scientific computing
numpy>=1.21.0
scipy>=1.8.0
scikit-learn>=1.1.0

# Data handling
pandas>=1.4.0
pyarrow>=8.0.0

# Visualization
matplotlib>=3.5.0
seaborn>=0.11.0
plotly>=5.8.0

# Configuration and utilities
pyyaml>=6.0
omegaconf>=2.2.0
hydra-core>=1.2.0

# Logging and monitoring
tensorboard>=2.9.0
wandb>=0.12.0
tqdm>=4.64.0

# Memory profiling
psutil>=5.9.0
py3nvml>=0.2.7

# Text processing
nltk>=3.7
spacy>=3.4.0

# Additional utilities
pathlib2>=2.3.7
click>=8.1.0
rich>=12.4.0

# Development and testing
pytest>=7.1.0
pytest-cov>=3.0.0
black>=22.3.0
flake8>=4.0.0
isort>=5.10.0

# Optional: For advanced features
# faiss-cpu>=1.7.2  # For similarity search
# redis>=4.3.0      # For caching
# ray>=2.0.0        # For distributed computing
