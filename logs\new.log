============================================================
Quick Test: DKDM Algorithm
============================================================
Starting quick test with dkdm...
2025-08-05 07:00:23 - d3afd_train - INFO - Starting D³AFD experiment with dkdm algorithm
2025-08-05 07:00:23 - d3afd_train - INFO - Output directory: outputs/quick_test_dkdm/dkdm_20250805_070023
2025-08-05 07:00:23 - d3afd_train - INFO - Loading Amazon Reviews dataset...
2025-08-05 07:00:23 - d3afd_train - INFO - Setting up 2 clients for domains: ['Electronics', 'Books']
Client client_0_Electronics: No local data provided or empty data (len: 0)
2025-08-05 07:00:24 - d3afd_train - INFO - Created client client_0_Electronics with 0 samples
Client client_1_Books: No local data provided or empty data (len: 0)
2025-08-05 07:00:25 - d3afd_train - INFO - Created client client_1_Books with 0 samples
2025-08-05 07:00:25 - d3afd_train - INFO - Initializing federated server with dkdm algorithm...
You are using the default legacy behaviour of the <class 'transformers.models.t5.tokenization_t5.T5Tokenizer'>. This is expected, and simply means that the `legacy` (previous) behavior will be used so nothing changes for you. If you want to use the new behaviour, set `legacy=False`. This should only be set if you understand what it means, and thoroughly read the reason why this was added as explained in 
2025-08-05 07:00:30 - src.utils.metrics - INFO - Metrics tracker initialized for dkdm algorithm
2025-08-05 07:00:30 - d3afd_train - INFO - Starting federated training...
2025-08-05 07:00:30 - d3afd_train - INFO - 
==================================================
2025-08-05 07:00:30 - d3afd_train - INFO - Round 1/3
2025-08-05 07:00:30 - d3afd_train - INFO - Algorithm: dkdm
2025-08-05 07:00:30 - d3afd_train - INFO - ==================================================
2025-08-05 07:00:30 - d3afd_train - INFO - Phase 1: Local training on clients...
2025-08-05 07:00:30 - d3afd_train - INFO - Training client client_0_Electronics...
Client client_0_Electronics: No model or data for training
2025-08-05 07:00:31 - d3afd_train - INFO - Training client client_1_Books...
Client client_1_Books: No model or data for training
2025-08-05 07:00:31 - d3afd_train - INFO - Phase 2: Global distillation...
客户端 client_0_Electronics 没有teacher_model，跳过域描述符生成
客户端 client_1_Books 没有teacher_model，跳过域描述符生成
Error in DKDM training batch 0: SentimentClassifier.forward() missing 1 required positional argument: 'attention_mask'
Error in DKDM training batch 1: SentimentClassifier.forward() missing 1 required positional argument: 'attention_mask'
Error in DKDM training batch 2: SentimentClassifier.forward() missing 1 required positional argument: 'attention_mask'
Error in DKDM training batch 3: SentimentClassifier.forward() missing 1 required positional argument: 'attention_mask'
Error in DKDM training batch 4: SentimentClassifier.forward() missing 1 required positional argument: 'attention_mask'
Error in DKDM training batch 5: SentimentClassifier.forward() missing 1 required positional argument: 'attention_mask'
Error in DKDM training batch 6: SentimentClassifier.forward() missing 1 required positional argument: 'attention_mask'
Error in DKDM training batch 7: SentimentClassifier.forward() missing 1 required positional argument: 'attention_mask'
Error in DKDM training batch 8: SentimentClassifier.forward() missing 1 required positional argument: 'attention_mask'
Error in DKDM training batch 9: SentimentClassifier.forward() missing 1 required positional argument: 'attention_mask'
Error in DKDM training batch 10: SentimentClassifier.forward() missing 1 required positional argument: 'attention_mask'
Error in DKDM training batch 11: SentimentClassifier.forward() missing 1 required positional argument: 'attention_mask'
Error in DKDM training batch 12: SentimentClassifier.forward() missing 1 required positional argument: 'attention_mask'
Error in DKDM training batch 13: SentimentClassifier.forward() missing 1 required positional argument: 'attention_mask'
Error in DKDM training batch 14: SentimentClassifier.forward() missing 1 required positional argument: 'attention_mask'
Error in DKDM training batch 15: SentimentClassifier.forward() missing 1 required positional argument: 'attention_mask'
Error in DKDM training batch 0: SentimentClassifier.forward() missing 1 required positional argument: 'attention_mask'
Error in DKDM training batch 1: SentimentClassifier.forward() missing 1 required positional argument: 'attention_mask'
Error in DKDM training batch 2: SentimentClassifier.forward() missing 1 required positional argument: 'attention_mask'
Error in DKDM training batch 3: SentimentClassifier.forward() missing 1 required positional argument: 'attention_mask'
Error in DKDM training batch 4: SentimentClassifier.forward() missing 1 required positional argument: 'attention_mask'
Error in DKDM training batch 5: SentimentClassifier.forward() missing 1 required positional argument: 'attention_mask'
Error in DKDM training batch 6: SentimentClassifier.forward() missing 1 required positional argument: 'attention_mask'
Error in DKDM training batch 7: SentimentClassifier.forward() missing 1 required positional argument: 'attention_mask'
Error in DKDM training batch 8: SentimentClassifier.forward() missing 1 required positional argument: 'attention_mask'
Error in DKDM training batch 9: SentimentClassifier.forward() missing 1 required positional argument: 'attention_mask'
Error in DKDM training batch 10: SentimentClassifier.forward() missing 1 required positional argument: 'attention_mask'
Error in DKDM training batch 11: SentimentClassifier.forward() missing 1 required positional argument: 'attention_mask'
Error in DKDM training batch 12: SentimentClassifier.forward() missing 1 required positional argument: 'attention_mask'
Error in DKDM training batch 13: SentimentClassifier.forward() missing 1 required positional argument: 