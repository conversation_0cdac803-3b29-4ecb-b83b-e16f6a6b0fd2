#!/usr/bin/env python3
"""
D³AFD Algorithm Comparison Script
Compare performance of DKDM, DiffDFKD, and DiffKD algorithms
"""

import os
import sys
import argparse
import logging
import yaml
import torch
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import json

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from train_d3afd_multi_algorithm import run_experiment, load_config, set_seed, setup_logger

def run_algorithm_comparison(config, algorithms, num_runs=3):
    """
    Run comparison experiments across multiple algorithms
    
    Args:
        config: Configuration object
        algorithms: List of algorithm names to compare
        num_runs: Number of runs per algorithm for statistical significance
    
    Returns:
        Dictionary containing results for each algorithm
    """
    results = {}
    
    for algorithm in algorithms:
        logger.info(f"\n{'='*60}")
        logger.info(f"Testing {algorithm.upper()} Algorithm")
        logger.info(f"{'='*60}")
        
        algorithm_results = []
        
        for run in range(num_runs):
            logger.info(f"\nRun {run + 1}/{num_runs} for {algorithm}")
            
            try:
                # Set different seed for each run
                set_seed(42 + run)
                
                # Run experiment
                final_metrics = run_experiment(config, algorithm)
                algorithm_results.append(final_metrics)
                
                logger.info(f"Run {run + 1} completed - Accuracy: {final_metrics.get('accuracy', 'N/A'):.4f}")
                
            except Exception as e:
                logger.error(f"Error in run {run + 1} for {algorithm}: {e}")
                algorithm_results.append(None)
        
        results[algorithm] = algorithm_results
    
    return results

def analyze_results(results):
    """
    Analyze and summarize comparison results
    
    Args:
        results: Dictionary of algorithm results
    
    Returns:
        DataFrame with statistical summary
    """
    summary_data = []
    
    for algorithm, runs in results.items():
        # Filter out failed runs
        valid_runs = [run for run in runs if run is not None]
        
        if not valid_runs:
            logger.warning(f"No valid runs for {algorithm}")
            continue
        
        # Extract metrics
        metrics = ['accuracy', 'f1_macro', 'f1_weighted', 'precision', 'recall']
        
        for metric in metrics:
            values = [run.get(metric, 0) for run in valid_runs]
            
            if values:
                summary_data.append({
                    'Algorithm': algorithm.upper(),
                    'Metric': metric,
                    'Mean': np.mean(values),
                    'Std': np.std(values),
                    'Min': np.min(values),
                    'Max': np.max(values),
                    'Runs': len(values)
                })
    
    return pd.DataFrame(summary_data)

def create_comparison_plots(summary_df, output_dir):
    """
    Create visualization plots for algorithm comparison
    
    Args:
        summary_df: DataFrame with summary statistics
        output_dir: Directory to save plots
    """
    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Set style
    plt.style.use('seaborn-v0_8')
    sns.set_palette("husl")
    
    # 1. Accuracy comparison
    plt.figure(figsize=(12, 6))
    
    accuracy_data = summary_df[summary_df['Metric'] == 'accuracy']
    
    plt.subplot(1, 2, 1)
    bars = plt.bar(accuracy_data['Algorithm'], accuracy_data['Mean'], 
                   yerr=accuracy_data['Std'], capsize=5, alpha=0.7)
    plt.title('Accuracy Comparison Across Algorithms')
    plt.ylabel('Accuracy')
    plt.xticks(rotation=45)
    
    # Add value labels on bars
    for bar, mean, std in zip(bars, accuracy_data['Mean'], accuracy_data['Std']):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + std + 0.01,
                f'{mean:.3f}±{std:.3f}', ha='center', va='bottom')
    
    # 2. F1-Macro comparison
    plt.subplot(1, 2, 2)
    f1_data = summary_df[summary_df['Metric'] == 'f1_macro']
    
    bars = plt.bar(f1_data['Algorithm'], f1_data['Mean'], 
                   yerr=f1_data['Std'], capsize=5, alpha=0.7)
    plt.title('F1-Macro Comparison Across Algorithms')
    plt.ylabel('F1-Macro Score')
    plt.xticks(rotation=45)
    
    # Add value labels on bars
    for bar, mean, std in zip(bars, f1_data['Mean'], f1_data['Std']):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + std + 0.01,
                f'{mean:.3f}±{std:.3f}', ha='center', va='bottom')
    
    plt.tight_layout()
    plt.savefig(output_dir / 'algorithm_comparison_main_metrics.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 3. Comprehensive metrics heatmap
    plt.figure(figsize=(10, 6))
    
    # Pivot data for heatmap
    heatmap_data = summary_df.pivot(index='Algorithm', columns='Metric', values='Mean')
    
    sns.heatmap(heatmap_data, annot=True, fmt='.3f', cmap='YlOrRd', 
                cbar_kws={'label': 'Score'})
    plt.title('Algorithm Performance Heatmap')
    plt.tight_layout()
    plt.savefig(output_dir / 'algorithm_performance_heatmap.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 4. Algorithm characteristics radar chart
    fig, axes = plt.subplots(2, 2, figsize=(12, 10), subplot_kw=dict(projection='polar'))
    axes = axes.flatten()
    
    algorithms = heatmap_data.index
    metrics = heatmap_data.columns
    
    for i, algorithm in enumerate(algorithms):
        if i >= len(axes):
            break
            
        ax = axes[i]
        values = heatmap_data.loc[algorithm].values
        
        # Close the plot
        values = np.concatenate((values, [values[0]]))
        angles = np.linspace(0, 2 * np.pi, len(metrics), endpoint=False).tolist()
        angles += angles[:1]
        
        ax.plot(angles, values, 'o-', linewidth=2, label=algorithm)
        ax.fill(angles, values, alpha=0.25)
        ax.set_xticks(angles[:-1])
        ax.set_xticklabels(metrics)
        ax.set_ylim(0, 1)
        ax.set_title(f'{algorithm} Performance Profile')
        ax.grid(True)
    
    plt.tight_layout()
    plt.savefig(output_dir / 'algorithm_radar_charts.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    logger.info(f"Comparison plots saved to {output_dir}")

def generate_report(summary_df, results, output_dir):
    """
    Generate a comprehensive comparison report
    
    Args:
        summary_df: DataFrame with summary statistics
        results: Raw results dictionary
        output_dir: Directory to save report
    """
    output_dir = Path(output_dir)
    report_path = output_dir / 'algorithm_comparison_report.md'
    
    with open(report_path, 'w') as f:
        f.write("# D³AFD Algorithm Comparison Report\n\n")
        f.write(f"Generated on: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        # Executive Summary
        f.write("## Executive Summary\n\n")
        
        # Find best performing algorithm for each metric
        for metric in ['accuracy', 'f1_macro', 'f1_weighted']:
            metric_data = summary_df[summary_df['Metric'] == metric]
            if not metric_data.empty:
                best_algo = metric_data.loc[metric_data['Mean'].idxmax(), 'Algorithm']
                best_score = metric_data['Mean'].max()
                f.write(f"- **Best {metric.replace('_', ' ').title()}**: {best_algo} ({best_score:.4f})\n")
        
        f.write("\n")
        
        # Algorithm Characteristics
        f.write("## Algorithm Characteristics\n\n")
        f.write("| Algorithm | Data-Free | Text Generation | Feature Distillation | Dynamic Iteration |\n")
        f.write("|-----------|-----------|-----------------|---------------------|-------------------|\n")
        f.write("| DKD       | ❌        | ❌              | ✅                  | ❌                |\n")
        f.write("| DKDM      | ✅        | ❌              | ✅                  | ✅                |\n")
        f.write("| DiffDFKD  | ✅        | ✅              | ❌                  | ❌                |\n")
        f.write("| DiffKD    | ❌        | ❌              | ✅                  | ❌                |\n\n")
        
        # Detailed Results
        f.write("## Detailed Results\n\n")
        f.write(summary_df.to_markdown(index=False))
        f.write("\n\n")
        
        # Algorithm Analysis
        f.write("## Algorithm Analysis\n\n")
        
        for algorithm in results.keys():
            f.write(f"### {algorithm.upper()}\n\n")
            
            valid_runs = [run for run in results[algorithm] if run is not None]
            if valid_runs:
                avg_accuracy = np.mean([run.get('accuracy', 0) for run in valid_runs])
                f.write(f"- **Average Accuracy**: {avg_accuracy:.4f}\n")
                f.write(f"- **Successful Runs**: {len(valid_runs)}/{len(results[algorithm])}\n")
                
                # Algorithm-specific insights
                if algorithm == 'dkdm':
                    f.write("- **Strengths**: Data-free distillation, dynamic iteration\n")
                    f.write("- **Use Case**: When original training data is unavailable\n")
                elif algorithm == 'diffdfkd':
                    f.write("- **Strengths**: T5-based synthetic data generation, multi-loss optimization\n")
                    f.write("- **Use Case**: When high-quality synthetic text data is needed\n")
                elif algorithm == 'diffkd':
                    f.write("- **Strengths**: Diffusion-based feature refinement\n")
                    f.write("- **Use Case**: When feature-level distillation is critical\n")
                elif algorithm == 'dkd':
                    f.write("- **Strengths**: Baseline knowledge distillation\n")
                    f.write("- **Use Case**: Standard federated learning scenarios\n")
            else:
                f.write("- **Status**: All runs failed\n")
            
            f.write("\n")
        
        # Recommendations
        f.write("## Recommendations\n\n")
        
        # Find overall best algorithm
        accuracy_data = summary_df[summary_df['Metric'] == 'accuracy']
        if not accuracy_data.empty:
            best_overall = accuracy_data.loc[accuracy_data['Mean'].idxmax(), 'Algorithm']
            f.write(f"1. **Overall Best Performance**: {best_overall}\n")
        
        f.write("2. **For Data-Free Scenarios**: Consider DKDM or DiffDFKD\n")
        f.write("3. **For Text Generation**: Use DiffDFKD with T5\n")
        f.write("4. **For Feature Learning**: Consider DiffKD\n")
        f.write("5. **For Baseline Comparison**: Use DKD\n\n")
    
    logger.info(f"Comparison report saved to {report_path}")

def main():
    parser = argparse.ArgumentParser(description="D³AFD Algorithm Comparison")
    parser.add_argument("--config", type=str, default="configs/d3afd_multi_algorithm.yaml",
                       help="Path to configuration file")
    parser.add_argument("--algorithms", nargs='+', 
                       choices=["dkd", "dkdm", "diffdfkd", "diffkd"],
                       default=["dkd", "dkdm", "diffdfkd", "diffkd"],
                       help="Algorithms to compare")
    parser.add_argument("--runs", type=int, default=3,
                       help="Number of runs per algorithm")
    parser.add_argument("--output_dir", type=str, default="outputs/algorithm_comparison",
                       help="Output directory for results")
    parser.add_argument("--seed", type=int, default=42,
                       help="Base random seed")
    
    args = parser.parse_args()
    
    # Load configuration
    config = load_config(args.config)
    
    # Setup logging
    global logger
    logger = setup_logger(config.experiment.log_level)
    
    # Create output directory
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    logger.info(f"Starting algorithm comparison with {len(args.algorithms)} algorithms")
    logger.info(f"Algorithms: {args.algorithms}")
    logger.info(f"Runs per algorithm: {args.runs}")
    logger.info(f"Output directory: {output_dir}")
    
    # Run comparison
    results = run_algorithm_comparison(config, args.algorithms, args.runs)
    
    # Analyze results
    summary_df = analyze_results(results)
    
    # Save raw results
    with open(output_dir / 'raw_results.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    # Save summary
    summary_df.to_csv(output_dir / 'summary_statistics.csv', index=False)
    
    # Create plots
    create_comparison_plots(summary_df, output_dir)
    
    # Generate report
    generate_report(summary_df, results, output_dir)
    
    # Print summary
    logger.info("\n" + "="*60)
    logger.info("ALGORITHM COMPARISON SUMMARY")
    logger.info("="*60)
    
    print(summary_df.to_string(index=False))
    
    logger.info(f"\nDetailed results saved to: {output_dir}")

if __name__ == "__main__":
    import numpy as np
    main()
