T5生成器样本示例
==================================================

样本 1:
域: Books
评分: 0
提示: Write a book review discussing has poor quality and doesn't work.
生成文本: This is the first book review of a novel, but it could be written out on the first page.
------------------------------

样本 2:
域: Books
评分: 0
提示: Write a book review discussing broke immediately after use.
生成文本: Write a book review discussing broken immediately after use. A review of your book, a review on a broken page. The post contains a list of books that you are reading.
------------------------------

样本 3:
域: Books
评分: 0
提示: Create a review for a novel that has poor quality and doesn't work.
生成文本: with poor quality and does not work. Create a review for a novel that has poor quality, doesn't work. "It's impossible to read"
------------------------------

样本 4:
域: Books
评分: 0
提示: Create a review for a novel that is a waste of money.
生成文本: <PERSON><PERSON>ez a  Create  Revision for neuer.
------------------------------

样本 5:
域: Books
评分: 0
提示: Generate feedback about a book with broke immediately after use.
生成文本: Generate feedback about a book with broken immediately after use. In this case, read the book and read it.The book is not a classic.
------------------------------

样本 6:
域: Books
评分: 0
提示: Generate feedback about a book with is terrible and disappointing.
生成文本: a book with is terrible and disappointing. For example, when you write your book, if you have a good idea of a product you can re-phrase the author.
------------------------------

样本 7:
域: Books
评分: 0
提示: Generate feedback about a book with is the worst product ever.
生成文本: Book is the worst product ever. Generate feedback about a book with is the lowest product ever! I’ve tried to find the best product.
------------------------------

样本 8:
域: Books
评分: 0
提示: Write a book review discussing is terrible and disappointing.
生成文本: Schreien Sie Sie schreiben Sie ein Buch Review Diskussion sind furcht und dezamorsing. Write a review of a book review review review discussing is terrible and disappointing.
------------------------------

样本 9:
域: Books
评分: 0
提示: Generate a review for a book about broke immediately after use.
生成文本: Generate read for oct. by gsw.
------------------------------

样本 10:
域: Books
评分: 0
提示: Create a review for a novel that is the worst product ever.
生成文本: Make a quick review for one novel that was the worst of all.
------------------------------

