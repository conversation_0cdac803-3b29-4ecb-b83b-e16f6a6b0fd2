# D³AFD Multi-Algorithm Configuration
# Supports DKDM, DiffDFKD, and DiffKD distillation algorithms

experiment:
  name: "d3afd_multi_algorithm_amazon_reviews"
  device: "cuda"
  seed: 42
  output_dir: "outputs/d3afd_multi_algorithm"
  log_level: "INFO"

# Dataset configuration for Amazon Reviews
data:
  dataset_name: "amazon_reviews"
  data_dir: "data/amazon_reviews"
  domains: ["Electronics", "Books", "Home_and_Kitchen", "Clothing_Shoes_and_Jewelry"]
  max_length: 512
  train_split: 0.8
  val_split: 0.1
  test_split: 0.1
  num_workers: 4

# Model configuration
model:
  model_name: "distilbert-base-uncased"
  num_labels: 3  # negative, neutral, positive
  hidden_size: 768
  dropout: 0.1
  freeze_backbone: false

# Training configuration
training:
  # Algorithm selection: "dkd", "dkdm", "diffdfkd", "diffkd"
  distillation_algorithm: "dkdm"  # Change this to experiment with different algorithms
  
  # General training parameters
  num_rounds: 10
  local_epochs: 3
  distillation_epochs: 5
  batch_size: 16
  distillation_batch_size: 32
  learning_rate: 2e-5
  distillation_lr: 1e-4
  weight_decay: 0.01
  optimizer_type: "adamw"
  
  # Pseudo data generation
  pseudo_samples_per_domain: 1000
  cache_refresh_interval: 3
  
  # Loss weights
  contrastive_weight: 0.1
  domain_weight: 0.05

# DKDM specific configuration
dkdm:
  num_timesteps: 1000
  batch_ratio: 0.4
  cache_dir: "cache/dkdm"
  distill_weight: 1.0

# DiffDFKD specific configuration  
diffdfkd:
  t5_model: "t5-base"
  guided_scale: 7.0
  inference_steps: 50
  oh_weight: 1.0      # One-hot loss weight
  bn_weight: 0.1      # BatchNorm regularization weight
  adv_weight: 1.0     # Adversarial loss weight

# DiffKD specific configuration
diffkd:
  inference_steps: 5
  num_train_timesteps: 1000
  use_ae: true        # Use autoencoder for teacher feature compression
  ae_channels: 384    # Autoencoder latent dimension
  tau: 1.0           # Temperature for knowledge distillation

# Federated learning configuration
federated:
  num_clients: 4
  clients_per_round: 4
  client_selection: "all"  # "random", "all", "weighted"
  
  # Client data distribution
  data_distribution: "domain_specific"  # Each client has one domain
  alpha: 0.5  # Dirichlet parameter for non-IID distribution

# Dynamic Domain Descriptor configuration
domain_descriptor:
  embedding_dim: 256
  num_prototypes: 10
  temperature: 0.1
  update_frequency: 1  # Update every N rounds

# Text generation configuration (for DiffDFKD)
text_generation:
  max_length: 150
  temperature: 0.8
  do_sample: true
  num_return_sequences: 1
  
  # Domain-specific generation prompts
  domain_prompts:
    Electronics:
      - "Generate a review for an electronic device:"
      - "Write a product review for electronics:"
      - "Create a customer review for a gadget:"
    Books:
      - "Generate a book review:"
      - "Write a review for a book:"
      - "Create a customer review for literature:"
    Home_and_Kitchen:
      - "Generate a review for a home appliance:"
      - "Write a review for kitchen equipment:"
      - "Create a customer review for household items:"
    Clothing_Shoes_and_Jewelry:
      - "Generate a review for clothing:"
      - "Write a review for fashion items:"
      - "Create a customer review for accessories:"

# Evaluation configuration
evaluation:
  metrics: ["accuracy", "f1_macro", "f1_weighted", "precision", "recall"]
  eval_frequency: 2  # Evaluate every N rounds
  save_best_model: true
  early_stopping_patience: 5

# Memory management
memory:
  enable_memory_management: true
  max_memory_usage: 0.8  # 80% of available GPU memory
  cleanup_frequency: 1   # Clean up every N batches
  force_cleanup_epochs: true

# Logging and monitoring
logging:
  log_frequency: 10  # Log every N batches
  save_logs: true
  tensorboard: true
  wandb:
    enabled: false
    project: "d3afd_multi_algorithm"
    entity: "your_wandb_entity"

# Reproducibility
reproducibility:
  deterministic: true
  benchmark: false
